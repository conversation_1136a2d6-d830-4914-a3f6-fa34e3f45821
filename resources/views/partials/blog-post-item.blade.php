<a href="{{ $permalink }}" class="post-item flex flex-col bg-[#F4F6FA] rounded-3xl overflow-hidden hover:scale-105 transition-all relative" data-categories="{{ implode(',', $category_slugs) }}">
    <div class="post-featured-image relative h-40">
        <div class="top-3 left-3 rounded-full backdrop-blur-md bg-white/45 px-3 pt-[2px] absolute">
            {{ $date }}
        </div>
        @if($has_thumbnail)
            <img class="h-full w-full object-cover" src="{{ $thumbnail_url }}" alt="{{ $title }}">
        @endif
    </div>
    <div class="pt-3 pb-5 px-5 h-full flex flex-col">
        @if(isset($categories) && count($categories))
            <div class="post-categories flex flex-wrap gap-2 mt-2">
                @foreach($categories as $category)
                    <span class="category bg-[#E8EBF3] text-[#566276] pt-0.5 px-3 rounded-full text-bodyExtraSmall whitespace-nowrap capitalize lg:text-bodyExtraSmallDesktop">
                        {{ $category->name }}
                    </span>
                @endforeach
            </div>
        @endif
        <div class="min-h-40 py-2 mt-4">
            <h2 class="text-bodyLarge mb-1 lg:text-bodyLargeDesktop">{{ $title }}</h2>
            <p class="text-bodyExtraSmall text-[#3F4A5A] lg:text-bodyExtraSmallDesktop">{!! $excerpt !!}</p>
        </div>
        <div class="post-author pt-4 flex items-center mt-auto border-t border-neutral-30">
            <img class="author-avatar w-10 h-10 rounded-full" src="{{ $author_avatar }}" alt="{{ $author_name }}">
            <span class="author-name text-gray-700 ml-2">{{ $author_name }}</span>
        </div>
    </div>
</a>
