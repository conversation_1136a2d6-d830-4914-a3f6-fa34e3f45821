import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
    InspectorControls
} from '@wordpress/block-editor';
import { PanelBody, SelectControl, FormTokenField, TextControl as WPTextControl } from '@wordpress/components';
import { useState } from '@wordpress/element';
import { commonAttributes } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor
} from './editor';

registerBlockType('sage/contact-form', {
    apiVersion: 2,
    title: 'Contact Form',
    icon: 'email',
    category: 'layout',
    attributes: {
        formType: {
            type: 'string',
            default: 'slider',
        },
        mainTitle: {
            type: 'string',
            default: '',
        },
        mainSubtitle: {
            type: 'string',
            default: '',
        },
        categories: {
            type: 'array',
            default: [],
        },
        items: {
            type: 'array',
            default: [{
                title: '',
                tagline: '',
                subtitle: '',
                description: '',
                image: ''
            }],
        },
        submissionEmail: {
            type: 'string',
            default: '',
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { formType, mainTitle, mainSubtitle, items, categories, submissionEmail } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        const handleItemChange = (index, field, value) => {
            const newItems = [...items];
            newItems[index][field] = value;
            setAttributes({ items: newItems });
        };

        const addItem = () => {
            const newItems = [...items, {
                title: '',
                tagline: '',
                subtitle: '',
                description: '',
                image: ''
            }];
            setAttributes({ items: newItems });
            setActiveTab(newItems.length - 1);
        };

        const removeItem = (index) => {
            const newItems = [...items];
            newItems.splice(index, 1);
            setAttributes({ items: newItems });
            setActiveTab(Math.max(activeTab - 1, 0));
        };

        return (
            <>
                <InspectorControls>
                    <PanelBody title="Form Settings">
                        <SelectControl
                            label="Form Type"
                            value={formType}
                            options={[
                                { label: 'Slider', value: 'slider' },
                                { label: 'Static', value: 'static' },
                            ]}
                            onChange={(newType) => setAttributes({ formType: newType })}
                        />
                    </PanelBody>
                    <PanelBody title="Email Settings">
                        <WPTextControl
                            label="Submission Email"
                            value={submissionEmail}
                            onChange={(newEmail) => setAttributes({ submissionEmail: newEmail })}
                            placeholder="Enter email for form submissions"
                        />
                    </PanelBody>
                </InspectorControls>

                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Contact Form</h2>
                        </div>
                    <TextControl
                        label="Main Title"
                        value={mainTitle}
                        onChange={(value) => setAttributes({ mainTitle: value })}
                        placeholder="Enter Main Title"
                        tagName="h2"
                    />

                    {formType === 'slider' && (
                        <TextControl
                            label="Main Subtitle"
                            value={mainSubtitle}
                            onChange={(value) => setAttributes({ mainSubtitle: value })}
                            placeholder="Enter Main Subtitle"
                            tagName="h4"
                        />
                    )}

                    <div className="mb-4">
                        <label className="block text-sm font-medium text-neutral-70 mb-2">Categories</label>
                        <div className="p-3 bg-white rounded-md border border-neutral-30">
                            <FormTokenField
                                value={categories}
                                onChange={(newCategories) => setAttributes({ categories: newCategories })}
                                placeholder="Add categories"
                                __nextHasNoMarginBottom
                            />
                        </div>
                        <p className="mt-1 text-xs text-neutral-60">Add categories for the contact form</p>
                    </div>

                    {formType === 'slider' && (
                        <div className="mb-6">
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-neutral-70 mb-2">Slides</label>
                                <div className="bg-neutral-20 rounded-md p-4">
                                    <TabNavigation
                                        items={items}
                                        activeTab={activeTab}
                                        onTabChange={setActiveTab}
                                        onAddItem={addItem}
                                        onRemoveItem={() => removeItem(activeTab)}
                                        getItemTitle={(_, index) => `Slide ${index + 1}`}
                                        itemName="Slide"
                                        addButtonTitle="Add New Slide"
                                    />

                                    {items[activeTab] && (
                                        <ItemEditor
                                            itemIndex={activeTab}
                                            onRemove={() => removeItem(activeTab)}
                                            itemName="Slide"
                                            showRemoveButton={items.length > 1}
                                        >
                                            <div className="mb-4">
                                                <TextControl
                                                    label="Slide Tagline"
                                                    value={items[activeTab].tagline}
                                                    onChange={(value) => handleItemChange(activeTab, 'tagline', value)}
                                                    placeholder="Enter slide tagline"
                                                    tagName="h5"
                                                />
                                            </div>

                                            <div className="mb-4">
                                                <TextControl
                                                    label="Slide Title"
                                                    value={items[activeTab].title}
                                                    onChange={(value) => handleItemChange(activeTab, 'title', value)}
                                                    placeholder="Enter slide title"
                                                    tagName="h3"
                                                />
                                            </div>

                                            <div className="mb-4">
                                                <TextControl
                                                    label="Slide Subtitle"
                                                    value={items[activeTab].subtitle}
                                                    onChange={(value) => handleItemChange(activeTab, 'subtitle', value)}
                                                    placeholder="Enter slide subtitle"
                                                    tagName="h4"
                                                />
                                            </div>

                                            <div className="mb-4">
                                                <TextControl
                                                    label="Slide Description"
                                                    value={items[activeTab].description}
                                                    onChange={(value) => handleItemChange(activeTab, 'description', value)}
                                                    placeholder="Enter slide description"
                                                    tagName="div"
                                                    multiline={true}
                                                />
                                            </div>

                                            <ImageUploader
                                                image={items[activeTab].image}
                                                onSelect={(url) => handleItemChange(activeTab, 'image', url)}
                                                onRemove={() => handleItemChange(activeTab, 'image', '')}
                                                label="Slide Image"
                                                description="Upload an image for this slide."
                                                height={48}
                                                objectFit="cover"
                                                altText="Slide Image"
                                            />
                                        </ItemEditor>
                                    )}
                                </div>
                                <p className="mt-1 text-xs text-neutral-60">Add slides for the contact form carousel.</p>
                            </div>
                        </div>
                    )}

                    {formType === 'static' && (
                        <div className="mb-6">
                            <div className="mb-4">
                                <label className="block text-sm font-medium text-neutral-70 mb-2">Static Image</label>
                                <div className="bg-neutral-20 rounded-md p-4">
                                    <div className="p-4 bg-white rounded-md border border-neutral-30">
                                        <ImageUploader
                                            image={items[0].image}
                                            onSelect={(url) => setAttributes({ items: [{ ...items[0], image: url }] })}
                                            onRemove={() => setAttributes({ items: [{ ...items[0], image: '' }] })}
                                            label="Form Image"
                                            description="Upload an image for the static form."
                                            height={48}
                                            objectFit="cover"
                                            altText="Contact Form Image"
                                        />
                                    </div>
                                </div>
                                <p className="mt-1 text-xs text-neutral-60">Upload an image to display with the static contact form.</p>
                            </div>
                        </div>
                    )}
                    </div>
                </div>
            </>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.mainTitle} />
                    <RichText.Content tagName="h4" value={attributes.mainSubtitle} />

                    <ul>
                        {attributes.categories?.map((category, index) => (
                            <li key={`category-${index}`}>
                                <RichText.Content tagName="span" value={category} />
                            </li>
                        ))}
                    </ul>

                    {attributes.formType === 'slider' &&
                        attributes.items?.map((item, index) => (
                            <div key={`contact-slide-${index}`}>
                                <RichText.Content tagName="h5" value={item.tagline} />
                                <RichText.Content tagName="h3" value={item.title} />
                                <RichText.Content tagName="h4" value={item.subtitle} />
                                <RichText.Content tagName="p" value={item.description} />

                                {item.image && (
                                    <img
                                        src={item.image}
                                        alt={item.title ? `${item.title} - Contact Slide Image` : "Contact Slide Image"}
                                    />
                                )}
                            </div>
                        ))}

                    {attributes.formType === 'static' && attributes.items?.[0]?.image && (
                        <img
                            src={attributes.items[0].image}
                            alt="Contact Form Static Image"
                        />
                    )}
                </div>
                <div data-dynamic="sage/contact-form"></div>
            </div>
        );
    }
});
