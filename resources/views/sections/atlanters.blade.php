<div class="atlanters-section relative flex justify-center items-center">
    <div class="container px-5 py-16">
        @if(!empty($attributes['tagline']))
            <x-title class="!text-[#0072A9] uppercase mb-2 font-medium" variation="tagline">
                {!! $attributes['tagline'] !!}
            </x-title>
        @endif
        @if(!empty($attributes['title']))
            <x-title class="mb-8 font-medium" highlight="dark" variation="large">
                {!! $attributes['title'] !!}
            </x-title>
        @endif
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($attributes['items'] as $item)
                <div class="flex flex-col h-64 rounded-xl overflow-hidden relative @if($item['type'] === 'atlanter') cursor-pointer atlanter-card @endif"
                     @if($item['type'] === 'atlanter' && isset($item['slug']))
                         data-slug="{{ $item['slug'] }}"
                     @endif>
                    @if($item['type'] === 'atlanter')
                        @if(!empty($item['featuredImage']))
                            <div class="absolute w-full h-full bg-cover bg-center"
                                 style="background-image: url('{{ $item['featuredImage'] }}');"></div>
                        @else
                            <div class="absolute w-full h-full bg-gray-300"></div>
                        @endif
                        <div class="flex flex-col justify-end z-10 text-white w-full h-full p-4">
                            <span class="text-bodyMedium font-normal lg:text-bodyMediumDesktop">
                                {{ $item['postTitle'] }}
                            </span>
                            @if(!empty($item['position']))
                                <span class="text-bodyExtraSmall lg:text-bodyExtraSmallDesktop">
                                    {{ $item['position'] }}
                                </span>
                            @endif
                        </div>
                        <div class="rounded-xl overflow-hidden absolute w-full h-full left-0 bottom-0 pointer-events-none bg-[linear-gradient(180deg,rgba(1,101,159,0)_55%,#042031_105%)]"></div>
                    @elseif($item['type'] === 'stat')
                        <div class="flex flex-col justify-end w-full h-full p-4" style="background: url('{{ asset('images/stat_bg.svg') }}'); background-size: cover;">
                            @if(!empty($item['image']))
                                @if(is_array($item['image']) && !empty($item['image']['url']))
                                    <img src="{{ $item['image']['url'] }}" alt="{{ $item['title'] }}" class="absolute left-0 top-0 w-full object-cover mb-2 w-full object-cover">
                                @elseif(is_string($item['image']))
                                    <img src="{{ $item['image'] }}" alt="{{ $item['title'] }}" class="absolute left-0 top-0 w-full object-cover mb-2 w-full object-cover">
                                @endif
                            @endif
                            <div class="flex justify-between items-center relative">
                                <x-title class="!font-medium" variation="{{ !empty($item['link']) ? 'small' : 'large' }}">
                                    {!! $item['title'] !!}
                                </x-title>
                                @if(!empty($item['link']))
                                    <a href="{{ $item['link'] }}" target="_blank" class="border border-[#3F4A5A] rounded-full p-4 h-4 w-4 flex items-center justify-center">
                                        <i class="fa-light fa-arrow-up-right"></i>
                                    </a>
                                @endif
                            </div>
                            <span class="{!! !empty($item['link']) ? 'text-bodyExtraSmall lg:text-bodyExtraSmallDesktop' : 'text-bodyLarge lg:text-bodyLargeDesktop' !!} relative font-normal">
                                {{ $item['description'] }}
                            </span>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</div>
<div id="atlanter-modal" class="hidden top-0 fixed inset-0 z-[1000] h-screen flex justify-center items-center bg-black bg-opacity-80 opacity-0 transform scale-95 transition-all duration-300">
    <div class="modal-content flex flex-col relative bg-white w-full h-full overflow-y-auto lg:flex-row">
        <div id="atlanter-post-content" class="flex flex-col justify-center items-center bg-[#F2F7FA] px-6 py-12 w-full lg:p-6 lg:w-1/2">
            <div class="w-full lg:max-w-lg">
                <div class="flex justify-between items-start mb-10">
                    <div class="flex flex-col">
                        <x-title id="atlanter-post-title" class="font-medium pt-3" highlight="dark" variation="large">
                        </x-title>
                        <x-title id="atlanter-position" class="text-transparent bg-clip-text bg-gradient-to-r from-neutral-100 to-neutral-80" variation="medium">
                        </x-title>
                    </div>
                    <button id="atlanter-modal-close" class="border border-[#3F4A5A] rounded-full mt-4 p-4 h-4 w-4 flex items-center justify-center lg:mt-5">
                        <i class="fa-light fa-xmark"></i>
                    </button>
                </div>
                <div id="atlanter-modal-content" class="w-full"></div>
            </div>
        </div>
        <div id="atlanter-modal-featured" class="w-full h-full lg:w-1/2">
            <img id="atlanter-featured-image" src="" alt="Atlanter Image" class="h-full w-full object-cover" style="display: none;">
        </div>
    </div>
</div>
