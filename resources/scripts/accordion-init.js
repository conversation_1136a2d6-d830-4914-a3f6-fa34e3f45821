document.addEventListener("DOMContentLoaded", () => {
    const toggles = document.querySelectorAll(".accordion-toggle");
    const tocLinks = document.querySelectorAll(".table-of-contents a");

    if (toggles.length === 0) return;

    const closeAllAccordions = () => {
        toggles.forEach(toggle => {
            const parent = toggle.closest(".accordion-item");
            const content = parent.querySelector(".toggle-hide");
            const chevron = toggle.querySelector(".fa-chevron-down");

            if (content && !content.classList.contains("hidden")) {
                content.style.maxHeight = content.scrollHeight + "px";
                requestAnimationFrame(() => {
                    content.style.transition = "max-height 0.3s ease";
                    content.style.maxHeight = "0";
                });

                if (chevron) chevron.style.transform = "rotate(0deg)";

                setTimeout(() => {
                    content.classList.add("hidden");
                    content.style.removeProperty("max-height");
                    content.style.removeProperty("transition");
                }, 300);
            }
        });
    };

    toggles.forEach(toggle => {
        toggle.addEventListener("click", () => {
            const parent = toggle.closest(".accordion-item");
            const content = parent.querySelector(".toggle-hide");
            const chevron = toggle.querySelector(".fa-chevron-down");

            if (!content) return;

            const isOpen = !content.classList.contains("hidden");

            if (isOpen) {
                content.style.maxHeight = content.scrollHeight + "px";
                requestAnimationFrame(() => {
                    content.style.transition = "max-height 0.3s ease";
                    content.style.maxHeight = "0";
                });

                if (chevron) chevron.style.transform = "rotate(0deg)";

                setTimeout(() => {
                    content.classList.add("hidden");
                    content.style.removeProperty("max-height");
                    content.style.removeProperty("transition");
                }, 300);
            } else {
                closeAllAccordions();
                content.classList.remove("hidden");
                content.style.maxHeight = "0";
                requestAnimationFrame(() => {
                    content.style.transition = "max-height 0.3s ease";
                    content.style.maxHeight = content.scrollHeight + "px";
                });

                if (chevron) chevron.style.transform = "rotate(180deg)";

                setTimeout(() => {
                    content.style.removeProperty("max-height");
                    content.style.removeProperty("transition");
                }, 300);
            }
        });
    });

    tocLinks.forEach(link => {
        link.addEventListener("click", (e) => {
            e.preventDefault();
            const targetId = link.getAttribute("href").substring(1);
            const targetAccordion = document.getElementById(targetId);
            const toggle = targetAccordion?.querySelector(".accordion-toggle");
            const content = targetAccordion?.querySelector(".toggle-hide");
    
            if (targetAccordion) {
                const observer = new IntersectionObserver((entries, obs) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            obs.disconnect();
                            setTimeout(() => {
                                closeAllAccordions();
                                toggle?.click();
                            }, 500);
                        }
                    });
                }, { threshold: 0.2 });
    
                const rect = targetAccordion.getBoundingClientRect();
                const offset = window.pageYOffset + rect.top - 80;
    
                window.scrollTo({
                    top: offset,
                    behavior: "smooth",
                });
    
                observer.observe(targetAccordion);
            }
        });
    });
    
});
