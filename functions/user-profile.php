<?php
/**
 * User Profile Extensions
 *
 * Adds custom fields to user profiles for internship testimonials and positions
 */

/**
 * Add custom fields to user profiles
 */
function add_user_profile_fields($user) {
    ?>
    <h3><?php _e('Internship Information', 'sage'); ?></h3>

    <table class="form-table">
        <tr>
            <th><label for="position"><?php _e('Position', 'sage'); ?></label></th>
            <td>
                <input type="text" name="position" id="position" value="<?php echo esc_attr(get_user_meta($user->ID, 'position', true)); ?>" class="regular-text" />
                <p class="description"><?php _e('User\'s current position or job title', 'sage'); ?></p>
            </td>
        </tr>
        <tr>
            <th><label for="internship_testimony"><?php _e('Internship Testimony', 'sage'); ?></label></th>
            <td>
                <?php
                $testimony = get_user_meta($user->ID, 'internship_testimony', true);
                wp_editor($testimony, 'internship_testimony', array(
                    'textarea_name' => 'internship_testimony',
                    'textarea_rows' => 5,
                    'media_buttons' => false,
                ));
                ?>
                <p class="description"><?php _e('User\'s testimony about their internship experience', 'sage'); ?></p>
            </td>
        </tr>
        <tr>
            <th><label for="mentor_feedback"><?php _e('Mentor Feedback', 'sage'); ?></label></th>
            <td>
                <?php
                $mentor_feedback = get_user_meta($user->ID, 'mentor_feedback', true);
                wp_editor($mentor_feedback, 'mentor_feedback', array(
                    'textarea_name' => 'mentor_feedback',
                    'textarea_rows' => 5,
                    'media_buttons' => false,
                ));
                ?>
                <p class="description"><?php _e('Feedback from the user\'s mentor', 'sage'); ?></p>
            </td>
        </tr>
        <tr>
            <th><label for="custom_profile_image"><?php _e('Custom Profile Image', 'sage'); ?></label></th>
            <td>
                <?php
                $image_id = get_user_meta($user->ID, 'custom_profile_image_id', true);
                $image_url = '';
                if ($image_id) {
                    $image_url = wp_get_attachment_url($image_id);
                }
                ?>
                <div class="custom-profile-image-container">
                    <?php if ($image_url) : ?>
                        <img src="<?php echo esc_url($image_url); ?>" alt="Profile Image" style="max-width: 150px; height: auto; margin-bottom: 10px; display: block;" />
                    <?php endif; ?>
                    <input type="hidden" name="custom_profile_image_id" id="custom_profile_image_id" value="<?php echo esc_attr($image_id); ?>" />
                    <button type="button" class="button" id="upload_profile_image_button"><?php _e('Upload Image', 'sage'); ?></button>
                    <?php if ($image_url) : ?>
                        <button type="button" class="button" id="remove_profile_image_button"><?php _e('Remove Image', 'sage'); ?></button>
                    <?php endif; ?>
                </div>
                <p class="description"><?php _e('Upload a custom profile image', 'sage'); ?></p>

                <script>
                jQuery(document).ready(function($) {
                    var mediaUploader;

                    $('#upload_profile_image_button').on('click', function(e) {
                        e.preventDefault();

                        if (mediaUploader) {
                            mediaUploader.open();
                            return;
                        }

                        mediaUploader = wp.media({
                            title: '<?php _e('Choose Profile Image', 'sage'); ?>',
                            button: {
                                text: '<?php _e('Set Profile Image', 'sage'); ?>'
                            },
                            multiple: false
                        });

                        mediaUploader.on('select', function() {
                            var attachment = mediaUploader.state().get('selection').first().toJSON();
                            $('#custom_profile_image_id').val(attachment.id);
                            $('.custom-profile-image-container img').remove();
                            $('.custom-profile-image-container').prepend('<img src="' + attachment.url + '" alt="Profile Image" style="max-width: 150px; height: auto; margin-bottom: 10px; display: block;" />');
                            $('#remove_profile_image_button').show();
                        });

                        mediaUploader.open();
                    });

                    $('#remove_profile_image_button').on('click', function(e) {
                        e.preventDefault();
                        $('#custom_profile_image_id').val('');
                        $('.custom-profile-image-container img').remove();
                        $(this).hide();
                    });
                });
                </script>
            </td>
        </tr>
    </table>
    <?php
}
add_action('show_user_profile', 'add_user_profile_fields');
add_action('edit_user_profile', 'add_user_profile_fields');

/**
 * Save custom user profile fields
 */
function save_user_profile_fields($user_id) {
    if (!current_user_can('edit_user', $user_id)) {
        return false;
    }

    if (isset($_POST['position'])) {
        update_user_meta($user_id, 'position', sanitize_text_field($_POST['position']));
    }

    if (isset($_POST['internship_testimony'])) {
        update_user_meta($user_id, 'internship_testimony', wp_kses_post($_POST['internship_testimony']));
    }

    if (isset($_POST['mentor_feedback'])) {
        update_user_meta($user_id, 'mentor_feedback', wp_kses_post($_POST['mentor_feedback']));
    }

    if (isset($_POST['custom_profile_image_id'])) {
        update_user_meta($user_id, 'custom_profile_image_id', absint($_POST['custom_profile_image_id']));
    }
}
add_action('personal_options_update', 'save_user_profile_fields');
add_action('edit_user_profile_update', 'save_user_profile_fields');

/**
 * Enqueue media scripts for profile image upload
 */
function enqueue_profile_image_scripts($hook) {
    if ($hook === 'profile.php' || $hook === 'user-edit.php') {
        wp_enqueue_media();
    }
}
add_action('admin_enqueue_scripts', 'enqueue_profile_image_scripts');

/**
 * Register user meta fields for REST API
 */
function register_user_meta_for_rest() {
    register_meta('user', 'position', [
        'type' => 'string',
        'single' => true,
        'show_in_rest' => true,
        'auth_callback' => function() {
            return current_user_can('edit_users');
        }
    ]);

    register_meta('user', 'internship_testimony', [
        'type' => 'string',
        'single' => true,
        'show_in_rest' => true,
        'auth_callback' => function() {
            return current_user_can('edit_users');
        }
    ]);

    register_meta('user', 'mentor_feedback', [
        'type' => 'string',
        'single' => true,
        'show_in_rest' => true,
        'auth_callback' => function() {
            return current_user_can('edit_users');
        }
    ]);

    register_meta('user', 'custom_profile_image_id', [
        'type' => 'integer',
        'single' => true,
        'show_in_rest' => true,
        'auth_callback' => function() {
            return current_user_can('edit_users');
        }
    ]);
}
add_action('init', 'register_user_meta_for_rest');
