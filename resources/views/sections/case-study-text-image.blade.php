@php
    $blockId = 'case-study-text-image-' . uniqid();
@endphp

<div id="{{ $blockId }}" class="case-studies-text-image relative flex justify-center items-center mb-8">
    <x-case-study-styles :accentColor="$attributes['accentColor']" blockClass="case-studies-text-image" :blockId="$blockId" />
    <div class="container px-5 py-10">
        <div class="relative max-w-3xl flex items-start gap-6 lg:gap-12">
            <div class="timeline after:w-1 after:h-full after:absolute after:left-[19px]">
                <div class="icon-container flex items-center justify-center w-[42px] h-[42px] rounded-full">
                    <img class="w-5 h-5 object-contain" src="{{ $attributes['icon'] }}" />
                </div>
            </div>
            <div>
                @if(!empty($attributes['title']))
                    <x-title class="mb-4" variation="medium">{!! $attributes['title'] !!}</x-title>
                @endif
                @if(!empty($attributes['description']))
                    <p class="mb-6">{!! $attributes['description'] !!}</p>
                @endif
                @if(!empty($attributes['image']))
                    <img src="{!! $attributes['image'] !!}" class="" />
                @endif
            </div>
        </div>
    </div>
</div>