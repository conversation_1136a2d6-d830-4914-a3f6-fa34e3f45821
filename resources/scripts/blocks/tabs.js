import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import { Panel, PanelBody } from '@wordpress/components';
import { useState } from '@wordpress/element';
import { commonAttributes } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    ButtonEditor
} from './editor';

registerBlockType('sage/tabs', {
    apiVersion: 2,
    title: 'Tabs',
    icon: 'index-card',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        tabs: {
            type: 'array',
            default: [
                {
                    title: '',
                    description: '',
                    buttonText: '',
                    buttonLink: '',
                    image: '',
                    tabButtonTitle: '',
                },
            ],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { title, description, tabs } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        const handleTabChange = (index, field, value) => {
            const newTabs = [...tabs];
            newTabs[index][field] = value;
            setAttributes({ tabs: newTabs });
        };

        const addTab = () => {
            const newTabs = [
                ...tabs,
                {
                    title: '',
                    description: '',
                    buttonText: '',
                    buttonLink: '',
                    image: '',
                    tabButtonTitle: '',
                },
            ];
            setAttributes({ tabs: newTabs });
            setActiveTab(newTabs.length - 1);
        };

        const removeTab = (index) => {
            const newTabs = [...tabs];
            newTabs.splice(index, 1);
            setAttributes({ tabs: newTabs });
            setActiveTab(Math.max(activeTab - 1, 0));
        };

        return (
            <div {...useBlockProps()}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Tabs</h2>
                    </div>
                    <TextControl
                        label="Main Title"
                        value={title}
                        onChange={(value) => setAttributes({ title: value })}
                        placeholder="Enter main title"
                        tagName="h2"
                    />
                    <TextControl
                        label="Main Description"
                        value={description}
                        onChange={(value) => setAttributes({ description: value })}
                        placeholder="Enter main description"
                        tagName="p"
                        multiline={true}
                    />

                    <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                        <Panel className="border-0 shadow-none">
                            <PanelBody title="Tabs" initialOpen={true}>

                                <TabNavigation
                                    items={tabs}
                                    activeTab={activeTab}
                                    onTabChange={setActiveTab}
                                    onAddItem={addTab}
                                    onRemoveItem={() => removeTab(activeTab)}
                                    getItemTitle={(tab, index) => tab.tabButtonTitle || `Tab ${index + 1}`}
                                    itemName="Tab"
                                    addButtonTitle="Add New Tab"
                                />

                                {tabs.length > 0 && (
                                    <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30 mt-4">
                                        <ItemEditor
                                            itemIndex={activeTab}
                                            onRemove={() => removeTab(activeTab)}
                                            itemName="Tab"
                                            showRemoveButton={tabs.length > 1}
                                        >
                                            <TextControl
                                                label="Tab Button Title"
                                                value={tabs[activeTab].tabButtonTitle}
                                                onChange={(value) => handleTabChange(activeTab, 'tabButtonTitle', value)}
                                                placeholder="Enter tab button title"
                                                tagName="h3"
                                            />

                                            <TextControl
                                                label="Tab Title"
                                                value={tabs[activeTab].title}
                                                onChange={(value) => handleTabChange(activeTab, 'title', value)}
                                                placeholder="Enter tab title"
                                                tagName="h3"
                                            />

                                            <TextControl
                                                label="Tab Description"
                                                value={tabs[activeTab].description}
                                                onChange={(value) => handleTabChange(activeTab, 'description', value)}
                                                placeholder="Enter tab description"
                                                tagName="p"
                                                multiline={true}
                                            />

                                            <ButtonEditor
                                                buttonText={tabs[activeTab].buttonText}
                                                buttonLink={tabs[activeTab].buttonLink}
                                                onChangeText={(value) => handleTabChange(activeTab, 'buttonText', value)}
                                                onChangeLink={(value) => handleTabChange(activeTab, 'buttonLink', value)}
                                                label="Tab Button"
                                            />

                                            <ImageUploader
                                                image={tabs[activeTab].image}
                                                onSelect={(url) => handleTabChange(activeTab, 'image', url)}
                                                onRemove={() => handleTabChange(activeTab, 'image', '')}
                                                label="Tab Image"
                                                description="Upload an image for this tab."
                                                height={48}
                                                objectFit="cover"
                                                altText="Tab Image"
                                            />
                                        </ItemEditor>
                                    </div>
                                )}
                            </PanelBody>
                        </Panel>
                    </div>
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" value={attributes.description} />

                    {attributes.tabs?.map((tab, index) => (
                        <div key={`tab-${index}`}>
                            <RichText.Content tagName="h3" value={tab.tabButtonTitle} />
                            <RichText.Content tagName="h3" value={tab.title} />
                            <RichText.Content tagName="p" value={tab.description} />
                            <RichText.Content tagName="span" value={tab.buttonText} />
                            {tab.buttonLink && (
                                <a href={tab.buttonLink} rel="noopener noreferrer">
                                    {tab.buttonText}
                                </a>
                            )}
                            {tab.image && (
                                <img
                                    src={tab.image}
                                    alt={`Tab Image ${index + 1}`}
                                />
                            )}
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/tabs"></div>
            </div>
        );
    }
});
