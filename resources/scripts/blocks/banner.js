import { registerBlockType } from '@wordpress/blocks';
import {
	RichText,
	BlockControls,
	AlignmentToolbar,
	useBlockProps,
} from '@wordpress/block-editor';
import { Fragment } from '@wordpress/element';
import { commonAttributes } from './common';
import {
	TextControl,
	ImageUploader
} from './editor';

registerBlockType('sage/banner', {
	apiVersion: 2,
	title: 'Banner',
	icon: 'format-image',
	category: 'layout',
	attributes: {
		...commonAttributes,
		backgroundImage: { type: 'string', default: '' },
		alignment: { type: 'string', default: 'left' },
	},
	edit: ({ attributes, setAttributes }) => {
		const { title, description, backgroundImage, alignment } = attributes;
		const blockProps = useBlockProps();

		return (
			<Fragment>
				<BlockControls>
					<AlignmentToolbar
						value={alignment}
						onChange={(newAlignment) => setAttributes({ alignment: newAlignment || 'left' })}
					/>
				</BlockControls>
				<div {...blockProps}>
					<div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
						<div className="mb-4 border-b border-neutral-30">
							<h2 className="text-2xl font-bold text-neutral-90">Banner</h2>
						</div>

						<ImageUploader
							image={backgroundImage}
							onSelect={(url) => setAttributes({ backgroundImage: url })}
							onRemove={() => setAttributes({ backgroundImage: '' })}
							label="Background Image"
							description="Upload a background image for the banner."
							height={48}
							objectFit="cover"
							altText="Banner Background"
						/>

						<TextControl
							label="Title"
							value={title}
							onChange={(value) => setAttributes({ title: value })}
							placeholder="Enter title"
							tagName="h2"
						/>

						<TextControl
							label="Description"
							value={description}
							onChange={(value) => setAttributes({ description: value })}
							placeholder="Enter description"
							tagName="p"
							multiline={true}
						/>

						<div className="mb-4">
							<label className="block text-sm font-medium text-neutral-70 mb-2">Text Alignment</label>
							<div className="p-3 bg-white rounded-md border border-neutral-30">
								<div className="flex gap-2">
									<button
										className={`pt-2 border-0 pb-1.5 px-4 rounded-md ${alignment === 'left' ? 'bg-primary-80 text-white' : 'cursor-pointer bg-neutral-20 text-neutral-70'}`}
										onClick={() => setAttributes({ alignment: 'left' })}
									>
										Left
									</button>
									<button
										className={`pt-2 border-0 pb-1.5 px-4 rounded-md ${alignment === 'center' ? 'bg-primary-80 text-white' : 'cursor-pointer bg-neutral-20 text-neutral-70'}`}
										onClick={() => setAttributes({ alignment: 'center' })}
									>
										Center
									</button>
									<button
										className={`pt-2 border-0 pb-1.5 px-4 rounded-md ${alignment === 'right' ? 'bg-primary-80 text-white' : 'cursor-pointer bg-neutral-20 text-neutral-70'}`}
										onClick={() => setAttributes({ alignment: 'right' })}
									>
										Right
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</Fragment>
		);
	},
	save: ({ attributes }) => {
		const blockProps = useBlockProps.save();
		return (
			<div {...blockProps}>
				<div style={{ display: 'none' }}>
					<RichText.Content tagName="h2" value={attributes.title} />
					<RichText.Content tagName="p" value={attributes.description} />
				</div>
				<div data-dynamic="sage/banner"></div>
			</div>
		);
	},
});
