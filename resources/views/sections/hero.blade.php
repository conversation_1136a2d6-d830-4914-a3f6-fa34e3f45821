<div class="hero-section flex justify-center items-start relative {{ ($attributes['imagePlacement'] ?? 'normal') === 'edge' ? 'm-auto' : '' }}">
    @if(($attributes['heroType'] ?? 'default') === 'default')
        <div class="flex flex-col p-5 gap-y-3 max-w-[1440px] items-stretch w-full lg:gap-y-0 lg:pl-0 lg:pr-6 lg:pt-0 lg:pb-16 lg:flex-row">
            <div class="relative rounded-3xl overflow-hidden flex flex-col items-start lg:w-[65.819%]">
                <div class="flex flex-col items-start max-w-[calc(100%_-_1.5rem)] lg:ml-6">
                    <div class="flex p-3 pt-6 pb-7 mb-0.5 gap-[18px] lg:gap-x-6 lg:p-12 lg:pb-16 lg:mb-2">
                        @if(!empty($attributes['topImages']))
                            @foreach($attributes['topImages'] as $topImage)
                                @if(!empty($topImage['image']))
                                    <img class="h-[18px] lg:h-6" src="{{ $topImage['image'] }}" />
                                @endif
                            @endforeach
                        @endif
                    </div>
                    <x-title variation="hero" class="px-3 lg:pl-12 lg:pr-36 mb-2">{!! $attributes['title'] ?? '' !!}</x-title>
                    <p class="px-3 mb-6 lg:mb-5 lg:pl-12 lg:max-w-3xl lg:pr-48">{!! $attributes['description'] ?? '' !!}</p>
                    @if(!empty($attributes['buttonText']))
                        <div class="flex items-center gap-4 ml-3 lg:ml-12 lg:mb-12">
                            <x-button type="link" variant="primary" size="medium" :href="$attributes['buttonLink'] ?? '#'">
                                {!! $attributes['buttonText'] !!}
                            </x-button>
                            @if(!empty($attributes['buttonCaption']))
                                <span class="text-bodyExtraSmall lg:text-bodyExtraSmallDesktop">{!! $attributes['buttonCaption'] !!}</span>
                            @endif
                        </div>
                    @endif
                    <div class="logos pt-3 pb-5 w-full overflow-hidden lg:min-h-[127px] lg:py-10 lg:w-[calc(100%_-_1.5rem)]" style="mask:linear-gradient(to right,rgba(0,0,0,0) 1%,rgba(0,0,0,1) 20%,rgba(0,0,0,1) 80%,rgba(0,0,0,0) 99%)">
                        <div class="carousel-logos flex gap-x-12">
                            @if(!empty($attributes['logos']))
                                @foreach($attributes['logos'] as $logo)
                                    @if(!empty($logo['image']))
                                        <div class="flex items-center flex-shrink-0">
                                            <img src="{{ $logo['image'] }}" alt="Logo Image" />
                                        </div>
                                    @endif
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>

                <x-floating-bg color="blue" />
            </div>

            <div class="flex rounded-3xl overflow-hidden relative lg:w-[34.181%]">
                @if(!empty($attributes['sliderItems']))
                    <div class="swiper-container hero-swiper flex flex-col justify-end w-full">
                        <div class="swiper-wrapper h-full">
                            @foreach($attributes['sliderItems'] as $slide)
                                <div class="swiper-slide pt-36 p-3 flex flex-col justify-end h-full relative lg:p-6">
                                    @if(!empty($slide['background']))
                                        <img class="rounded-3xl overflow-hidden object-cover w-full h-full absolute inset-0 -z-20" src="{{ $slide['background'] }}" />
                                    @endif
                                    <div class="relative z-10">
                                        <p class="text-white mb-4">{!! $slide['description'] ?? '' !!}</p>
                                        @if(!empty($slide['logo']) || !empty($slide['companyName']))
                                            <div class="flex items-center gap-2">
                                                @if(!empty($slide['logo']))
                                                    <img src="{{ $slide['logo'] }}" alt="Company Logo" />
                                                @endif
                                                @if(!empty($slide['companyName']))
                                                    <span class="text-white mt-1">{!! $slide['companyName'] !!}</span>
                                                @endif
                                            </div>
                                        @endif
                                    </div>
                                    <div class="rounded-3xl overflow-hidden absolute w-full h-full left-0 bottom-0 pointer-events-none -z-10 bg-[linear-gradient(180deg,rgba(1,101,159,0)_0%,#042031_100%)] lg:h-1/2"></div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @elseif(($attributes['heroType'] ?? 'default') === 'small')
        <div class="hero-small flex flex-col lg:flex-row items-center px-5 pt-18 {{ ($attributes['imagePlacement'] ?? 'normal') === 'edge' ? 'justify-center pb-0 lg:pb-18' : 'pb-18' }} lg:p-18 {{ ($attributes['imagePlacement'] ?? 'normal') === 'edge' ? 'w-full' : '' }} relative">
            <div class="px-5 lg:container">
            <div class="flex flex-col items-start z-10 relative {{ ($attributes['imagePlacement'] ?? 'normal') === 'edge' ? 'lg:min-w-[675px] min-h-0' : '' }}">
                @if(!empty($attributes['tagline']))
                    @php
                        $backgroundColors = [
                            'blue' => '#CCE9F6',
                            'green' => '#CCF6DD',
                            'peach' => '#F9D9D4',
                            'pink' => '#F6CCF5',
                        ];
                    @endphp
                    <span
                        class="text-bodyExtraSmallDesktop mb-5 rounded-full px-2 lg:mb-9"
                        style="background-color: {{ $backgroundColors[$attributes['backgroundColor']] ?? 'transparent' }}">
                        {!! $attributes['tagline'] !!}
                    </span>
                @endif
                <x-title variation="hero" class="max-w-2xl mb-2 lg:pr-4">{!! $attributes['title'] ?? '' !!}</x-title>
                <p class="max-w-3xl mb-8 lg:pr-5">{!! $attributes['description'] ?? '' !!}</p>
                @if(!empty($attributes['buttonText']))
                    <div class="flex items-center gap-4">
                        <x-button type="link" variant="primary" size="small" icon="link" :href="$attributes['buttonLink'] ?? '#'">
                            {!! $attributes['buttonText'] !!}
                        </x-button>
                        @if(!empty($attributes['buttonCaption']))
                            <span class="text-bodyExtraSmall text-[#6E7B91] lg:text-bodyExtraSmallDesktop">{!! $attributes['buttonCaption'] !!}</span>
                        @endif
                    </div>
                @endif

                @if(!empty($attributes['smallImage']) && ($attributes['imagePlacement'] ?? 'normal') === 'edge')
                    <div class="w-screen -mx-5 mt-8 block lg:hidden">
                        <img class="w-full h-auto object-cover"
                            style="mask-image: radial-gradient(circle at 90% 0%, transparent 10%, black 20%),
                                radial-gradient(circle at 25% 0%, transparent 5%, black 25%),
                                linear-gradient(to bottom, transparent, black 35%);
                                mask-composite: intersect;
                                -webkit-mask-image: radial-gradient(circle at 90% 0%, transparent 10%, black 20%),
                                radial-gradient(circle at 25% 0%, transparent 5%, black 25%),
                                linear-gradient(to bottom, transparent, black 35%);
                                -webkit-mask-composite: intersect;"
                            src="{{ $attributes['smallImage'] }}" />
                    </div>
                @endif
            </div>
            </div>
            @if(!empty($attributes['smallImage']) && ($attributes['imagePlacement'] ?? 'normal') === 'normal')
                <img class="blur-xs absolute z-0 object-cover h-5/6 w-5/6 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 lg:translate-x-0 lg:translate-y-0 lg:w-auto lg:static lg:blur-none lg:h-80" src="{{ $attributes['smallImage'] }}" />
            @endif
        </div>
        <x-floating-bg color="{{ $attributes['backgroundColor'] }}" />
        @if(!empty($attributes['smallImage']) && ($attributes['imagePlacement'] ?? 'normal') === 'edge')
            <img class="z-0 w-[45%] max-w-[400px] object-cover absolute right-0 bottom-0 h-80 hidden lg:block lg:h-full lg:right-[1.5rem] lg:rounded-3xl"
                style="mask-image:
                        radial-gradient(circle at 10% 0%, transparent 10%, black 20%),
                        radial-gradient(circle at 0% 90%, transparent 5%, black 25%),
                        linear-gradient(to right, transparent, black 35%);
                        mask-composite: intersect;
                        -webkit-mask-image:
                        radial-gradient(circle at 10% 0%, transparent 10%, black 20%),
                        radial-gradient(circle at 0% 90%, transparent 5%, black 25%),
                        linear-gradient(to right, transparent, black 35%);
                        -webkit-mask-composite: intersect;"
                src="{{ $attributes['smallImage'] }}" />
        @endif
    @endif
</div>