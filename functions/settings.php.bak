<?php
function register_contact_form_cpt() {
    $args = [
        'public'       => false,
        'show_ui'      => true,
        'show_in_menu' => false,
        'supports'     => ['title', 'editor'],
        'show_in_rest' => true,
    ];
    register_post_type('contact_form', $args);
}
add_action('init', 'register_contact_form_cpt');


add_action('admin_menu', function() {
    add_menu_page(
        'Theme Settings',
        'Theme Settings',
        'manage_options',
        'theme-settings',
        '__return_false',
        'dashicons-admin-generic',
        60
    );
    add_submenu_page(
        'theme-settings',
        'Contact Forms',
        'Contact Forms',
        'manage_options',
        'theme-settings-contact-forms',
        'contact_forms_page'
    );
    add_submenu_page(
        'theme-settings',
        'Technology Icons',
        'Technology Icons',
        'manage_options',
        'theme-settings-technology-icons',
        'technology_icons_page'
    );
    add_submenu_page(
        'theme-settings',
        'Positions',
        'Positions',
        'manage_options',
        'theme-settings-positions',
        'positions_page'
    );
    add_submenu_page(
        'theme-settings',
        'Case Study Icons',
        'Case Study Icons',
        'manage_options',
        'theme-settings-case-study-icons',
        'case_study_icons_page'
    );
});

function contact_forms_page() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST'
        && isset($_POST['default_contact_email'])
        && check_admin_referer('save_contact_email', 'contact_email_nonce')) {
         update_option('contact_form_email', sanitize_email($_POST['default_contact_email']));
         echo '<div class="updated"><p>Default contact email saved.</p></div>';
    }
    $default_email = get_option('contact_form_email', '');
    ?>
    <div class="wrap">
        <h1>Contact Forms</h1>
        <h2>Blog Post Contact Form</h2>
        <p>Edit the blog post contact form using the Gutenberg editor:</p>
        <p>
            <a href="<?php echo esc_url(get_edit_post_link(get_option('blog_post_contact_form_id'))); ?>" class="button button-primary">
                Edit Contact Form
            </a>
        </p>
        <h3>Default Submission Email</h3>
        <form method="post">
            <?php wp_nonce_field('save_contact_email', 'contact_email_nonce'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="default_contact_email">Default Submission Email</label>
                    </th>
                    <td>
                        <input name="default_contact_email" type="email" id="default_contact_email" value="<?php echo esc_attr($default_email); ?>" class="regular-text" />
                    </td>
                </tr>
            </table>
            <?php submit_button('Save Email'); ?>
        </form>
    </div>
    <?php
}

// Function to localize technology icons for use in JavaScript
function localize_technology_icons() {
    $tech_icons = get_option('technology_icons', []);

    // Localize for the block editor
    wp_localize_script('case-study-technologies', 'technologyIconsData', [
        'icons' => $tech_icons
    ]);

    // Also localize for the front-end
    wp_localize_script('case-study-technologies-init', 'technologyIconsData', [
        'icons' => $tech_icons
    ]);

    // Debug output to help troubleshoot
    if (is_admin() && isset($_GET['debug_tech_icons'])) {
        echo '<pre>Technology Icons: ' . esc_html(json_encode($tech_icons, JSON_PRETTY_PRINT)) . '</pre>';
    }

    // Direct script injection as a fallback
    if (is_admin()) {
        add_action('admin_footer', function() use ($tech_icons) {
            echo '<script type="text/javascript">
            window.technologyIconsData = window.technologyIconsData || {};
            window.technologyIconsData.icons = ' . json_encode($tech_icons) . ';
            console.log("Technology icons injected directly into the page:", window.technologyIconsData.icons);
            </script>';
        });
    }
}
add_action('enqueue_block_editor_assets', 'localize_technology_icons', 99);
add_action('wp_enqueue_scripts', 'localize_technology_icons', 99);

// Register REST API endpoint for technology icons
add_action('rest_api_init', function() {
    register_rest_route('wp/v2', '/settings/technology_icons', [
        'methods' => 'GET',
        'callback' => function() {
            return get_option('technology_icons', []);
        },
        'permission_callback' => function() {
            return current_user_can('edit_posts');
        }
    ]);
});

// Add technology icons data directly to the block editor
add_action('enqueue_block_editor_assets', function() {
    $tech_icons = get_option('technology_icons', []);

    wp_add_inline_script('wp-blocks', 'window.technologyIconsData = ' . json_encode(['icons' => $tech_icons]) . ';', 'before');
}, 20);

function technology_icons_page() {
    // Enqueue WordPress media scripts
    wp_enqueue_media();

    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle icon upload
        if (isset($_POST['action']) && $_POST['action'] === 'add_icon' && check_admin_referer('add_technology_icon', 'technology_icon_nonce')) {
            if (isset($_POST['icon_id']) && !empty($_POST['icon_id']) && isset($_POST['icon_name']) && !empty($_POST['icon_name'])) {
                $icon_id = intval($_POST['icon_id']);
                $icon_name = sanitize_text_field($_POST['icon_name']);

                // Get existing icons
                $tech_icons = get_option('technology_icons', []);

                // Add new icon
                $tech_icons[] = [
                    'id' => $icon_id,
                    'name' => $icon_name,
                    'url' => wp_get_attachment_url($icon_id),
                ];

                // Save updated icons
                update_option('technology_icons', $tech_icons);
                echo '<div class="updated"><p>Technology icon added successfully.</p></div>';
            }
        }

        // Handle icon removal
        if (isset($_POST['action']) && $_POST['action'] === 'remove_icon' && check_admin_referer('remove_technology_icon', 'remove_icon_nonce')) {
            if (isset($_POST['icon_index']) && is_numeric($_POST['icon_index'])) {
                $icon_index = intval($_POST['icon_index']);
                $tech_icons = get_option('technology_icons', []);

                if (isset($tech_icons[$icon_index])) {
                    // Remove the icon at the specified index
                    array_splice($tech_icons, $icon_index, 1);
                    update_option('technology_icons', $tech_icons);
                    echo '<div class="updated"><p>Technology icon removed successfully.</p></div>';
                }
            }
        }
    }

    // Get existing technology icons
    $tech_icons = get_option('technology_icons', []);
    ?>
    <div class="wrap">
        <h1>Technology Icons</h1>
        <p>Upload and manage technology icons that can be used in the Case Study Technologies block.</p>

        <h2>Add New Technology Icon</h2>
        <form method="post" class="add-technology-icon-form">
            <?php wp_nonce_field('add_technology_icon', 'technology_icon_nonce'); ?>
            <input type="hidden" name="action" value="add_icon">
            <input type="hidden" name="icon_id" id="technology_icon_id" value="">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="icon_name">Icon Name</label>
                    </th>
                    <td>
                        <input name="icon_name" type="text" id="icon_name" class="regular-text" required />
                        <p class="description">Enter a descriptive name for this technology icon.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label>Icon Image</label>
                    </th>
                    <td>
                        <div id="technology-icon-preview" style="display: none; margin-bottom: 10px;">
                            <img src="" alt="Icon Preview" style="max-width: 100px; max-height: 100px;">
                        </div>
                        <button type="button" class="button" id="upload_technology_icon_button">Select Icon</button>
                        <p class="description">Upload or select an image to use as a technology icon. Recommended size: 100x100px.</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Add Technology Icon', 'primary', 'submit', true, ['id' => 'add_technology_icon_submit', 'disabled' => 'disabled']); ?>
        </form>

        <h2>Existing Technology Icons</h2>
        <?php if (empty($tech_icons)) : ?>
            <p>No technology icons have been added yet.</p>
        <?php else : ?>
            <div class="technology-icons-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach ($tech_icons as $index => $icon) : ?>
                    <div class="technology-icon-item" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center;">
                        <img src="<?php echo esc_url($icon['url']); ?>" alt="<?php echo esc_attr($icon['name']); ?>" style="max-width: 80px; max-height: 80px; margin: 0 auto 10px;">
                        <p style="margin: 0; font-weight: bold;"><?php echo esc_html($icon['name']); ?></p>
                        <form method="post" style="margin-top: 10px;">
                            <?php wp_nonce_field('remove_technology_icon', 'remove_icon_nonce'); ?>
                            <input type="hidden" name="action" value="remove_icon">
                            <input type="hidden" name="icon_index" value="<?php echo $index; ?>">
                            <button type="submit" class="button button-small button-link-delete" onclick="return confirm('Are you sure you want to remove this icon?');">Remove</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
    jQuery(document).ready(function($) {
        var mediaUploader;

        $('#upload_technology_icon_button').click(function(e) {
            e.preventDefault();

            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            // Make sure we're using the wp.media correctly
            mediaUploader = window.wp.media({
                title: 'Select Technology Icon',
                button: {
                    text: 'Use this icon'
                },
                library: {
                    type: 'image'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#technology_icon_id').val(attachment.id);
                $('#technology-icon-preview').show().find('img').attr('src', attachment.url);
                $('#add_technology_icon_submit').prop('disabled', false);
            });

            mediaUploader.open();
        });

        // Enable/disable submit button based on form fields
        $('#icon_name').on('input', function() {
            checkFormValidity();
        });

        function checkFormValidity() {
            var iconId = $('#technology_icon_id').val();
            var iconName = $('#icon_name').val().trim();

            if (iconId && iconName) {
                $('#add_technology_icon_submit').prop('disabled', false);
            } else {
                $('#add_technology_icon_submit').prop('disabled', true);
            }
        }
    });
    </script>
    <?php
}

// Function to localize positions for use in JavaScript
function localize_positions() {
    $positions = get_option('positions', []);

    // Localize for the block editor
    wp_localize_script('case-study-timeline', 'positionsData', [
        'positions' => $positions
    ]);

    // Also localize for the front-end
    wp_localize_script('case-study-timeline-init', 'positionsData', [
        'positions' => $positions
    ]);

    // Debug output to help troubleshoot
    if (is_admin() && isset($_GET['debug_positions'])) {
        echo '<pre>Positions: ' . esc_html(json_encode($positions, JSON_PRETTY_PRINT)) . '</pre>';
    }

    // Add debug info to console
    if (is_admin()) {
        add_action('admin_footer', function() use ($positions) {
            echo '<script type="text/javascript">
            console.log("Positions data from PHP:", ' . json_encode($positions) . ');
            </script>';
        });
    }

    // Direct script injection as a fallback
    if (is_admin()) {
        add_action('admin_footer', function() use ($positions) {
            echo '<script type="text/javascript">
            window.positionsData = window.positionsData || {};
            window.positionsData.positions = ' . json_encode($positions) . ';
            console.log("Positions injected directly into the page:", window.positionsData.positions);
            </script>';
        });
    }
}
add_action('enqueue_block_editor_assets', 'localize_positions', 99);
add_action('wp_enqueue_scripts', 'localize_positions', 99);

// Register REST API endpoint for positions
add_action('rest_api_init', function() {
    register_rest_route('wp/v2', '/settings/positions', [
        'methods' => 'GET',
        'callback' => function() {
            return get_option('positions', []);
        },
        'permission_callback' => function() {
            return current_user_can('edit_posts');
        }
    ]);
});

// Add positions data directly to the block editor
add_action('enqueue_block_editor_assets', function() {
    $positions = get_option('positions', []);

    wp_add_inline_script('wp-blocks', 'window.positionsData = ' . json_encode(['positions' => $positions]) . ';', 'before');
}, 20);

// Function to localize case study icons for use in JavaScript
function localize_case_study_icons() {
    $case_study_icons = get_option('case_study_icons', []);

    // Localize for all case study blocks
    $case_study_blocks = [
        'case-study-features',
        'case-study-technologies',
        'case-study-text-image',
        'case-study-timeline',
        'case-study-outcomes'
    ];

    foreach ($case_study_blocks as $block) {
        wp_localize_script($block, 'caseStudyIconsData', [
            'icons' => $case_study_icons
        ]);
    }

    // Debug output to help troubleshoot
    if (is_admin() && isset($_GET['debug_case_study_icons'])) {
        echo '<pre>Case Study Icons: ' . esc_html(json_encode($case_study_icons, JSON_PRETTY_PRINT)) . '</pre>';
    }
}

add_action('enqueue_block_editor_assets', 'localize_case_study_icons', 99);
add_action('wp_enqueue_scripts', 'localize_case_study_icons', 99);

// Register REST API endpoint for case study icons
add_action('rest_api_init', function() {
    register_rest_route('wp/v2', '/settings/case_study_icons', [
        'methods' => 'GET',
        'callback' => function() {
            return get_option('case_study_icons', []);
        },
        'permission_callback' => function() {
            return current_user_can('edit_posts');
        }
    ]);
});

// Add case study icons data directly to the block editor
add_action('enqueue_block_editor_assets', function() {
    $case_study_icons = get_option('case_study_icons', []);

    wp_add_inline_script('wp-blocks', 'window.caseStudyIconsData = ' . json_encode(['icons' => $case_study_icons]) . ';', 'before');
}, 20);

function case_study_icons_page() {
    // Enqueue WordPress media scripts
    wp_enqueue_media();

    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle icon upload
        if (isset($_POST['action']) && $_POST['action'] === 'add_icon' && check_admin_referer('add_case_study_icon', 'case_study_icon_nonce')) {
            if (isset($_POST['icon_id']) && !empty($_POST['icon_id']) && isset($_POST['icon_name']) && !empty($_POST['icon_name']) && isset($_POST['icon_color']) && !empty($_POST['icon_color'])) {
                $icon_id = intval($_POST['icon_id']);
                $icon_name = sanitize_text_field($_POST['icon_name']);
                $icon_color = sanitize_text_field($_POST['icon_color']);

                // Get existing icons
                $case_study_icons = get_option('case_study_icons', []);

                // Add new icon
                $case_study_icons[] = [
                    'id' => $icon_id,
                    'name' => $icon_name,
                    'url' => wp_get_attachment_url($icon_id),
                    'color' => $icon_color,
                ];

                // Save updated icons
                update_option('case_study_icons', $case_study_icons);
                echo '<div class="updated"><p>Case study icon added successfully.</p></div>';
            }
        }

        // Handle icon removal
        if (isset($_POST['action']) && $_POST['action'] === 'remove_icon' && check_admin_referer('remove_case_study_icon', 'remove_icon_nonce')) {
            if (isset($_POST['icon_index']) && is_numeric($_POST['icon_index'])) {
                $icon_index = intval($_POST['icon_index']);
                $case_study_icons = get_option('case_study_icons', []);

                if (isset($case_study_icons[$icon_index])) {
                    // Remove the icon at the specified index
                    array_splice($case_study_icons, $icon_index, 1);
                    update_option('case_study_icons', $case_study_icons);
                    echo '<div class="updated"><p>Case study icon removed successfully.</p></div>';
                }
            }
        }
    }

    // Get existing case study icons
    $case_study_icons = get_option('case_study_icons', []);
    ?>
    <div class="wrap">
        <h1>Case Study Icons</h1>
        <p>Upload and manage icons that can be used in all Case Study blocks.</p>

        <h2>Add New Case Study Icon</h2>
        <form method="post" class="add-case-study-icon-form">
            <?php wp_nonce_field('add_case_study_icon', 'case_study_icon_nonce'); ?>
            <input type="hidden" name="action" value="add_icon">
            <input type="hidden" name="icon_id" id="case_study_icon_id" value="">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="icon_name">Icon Name</label>
                    </th>
                    <td>
                        <input name="icon_name" type="text" id="icon_name" class="regular-text" required />
                        <p class="description">Enter a descriptive name for this icon.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="icon_color">Icon Color</label>
                    </th>
                    <td>
                        <input name="icon_color" type="color" id="icon_color" value="#538564" required />
                        <p class="description">Choose a color for this icon. This will be used as the accent color in case study blocks.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label>Icon Image</label>
                    </th>
                    <td>
                        <div id="case-study-icon-preview" style="display: none; margin-bottom: 10px;">
                            <img src="" alt="Icon Preview" style="max-width: 100px; max-height: 100px;">
                        </div>
                        <button type="button" class="button" id="upload_case_study_icon_button">Select Icon</button>
                        <p class="description">Upload or select an image to use as an icon. Recommended size: 24x24px.</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Add Icon', 'primary', 'add_case_study_icon_submit', true, ['disabled' => 'disabled', 'id' => 'add_case_study_icon_submit']); ?>
        </form>

        <h2>Existing Case Study Icons</h2>
        <?php if (empty($case_study_icons)) : ?>
            <p>No case study icons have been added yet.</p>
        <?php else : ?>
            <div class="case-study-icons-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach ($case_study_icons as $index => $icon) : ?>
                    <div class="case-study-icon-item" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center;">
                        <div style="width: 42px; height: 42px; border-radius: 50%; background-color: <?php echo esc_attr($icon['color']); ?>; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                            <img src="<?php echo esc_url($icon['url']); ?>" alt="<?php echo esc_attr($icon['name']); ?>" style="max-width: 24px; max-height: 24px;">
                        </div>
                        <p style="margin: 0; font-weight: bold;"><?php echo esc_html($icon['name']); ?></p>
                        <p style="margin: 5px 0 10px; color: #666;"><?php echo esc_html($icon['color']); ?></p>
                        <form method="post" style="margin-top: 10px;">
                            <?php wp_nonce_field('remove_case_study_icon', 'remove_icon_nonce'); ?>
                            <input type="hidden" name="action" value="remove_icon">
                            <input type="hidden" name="icon_index" value="<?php echo $index; ?>">
                            <button type="submit" class="button button-small button-link-delete" onclick="return confirm('Are you sure you want to remove this icon?');">Remove</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
    jQuery(document).ready(function($) {
        var mediaUploader;

        $('#upload_case_study_icon_button').click(function(e) {
            e.preventDefault();

            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            // Make sure we're using the wp.media correctly
            mediaUploader = window.wp.media({
                title: 'Select Case Study Icon',
                button: {
                    text: 'Use this icon'
                },
                library: {
                    type: 'image'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#case_study_icon_id').val(attachment.id);
                $('#case-study-icon-preview').show().find('img').attr('src', attachment.url);
                $('#add_case_study_icon_submit').prop('disabled', false);
            });

            mediaUploader.open();
        });
    });
    </script>
    <?php
}

function positions_page() {
    // Enqueue WordPress media scripts
    wp_enqueue_media();

    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle position upload
        if (isset($_POST['action']) && $_POST['action'] === 'add_position' && check_admin_referer('add_position', 'position_nonce')) {
            if (isset($_POST['icon_id']) && !empty($_POST['icon_id']) && isset($_POST['position_name']) && !empty($_POST['position_name'])) {
                $icon_id = intval($_POST['icon_id']);
                $position_name = sanitize_text_field($_POST['position_name']);

                // Get existing positions
                $positions = get_option('positions', []);

                // Add new position
                $positions[] = [
                    'id' => $icon_id,
                    'name' => $position_name,
                    'url' => wp_get_attachment_url($icon_id),
                ];

                // Save updated positions
                update_option('positions', $positions);
                echo '<div class="updated"><p>Position added successfully.</p></div>';
            }
        }

        // Handle position removal
        if (isset($_POST['action']) && $_POST['action'] === 'remove_position' && check_admin_referer('remove_position', 'remove_position_nonce')) {
            if (isset($_POST['position_index']) && is_numeric($_POST['position_index'])) {
                $position_index = intval($_POST['position_index']);
                $positions = get_option('positions', []);

                if (isset($positions[$position_index])) {
                    // Remove the position at the specified index
                    array_splice($positions, $position_index, 1);
                    update_option('positions', $positions);
                    echo '<div class="updated"><p>Position removed successfully.</p></div>';
                }
            }
        }
    }

    // Get existing positions
    $positions = get_option('positions', []);
    ?>
    <div class="wrap">
        <h1>Positions</h1>
        <p>Upload and manage positions that can be used in the Case Study Timeline block.</p>

        <h2>Add New Position</h2>
        <form method="post" class="add-position-form">
            <?php wp_nonce_field('add_position', 'position_nonce'); ?>
            <input type="hidden" name="action" value="add_position">
            <input type="hidden" name="icon_id" id="position_icon_id" value="">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="position_name">Position Name</label>
                    </th>
                    <td>
                        <input name="position_name" type="text" id="position_name" class="regular-text" required />
                        <p class="description">Enter a descriptive name for this position.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label>Position Icon</label>
                    </th>
                    <td>
                        <div id="position-icon-preview" style="display: none; margin-bottom: 10px;">
                            <img src="" alt="Icon Preview" style="max-width: 100px; max-height: 100px;">
                        </div>
                        <button type="button" class="button" id="upload_position_icon_button">Select Icon</button>
                        <p class="description">Upload or select an image to use as a position icon. Recommended size: 24x24px.</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Add Position', 'primary', 'submit', true, ['id' => 'add_position_submit', 'disabled' => 'disabled']); ?>
        </form>

        <h2>Existing Positions</h2>
        <?php if (empty($positions)) : ?>
            <p>No positions have been added yet.</p>
        <?php else : ?>
            <div class="positions-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach ($positions as $index => $position) : ?>
                    <div class="position-item" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center;">
                        <img src="<?php echo esc_url($position['url']); ?>" alt="<?php echo esc_attr($position['name']); ?>" style="max-width: 80px; max-height: 80px; margin: 0 auto 10px;">
                        <p style="margin: 0; font-weight: bold;"><?php echo esc_html($position['name']); ?></p>
                        <form method="post" style="margin-top: 10px;">
                            <?php wp_nonce_field('remove_position', 'remove_position_nonce'); ?>
                            <input type="hidden" name="action" value="remove_position">
                            <input type="hidden" name="position_index" value="<?php echo $index; ?>">
                            <button type="submit" class="button button-small button-link-delete" onclick="return confirm('Are you sure you want to remove this position?');">Remove</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
    jQuery(document).ready(function($) {
        var mediaUploader;

        $('#upload_position_icon_button').click(function(e) {
            e.preventDefault();

            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            // Make sure we're using the wp.media correctly
            mediaUploader = window.wp.media({
                title: 'Select Position Icon',
                button: {
                    text: 'Use this icon'
                },
                library: {
                    type: 'image'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#position_icon_id').val(attachment.id);
                $('#position-icon-preview').show().find('img').attr('src', attachment.url);
                $('#add_position_submit').prop('disabled', false);
            });

            mediaUploader.open();
        });

        // Enable/disable submit button based on form fields
        $('#position_name').on('input', function() {
            checkFormValidity();
        });

        function checkFormValidity() {
            var iconId = $('#position_icon_id').val();
            var positionName = $('#position_name').val().trim();

            if (iconId && positionName) {
                $('#add_position_submit').prop('disabled', false);
            } else {
                $('#add_position_submit').prop('disabled', true);
            }
        }
    });
    </script>
    <?php
}
