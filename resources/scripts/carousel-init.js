import Swiper from 'swiper';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';

function setupInfiniteSlider(wrapperClass, sliderClass, reverse = false) {
    const wrapSlider = document.querySelector(`.${wrapperClass}`);
    if (!wrapSlider) return;

    const slider = wrapSlider.querySelector(`.${sliderClass}`);
    if (!slider) return;

    const sliderWidth = slider.offsetWidth;
    const clonedSlider = slider.cloneNode(true);
    wrapSlider.appendChild(clonedSlider);

    let pos = reverse ? -sliderWidth : sliderWidth;
    const clientWidth = document.documentElement.clientWidth;
    const threshold = reverse ? clientWidth + 24 : -sliderWidth - clientWidth - 24;
    const offset = sliderWidth - clientWidth - sliderWidth - 24;

    function animate() {
        pos += reverse ? 1 : -1;
        const updatedPosition = pos + (reverse ? clientWidth : -clientWidth);
        slider.style.transform = clonedSlider.style.transform = `translateX(${updatedPosition}px)`;

        if ((reverse && updatedPosition >= threshold) ||
        (!reverse && updatedPosition <= offset)) {
            pos = reverse ? -sliderWidth : sliderWidth;
        }

        requestAnimationFrame(animate);
    }

    animate();
}

setupInfiniteSlider('carousel-top', 'carousel-top-slider', false);
setupInfiniteSlider('carousel-bottom', 'carousel-bottom-slider', true);

function setupSmallCarousel() {
    const smallCarouselContainer = document.querySelector('.small-carousel');
    if (!smallCarouselContainer) return;

    let swiperInstance = null;

    function initializeSwiper() {
        if (window.innerWidth < 1024) {
            if (!swiperInstance) {
                swiperInstance = new Swiper(smallCarouselContainer, {
                    modules: [Autoplay],
                    loop: true,
                    freeMode: true,
                    spaceBetween: 32,
                    grabCursor: true,
                    slidesPerView: 3,
                    loop: true,
                    autoplay: {
                      delay: 1,
                      disableOnInteraction: true
                    },
                    freeMode: true,
                    speed: 5000,
                    freeModeMomentum: false
                });
            }
        } else if (swiperInstance) {
            swiperInstance.destroy(true, true);
            swiperInstance = null;
        }
    }

    initializeSwiper();
    window.addEventListener('resize', initializeSwiper);
}

setupSmallCarousel();

let swiperInstance = null;

function setupLargeCarousel() {
    const container = document.querySelector('.carousel-large-section .swiper');
    if (!container) return;

    swiperInstance = new Swiper(container, {
        slidesPerView: 'auto',
        spaceBetween: 24,
        speed: 400
    });

    window.addEventListener('resize', () => {
        if (swiperInstance) swiperInstance.update();
    });
}

setupLargeCarousel();

function setupPinnedScroll() {
    const wrapper = document.querySelector('.carousel-large-wrapper');
    if (!wrapper || !swiperInstance) return;

    const slides = document.querySelectorAll('.carousel-large-section .swiper-slide').length;
    if (!slides) return;

    let lastIndex = 0;

    function handleScroll() {
        if (window.innerWidth < 1024) return;

        const rect = wrapper.getBoundingClientRect();
        const wrapperHeight = wrapper.offsetHeight;
        const windowHeight = window.innerHeight;
        const offsetStart = windowHeight / 2;
        const scrolledInside = Math.max(0, -rect.top + offsetStart);
        const totalScroll = wrapperHeight - windowHeight;
        const clamped = Math.min(Math.max(scrolledInside, 0), totalScroll);
        const progress = clamped / totalScroll;
        const newIndex = Math.round(progress * (slides - 1));

        if (newIndex !== lastIndex) {
            swiperInstance.slideTo(newIndex);
            lastIndex = newIndex;
        }
    }

    window.addEventListener('scroll', handleScroll);

    window.addEventListener('resize', () => {
        if (window.innerWidth < 1024) {
            swiperInstance.slideTo(0);
        }
    });
}

setupPinnedScroll();
