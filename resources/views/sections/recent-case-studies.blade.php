@php
// Query the three latest case studies
$recent_case_studies = new WP_Query([
    'post_type' => 'case_study',
    'posts_per_page' => 3,
    'orderby' => 'date',
    'order' => 'DESC',
]);
@endphp

@if($recent_case_studies->have_posts())
    <div class="flex justify-center items-center">
        <div class="container flex flex-col items-start justify-between px-5 py-8 lg:flex-row">
            <div class="mb-4 lg:max-w-md lg:w-3/5 lg:pr-6 lg:mb-0">
                @if(!empty($attributes['title']))
                    <x-title class="mb-2" variation="large">{!! $attributes['title'] !!}</x-title>
                @endif
                @if(!empty($attributes['description']))
                    <p class="text-bodyLarge lg:text-bodyLargeDesktop">
                        {!! $attributes['description'] !!}
                    </p>
                @endif
            </div>
            <div>
                @if(!empty($attributes['buttonText']) && !empty($attributes['buttonLink']))
                    <x-button type="link" variant="secondary" :href="$attributes['buttonLink']">
                        {!! $attributes['buttonText'] !!}
                    </x-button>
                @endif
            </div>
        </div>
    </div>

    <div class="container px-5 py-8 mx-auto scroll-smooth">
        <div class="recent-case-studies grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            @while($recent_case_studies->have_posts())
                @php
                    $recent_case_studies->the_post();
                    $primary_industry = get_yoast_primary_term(get_the_ID(), 'industry');
                    $primary_service = get_yoast_primary_term(get_the_ID(), 'service');
                    $permalink = get_permalink();
                    $excerpt = has_excerpt() ? wp_strip_all_tags(get_the_excerpt()) : wp_trim_words(wp_strip_all_tags(get_the_content()), 20);
                @endphp
                <div class="post-item flex flex-col bg-[#F4F6FA] rounded-3xl overflow-hidden hover:scale-105 transition-all relative">
                    <a class="absolute left-0 top-0 w-full h-full z-10" href="{{ $permalink }}"></a>
                    <div class="post-featured-image relative h-40">
                        <div class="top-5 left-5 rounded-full backdrop-blur-md bg-white/45 px-3 pt-[2px] absolute">
                            {{ get_the_date('d.m.Y') }}
                        </div>
                        @if(has_post_thumbnail())
                            <img class="h-full w-full object-cover" src="{{ get_the_post_thumbnail_url(get_the_ID(), 'full') }}" alt="{{ get_the_title() }}">
                        @endif
                    </div>
                    <div class="p-5 px-5 h-full flex flex-col">
                        <div class="post-categories flex flex-wrap gap-2 mt-2">
                            @if($primary_industry)
                                <span class="tag bg-[#E8EBF3] pt-0.5 px-3 rounded-full text-bodyExtraSmall whitespace-nowrap capitalize lg:text-bodyExtraSmallDesktop">
                                    {!! $primary_industry->name !!}
                                </span>
                            @endif
                            @if($primary_service)
                                <span class="tag bg-[#E8EBF3] pt-0.5 px-3 rounded-full text-bodyExtraSmall whitespace-nowrap capitalize lg:text-bodyExtraSmallDesktop">
                                    {!! $primary_service->name !!}
                                </span>
                            @endif
                        </div>
                        <div class="py-2 mt-4">
                            <h2 class="text-bodyLarge mb-1 lg:text-bodyLargeDesktop">
                                <a href="{{ $permalink }}">{!! get_the_title() !!}</a>
                            </h2>
                            <div class="relative h-10 overflow-hidden text-bodyExtraSmall lg:text-bodyExtraSmallDesktop">
                                <p class="text-bodyExtraSmall h-14 text-[#3F4A5A] lg:text-bodyExtraSmallDesktop" style="-webkit-line-clamp:2;display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;">
                                    {!! $excerpt !!}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            @endwhile
            @php wp_reset_postdata(); @endphp
        </div>
    </div>
@endif
