<?php
/**
 * Positions Settings Page
 * 
 * Handles the positions settings page functionality.
 */

/**
 * Localize positions for use in JavaScript
 */
function localize_positions() {
    $positions = get_option('positions', []);

    // Localize for the block editor
    wp_localize_script('case-study-timeline', 'positionsData', [
        'positions' => $positions
    ]);

    // Also localize for the front-end
    wp_localize_script('case-study-timeline-init', 'positionsData', [
        'positions' => $positions
    ]);

    // Debug output to help troubleshoot
    if (is_admin() && isset($_GET['debug_positions'])) {
        echo '<pre>Positions: ' . esc_html(json_encode($positions, JSON_PRETTY_PRINT)) . '</pre>';
    }

    // Add debug info to console
    if (is_admin()) {
        add_action('admin_footer', function() use ($positions) {
            echo '<script type="text/javascript">
            console.log("Positions data from PHP:", ' . json_encode($positions) . ');
            </script>';
        });
    }

    // Direct script injection as a fallback
    if (is_admin()) {
        add_action('admin_footer', function() use ($positions) {
            echo '<script type="text/javascript">
            window.positionsData = window.positionsData || {};
            window.positionsData.positions = ' . json_encode($positions) . ';
            console.log("Positions injected directly into the page:", window.positionsData.positions);
            </script>';
        });
    }
}
add_action('enqueue_block_editor_assets', 'localize_positions', 99);
add_action('wp_enqueue_scripts', 'localize_positions', 99);

/**
 * Register REST API endpoint for positions
 */
add_action('rest_api_init', function() {
    register_rest_route('wp/v2', '/settings/positions', [
        'methods' => 'GET',
        'callback' => function() {
            return get_option('positions', []);
        },
        'permission_callback' => function() {
            return current_user_can('edit_posts');
        }
    ]);
});

/**
 * Add positions data directly to the block editor
 */
add_action('enqueue_block_editor_assets', function() {
    $positions = get_option('positions', []);

    wp_add_inline_script('wp-blocks', 'window.positionsData = ' . json_encode(['positions' => $positions]) . ';', 'before');
}, 20);

/**
 * Render Positions Settings Page
 */
function positions_page() {
    // Enqueue WordPress media scripts
    wp_enqueue_media();

    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle position upload
        if (isset($_POST['action']) && $_POST['action'] === 'add_position' && check_admin_referer('add_position', 'position_nonce')) {
            if (isset($_POST['icon_id']) && !empty($_POST['icon_id']) && isset($_POST['position_name']) && !empty($_POST['position_name'])) {
                $icon_id = intval($_POST['icon_id']);
                $position_name = sanitize_text_field($_POST['position_name']);

                // Get existing positions
                $positions = get_option('positions', []);

                // Add new position
                $positions[] = [
                    'id' => $icon_id,
                    'name' => $position_name,
                    'url' => wp_get_attachment_url($icon_id),
                ];

                // Save updated positions
                update_option('positions', $positions);
                echo '<div class="updated"><p>Position added successfully.</p></div>';
            }
        }

        // Handle position removal
        if (isset($_POST['action']) && $_POST['action'] === 'remove_position' && check_admin_referer('remove_position', 'remove_position_nonce')) {
            if (isset($_POST['position_index']) && is_numeric($_POST['position_index'])) {
                $position_index = intval($_POST['position_index']);
                $positions = get_option('positions', []);

                if (isset($positions[$position_index])) {
                    // Remove the position at the specified index
                    array_splice($positions, $position_index, 1);
                    update_option('positions', $positions);
                    echo '<div class="updated"><p>Position removed successfully.</p></div>';
                }
            }
        }
    }

    // Get existing positions
    $positions = get_option('positions', []);
    ?>
    <div class="wrap">
        <h1>Positions</h1>
        <p>Upload and manage positions that can be used in the Case Study Timeline block.</p>

        <h2>Add New Position</h2>
        <form method="post" class="add-position-form">
            <?php wp_nonce_field('add_position', 'position_nonce'); ?>
            <input type="hidden" name="action" value="add_position">
            <input type="hidden" name="icon_id" id="position_icon_id" value="">

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="position_name">Position Name</label>
                    </th>
                    <td>
                        <input name="position_name" type="text" id="position_name" class="regular-text" required />
                        <p class="description">Enter a descriptive name for this position.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label>Position Icon</label>
                    </th>
                    <td>
                        <div id="position-icon-preview" style="display: none; margin-bottom: 10px;">
                            <img src="" alt="Icon Preview" style="max-width: 100px; max-height: 100px;">
                        </div>
                        <button type="button" class="button" id="upload_position_icon_button">Select Icon</button>
                        <p class="description">Upload or select an image to use as a position icon. Recommended size: 24x24px.</p>
                    </td>
                </tr>
            </table>

            <?php submit_button('Add Position', 'primary', 'submit', true, ['id' => 'add_position_submit', 'disabled' => 'disabled']); ?>
        </form>

        <h2>Existing Positions</h2>
        <?php if (empty($positions)) : ?>
            <p>No positions have been added yet.</p>
        <?php else : ?>
            <div class="positions-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 20px; margin-top: 20px;">
                <?php foreach ($positions as $index => $position) : ?>
                    <div class="position-item" style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center;">
                        <img src="<?php echo esc_url($position['url']); ?>" alt="<?php echo esc_attr($position['name']); ?>" style="max-width: 80px; max-height: 80px; margin: 0 auto 10px;">
                        <p style="margin: 0; font-weight: bold;"><?php echo esc_html($position['name']); ?></p>
                        <form method="post" style="margin-top: 10px;">
                            <?php wp_nonce_field('remove_position', 'remove_position_nonce'); ?>
                            <input type="hidden" name="action" value="remove_position">
                            <input type="hidden" name="position_index" value="<?php echo $index; ?>">
                            <button type="submit" class="button button-small button-link-delete" onclick="return confirm('Are you sure you want to remove this position?');">Remove</button>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
    jQuery(document).ready(function($) {
        var mediaUploader;

        $('#upload_position_icon_button').click(function(e) {
            e.preventDefault();

            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            // Make sure we're using the wp.media correctly
            mediaUploader = window.wp.media({
                title: 'Select Position Icon',
                button: {
                    text: 'Use this icon'
                },
                library: {
                    type: 'image'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#position_icon_id').val(attachment.id);
                $('#position-icon-preview').show().find('img').attr('src', attachment.url);
                $('#add_position_submit').prop('disabled', false);
            });

            mediaUploader.open();
        });

        // Enable/disable submit button based on form fields
        $('#position_name').on('input', function() {
            checkFormValidity();
        });

        function checkFormValidity() {
            var iconId = $('#position_icon_id').val();
            var positionName = $('#position_name').val().trim();

            if (iconId && positionName) {
                $('#add_position_submit').prop('disabled', false);
            } else {
                $('#add_position_submit').prop('disabled', true);
            }
        }
    });
    </script>
    <?php
}
