@php
$menuName      = 'primary_navigation';
$menuLocations = get_nav_menu_locations();
$menuId        = $menuLocations[$menuName] ?? 0;
$allItems      = wp_get_nav_menu_items($menuId) ?? [];

$studies       = null;
$industriesParent = null;
$sliderParent  = null;
$buttonParent  = null;

foreach ($allItems as $item) {
    $type = get_post_meta($item->ID, '_menu_item_submenu_type', true);
    if ($item->menu_item_parent == 0 && $type === 'studies') {
        $studies = $item;
        break;
    }
}

if ($studies) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $studies->ID) {
            if ($child->title === 'Industries') {
                $industriesParent = $child;
            } elseif ($child->title === 'Slider') {
                $sliderParent = $child;
            } elseif ($child->title === 'Button') {
                $buttonParent = $child;
            }
        }
    }
}

$industriesItems = [];
if ($industriesParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $industriesParent->ID) {
            $industriesItems[] = $child;
        }
    }
}

$sliderItems = [];
if ($sliderParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $sliderParent->ID) {
            $sliderItems[] = $child;
        }
    }
}

$buttonItems = [];
if ($buttonParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $buttonParent->ID) {
            $buttonItems[] = $child;
        }
    }
}
@endphp

<div class="submenu w-full cursor-default top-2.5 flex flex-col gap-6 bg-white rounded-max lg:w-[729px] lg:p-4 lg:border lg:border-[#E8EBF3] lg:shadow-fade lg:gap-8 lg:absolute lg:left-1/2 lg:-translate-x-1/2 xl:left-0 xl:translate-x-0">
    <div class="flex flex-col gap-6 items-start justify-between w-full lg:gap-0 lg:flex-row">
        <div class="flex flex-col gap-2 lg:w-[465px] lg:gap-3">
            @if(!empty($industriesItems))
                @php
                    $indItem = $industriesItems[0];
                    $indTags = get_post_meta($indItem->ID, '_menu_item_tags', true);
                    $indTags = array_filter(explode(',', $indTags));
                @endphp
                <x-title variation="small">
                    {{ $indItem->title }}
                </x-title>
                <div class="text-bodySmall text-[#6E7B91] lg:text-bodySmallDesktop overflow-hidden lg:max-w-96">
                    @php
                        $formattedTags = [];
                        foreach($indTags as $tag) {
                            $formattedTags[] = trim($tag);
                        }
                        echo implode(', ', $formattedTags);
                    @endphp
                </div>
            @endif
        </div>
        @if(!empty($buttonItems))
            @php
                $btn = $buttonItems[0];
            @endphp
            <x-button type="link" variant="primary" size="small" icon="link" :href="$btn->url">
                {{ $btn->title }}
            </x-button>
        @endif
    </div>
    <div class="case-studies-slider swiper w-full">
        <div class="swiper-pagination flex items-center !top-6 !left-6 !h-2"></div>
        <div class="swiper-wrapper">
            @foreach($sliderItems as $slide)
                @php
                    $slideTitle = get_post_meta($slide->ID, '_menu_item_slide_title', true);
                    $slideImgId = get_post_meta($slide->ID, '_menu_item_image_id', true);
                    $slideImg   = wp_get_attachment_url($slideImgId);
                @endphp
                <div class="swiper-slide rounded-3xl overflow-hidden relative">
                    <img class="absolute right-0 top-0 -z-[10] w-full h-full object-cover" src="{{ $slideImg }}" />
                    <div class="flex flex-col items-start gap-4 p-6 pt-12" style="background: linear-gradient(270deg, rgba(1, 101, 159, 0) 0%, rgba(4, 32, 49, 0.9) 78.55%);">
                        <span class="text-bodyLarge text-white font-normal max-w-52 lg:text-bodyLargeDesktop">
                            {{ $slideTitle }}
                        </span>
                        <x-button type="link" variant="tertiary" size="extra-small" icon="link" :href="$slide->url">
                            {{ $slide->title }}
                        </x-button>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>
