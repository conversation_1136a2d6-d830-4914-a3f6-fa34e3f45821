import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import {
    Button,
    SelectControl,
    Panel,
    PanelBody
} from '@wordpress/components';
import { Fragment, useState } from '@wordpress/element';
import { commonAttributes } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor
} from './editor';

registerBlockType('sage/carousel', {
    apiVersion: 2,
    title: 'Carousel',
    icon: 'slides',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        variant: {
            type: 'string',
            default: 'default',
        },
        items: {
            type: 'array',
            default: [{
                title: '',
                image: '',
            }],
        },
        largeItems: {
            type: 'array',
            default: [{
                tagline: '',
                title: '',
                subtitle: '',
                image: '',
                listItems: [],
            }],
        },
        smallItems: {
            type: 'array',
            default: [{
                image: '',
            }],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { variant, items, largeItems, smallItems } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        const handleItemChange = (index, field, value, attrName) => {
            const newItems = [...attributes[attrName]];
            newItems[index][field] = value;
            setAttributes({ [attrName]: newItems });
        };

        const handleListChange = (index, subIndex, value) => {
            const updatedItems = [...largeItems];
            updatedItems[index].listItems[subIndex] = value;
            setAttributes({ largeItems: updatedItems });
        };

        const addListItem = (index) => {
            const updatedItems = [...largeItems];
            updatedItems[index].listItems.push('');
            setAttributes({ largeItems: updatedItems });
        };

        const removeListItem = (index, subIndex) => {
            const updatedItems = [...largeItems];
            updatedItems[index].listItems.splice(subIndex, 1);
            setAttributes({ largeItems: updatedItems });
        };

        const addItem = (attrName, newItem) => {
            const newItems = [...attributes[attrName], newItem];
            setAttributes({ [attrName]: newItems });
            setActiveTab(newItems.length - 1);
        };

        const removeItem = (index, attrName) => {
            const newItems = [...attributes[attrName]];
            newItems.splice(index, 1);
            setAttributes({ [attrName]: newItems });
            setActiveTab(Math.max(activeTab - 1, 0));
        };

        return (
            <div {...useBlockProps()}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Carousel</h2>
                    </div>
                    <div className="mb-6 pb-6 border-b border-neutral-30">
                        <h3 className="text-base font-medium mb-3">Carousel Settings</h3>
                        <SelectControl
                            label="Carousel Variant"
                            value={variant}
                            options={[
                                { label: 'Default', value: 'default' },
                                { label: 'Small', value: 'small' },
                                { label: 'Large', value: 'large' },
                            ]}
                            onChange={(val) => setAttributes({ variant: val })}
                            className="mb-2"
                        />
                        <p className="text-xs text-neutral-60 mb-2">Select the type of carousel you want to display.</p>
                    </div>

                    {variant === 'default' && (
                        <Fragment>
                            <TextControl
                                label="Block Title"
                                value={attributes.title}
                                onChange={(value) => setAttributes({ title: value })}
                                placeholder="Enter title"
                                tagName="h2"
                            />
                            <TextControl
                                label="Block Description"
                                value={attributes.description}
                                onChange={(value) => setAttributes({ description: value })}
                                placeholder="Enter description"
                                tagName="div"
                                multiline={true}
                            />

                            <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                                <Panel className="border-0 shadow-none">
                                    <PanelBody
                                        title="Carousel Items"
                                        initialOpen={true}
                                    >
                                        <p className="text-sm text-neutral-60 mb-4">Add items to display in the carousel.</p>

                                        <TabNavigation
                                            items={items}
                                            activeTab={activeTab}
                                            onTabChange={setActiveTab}
                                            onAddItem={() => addItem('items', { title: '', image: '' })}
                                            onRemoveItem={() => removeItem(activeTab, 'items')}
                                            getItemTitle={(item, index) => item.title && item.title.trim().length > 0 ? item.title : `Item ${index + 1}`}
                                            itemName="Item"
                                            addButtonTitle="Add New Item"
                                        />

                                        {items.length > 0 && (
                                            <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30">
                                                <ItemEditor
                                                    itemIndex={activeTab}
                                                    onRemove={() => removeItem(activeTab, 'items')}
                                                    itemName="Item"
                                                    showRemoveButton={items.length > 1}
                                                >
                                                    <TextControl
                                                        label="Item Title"
                                                        value={items[activeTab].title}
                                                        onChange={(value) => handleItemChange(activeTab, 'title', value, 'items')}
                                                        placeholder="Enter item title"
                                                        tagName="h3"
                                                    />

                                                    <ImageUploader
                                                        image={items[activeTab].image}
                                                        onSelect={(url) => handleItemChange(activeTab, 'image', url, 'items')}
                                                        onRemove={() => handleItemChange(activeTab, 'image', '', 'items')}
                                                        label="Item Image"
                                                        description="Upload an image for this carousel item."
                                                        height={48}
                                                        objectFit="cover"
                                                        altText="Carousel Image"
                                                    />
                                                </ItemEditor>
                                            </div>
                                        )}
                                    </PanelBody>
                                </Panel>
                            </div>
                        </Fragment>
                    )}

                    {variant === 'large' && (
                        <Fragment>
                            <TextControl
                                label="Block Title"
                                value={attributes.title}
                                onChange={(value) => setAttributes({ title: value })}
                                placeholder="Enter title"
                                tagName="h2"
                            />
                            <TextControl
                                label="Block Description"
                                value={attributes.description}
                                onChange={(value) => setAttributes({ description: value })}
                                placeholder="Enter description"
                                tagName="div"
                                multiline={true}
                            />

                            <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                                <Panel className="border-0 shadow-none">
                                    <PanelBody
                                        title="Large Carousel Items"
                                        initialOpen={true}
                                    >
                                        <p className="text-sm text-neutral-60 mb-4">Add items to display in the large carousel. Each item can have a tagline, title, subtitle, image, and list items.</p>

                                        <TabNavigation
                                            items={largeItems}
                                            activeTab={activeTab}
                                            onTabChange={setActiveTab}
                                            onAddItem={() => addItem('largeItems', { tagline: '', title: '', subtitle: '', image: '', listItems: [] })}
                                            onRemoveItem={() => removeItem(activeTab, 'largeItems')}
                                            getItemTitle={(item, index) => item.title && item.title.trim().length > 0 ? item.title : `Item ${index + 1}`}
                                            itemName="Item"
                                            addButtonTitle="Add New Item"
                                        />

                                        {largeItems.length > 0 && (
                                            <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30">
                                                <ItemEditor
                                                    itemIndex={activeTab}
                                                    onRemove={() => removeItem(activeTab, 'largeItems')}
                                                    itemName="Item"
                                                    showRemoveButton={largeItems.length > 1}
                                                >
                                                    <TextControl
                                                        label="Tagline"
                                                        value={largeItems[activeTab].tagline}
                                                        onChange={(value) => handleItemChange(activeTab, 'tagline', value, 'largeItems')}
                                                        placeholder="Enter tagline"
                                                        tagName="p"
                                                    />

                                                    <TextControl
                                                        label="Title"
                                                        value={largeItems[activeTab].title}
                                                        onChange={(value) => handleItemChange(activeTab, 'title', value, 'largeItems')}
                                                        placeholder="Enter title"
                                                        tagName="h3"
                                                    />

                                                    <TextControl
                                                        label="Subtitle"
                                                        value={largeItems[activeTab].subtitle}
                                                        onChange={(value) => handleItemChange(activeTab, 'subtitle', value, 'largeItems')}
                                                        placeholder="Enter subtitle"
                                                        tagName="p"
                                                    />

                                                    <ImageUploader
                                                        image={largeItems[activeTab].image}
                                                        onSelect={(url) => handleItemChange(activeTab, 'image', url, 'largeItems')}
                                                        onRemove={() => handleItemChange(activeTab, 'image', '', 'largeItems')}
                                                        label="Item Image"
                                                        description="Upload an image for this carousel item."
                                                        height={48}
                                                        objectFit="cover"
                                                        altText="Carousel Image"
                                                    />

                                                    <div className="mb-4">
                                                        <label className="block text-sm font-medium text-neutral-70 mb-2">List Items</label>
                                                        <div className="p-4 bg-neutral-10 rounded-lg border border-neutral-30">
                                                            {largeItems[activeTab].listItems.map((listItem, i) => (
                                                                <div key={i} className="flex items-center gap-2 mb-3 last:mb-0">
                                                                    <div className="flex-grow p-2 bg-white rounded-md border border-neutral-30">
                                                                        <RichText
                                                                            tagName="span"
                                                                            placeholder="Enter list item"
                                                                            value={listItem}
                                                                            onChange={(value) => handleListChange(activeTab, i, value)}
                                                                            className="text-sm"
                                                                            inlineToolbar
                                                                            allowedFormats={['core/bold']}
                                                                        />
                                                                    </div>
                                                                    <Button
                                                                        isDestructive
                                                                        className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm bg-red-500 text-white shadow-sm"
                                                                        onClick={() => removeListItem(activeTab, i)}
                                                                        icon="no-alt"
                                                                    >
                                                                    </Button>
                                                                </div>
                                                            ))}
                                                            <Button
                                                                onClick={() => addListItem(activeTab)}
                                                                className="w-full justify-center border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 py-2 rounded-md transition-all mt-3"
                                                                icon="plus"
                                                            >
                                                                Add List Item
                                                            </Button>
                                                        </div>
                                                        <p className="mt-2 text-xs text-neutral-60">Add list items that will appear with checkmarks.</p>
                                                    </div>
                                                </ItemEditor>
                                            </div>
                                        )}
                                    </PanelBody>
                                </Panel>
                            </div>
                        </Fragment>
                    )}

                    {variant === 'small' && (
                        <Fragment>
                            <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                                <Panel className="border-0 shadow-none">
                                    <PanelBody
                                        title="Small Carousel Items"
                                        initialOpen={true}
                                    >
                                        <p className="text-sm text-neutral-60 mb-4">Add small logo or image items to display in the carousel.</p>

                                        <TabNavigation
                                            items={smallItems}
                                            activeTab={activeTab}
                                            onTabChange={setActiveTab}
                                            onAddItem={() => addItem('smallItems', { image: '' })}
                                            onRemoveItem={() => removeItem(activeTab, 'smallItems')}
                                            getItemTitle={(_, index) => `Item ${index + 1}`}
                                            itemName="Item"
                                            addButtonTitle="Add New Item"
                                        />

                                        {smallItems.length > 0 && (
                                            <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30">
                                                <ItemEditor
                                                    itemIndex={activeTab}
                                                    onRemove={() => removeItem(activeTab, 'smallItems')}
                                                    itemName="Item"
                                                    showRemoveButton={smallItems.length > 1}
                                                >
                                                    <ImageUploader
                                                        image={smallItems[activeTab].image}
                                                        onSelect={(url) => handleItemChange(activeTab, 'image', url, 'smallItems')}
                                                        onRemove={() => handleItemChange(activeTab, 'image', '', 'smallItems')}
                                                        label="Logo/Image"
                                                        description="Upload a logo or small image for this carousel item."
                                                        height={32}
                                                        objectFit="contain"
                                                        altText="Carousel Small Image"
                                                        padding={4}
                                                    />
                                                </ItemEditor>
                                            </div>
                                        )}
                                    </PanelBody>
                                </Panel>
                            </div>
                        </Fragment>
                    )}
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" value={attributes.description} />

                    {attributes.items?.map((item, index) => (
                        <div key={`carousel-item-${index}`}>
                            <RichText.Content tagName="h3" value={item.title} />
                            {item.image && (
                                <img
                                    src={item.image}
                                    alt={item.title ? `${item.title} - Carousel Image` : "Carousel Image"}
                                />
                            )}
                        </div>
                    ))}

                    {attributes.largeItems?.map((item, index) => (
                        <div key={`carousel-large-item-${index}`}>
                            <RichText.Content tagName="h3" value={item.tagline} />
                            <RichText.Content tagName="h2" value={item.title} />
                            <RichText.Content tagName="p" value={item.subtitle} />
                            {item.image && (
                                <img
                                    src={item.image}
                                    alt={item.title ? `${item.title} - Large Carousel Image` : "Large Carousel Image"}
                                />
                            )}
                            <ul>
                                {item.listItems?.map((listItem, i) => (
                                    <li key={`large-list-${index}-${i}`}>
                                        <RichText.Content tagName="span" value={listItem} />
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}

                    {attributes.smallItems?.map((item, index) => (
                        <div key={`carousel-small-item-${index}`}>
                            {item.image && (
                                <img
                                    src={item.image}
                                    alt={`Small Carousel Image ${index + 1}`}
                                />
                            )}
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/carousel"></div>
            </div>
        );
    }
});
