import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import {
    Panel,
    PanelBody
} from '@wordpress/components';
import { commonAttributes } from './common';
import {
    BlockSettings,
    TextControl,
    ImageUploader
} from './editor';

registerBlockType('sage/case-study-text-image', {
    apiVersion: 2,
    title: 'Case Study: Text & Image',
    icon: 'format-image',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        image: {
            type: 'string',
        },
        icon: {
            type: 'string',
        },
        accentColor: {
            type: 'string',
        },
    },
    edit: ({ attributes, setAttributes }) => {
        // Get the block props with the default className
        const blockProps = useBlockProps();
        const { title, description, image, icon, accentColor } = attributes;

        return (
            <div {...blockProps}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Case Study: Text & Image</h2>
                    </div>
                    <BlockSettings
                        title={title}
                        onTitleChange={(val) => setAttributes({ title: val })}
                        icon={icon}
                        onIconSelect={(url) => setAttributes({ icon: url })}
                        onIconRemove={() => setAttributes({ icon: '' })}
                        accentColor={accentColor}
                        onAccentColorChange={(color) => setAttributes({ accentColor: color })}
                    />

                    <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                        <Panel className="border-0 shadow-none">
                            <PanelBody title="Content" initialOpen={true}>
                                <TextControl
                                    label="Description"
                                    value={description}
                                    onChange={(val) => setAttributes({ description: val })}
                                    placeholder="Enter description"
                                    tagName="div"
                                    multiline={true}
                                />

                                <ImageUploader
                                    image={image}
                                    onSelect={(url) => setAttributes({ image: url })}
                                    onRemove={() => setAttributes({ image: '' })}
                                    label="Featured Image"
                                    description="Upload a high-quality image that represents this case study section."
                                    height={48}
                                    objectFit="cover"
                                    altText="Case Study"
                                />
                            </PanelBody>
                        </Panel>
                    </div>
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        const { title, description, image, icon, accentColor } = attributes;

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }} data-accent-color={accentColor}>
                    <RichText.Content tagName="h2" value={title} />
                    <RichText.Content tagName="p" value={description} />
                    {icon && <img src={icon} alt="Case Study Icon" />}
                    {image && <img src={image} alt="Case Study" />}
                </div>
                <div data-dynamic="sage/case-study-text-image"></div>
            </div>
        );
    },
});
