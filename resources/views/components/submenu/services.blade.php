@php
$menuName = 'primary_navigation';
$menuLocations = get_nav_menu_locations();
$menuId = $menuLocations[$menuName] ?? 0;
$allItems = wp_get_nav_menu_items($menuId) ?? [];
$services = null;
$itemsParent = null;
$ctaWideParent = null;
$ctaNarrowParent = null;

foreach ($allItems as $item) {
    if ($item->menu_item_parent == 0 && $item->title === 'Services') {
        $services = $item;
        break;
    }
}

if ($services) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $services->ID) {
            if ($child->title === 'Items') {
                $itemsParent = $child;
            } elseif ($child->title === 'CTA Wide') {
                $ctaWideParent = $child;
            } elseif ($child->title === 'CTA Narrow') {
                $ctaNarrowParent = $child;
            }
        }
    }
}

$items = [];
if ($itemsParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $itemsParent->ID) {
            $items[] = $child;
        }
    }
}

$ctaWide = [];
if ($ctaWideParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $ctaWideParent->ID) {
            $ctaWide[] = $child;
        }
    }
}

$ctaNarrow = [];
if ($ctaNarrowParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $ctaNarrowParent->ID) {
            $ctaNarrow[] = $child;
        }
    }
}
@endphp

<div class="submenu w-full cursor-default top-2.5 flex flex-col gap-6 bg-white rounded-max lg:p-4 lg:border lg:border-[#E8EBF3] lg:shadow-fade lg:gap-4 lg:w-fit lg:flex-row lg:absolute lg:left-1/2 lg:-translate-x-1/2 xl:left-0 xl:translate-x-0">
    <div class="flex flex-col gap-6 sm:gap-2 lg:gap-8">
        <ul class="flex flex-col columns-2 gap-2 sm:block lg:w-[792px]">
            @foreach($items as $item)
                @php
                $imageId = get_post_meta($item->ID, '_menu_item_image_id', true);
                $image = $imageId ? wp_get_attachment_url($imageId) : '';
                $tagsStr = get_post_meta($item->ID, '_menu_item_tags', true);
                $tags = array_filter(explode(',', $tagsStr));
                @endphp
                <li class="mb-4 last:mb-0 group w-full overflow-hidden bg-white transition-all hover:bg-[#F4F6FA] lg:rounded-2xl lg:mb-0">
                    <a class="flex items-start lg:p-3" href="{{ $item->url }}">
                        <img class="h-[52px] w-[52px] mr-2" src="{{ $image }}" />
                        <div class="flex flex-col">
                            <span class="text-bodyMedium lg:text-bodyMediumDesktop">
                                {{ $item->title }}
                            </span>
                            <div class="flex flex-wrap gap-1">
                                @foreach($tags as $tag)
                                    <span class="tag bg-[#E8EBF3] text-[#566276] pt-0.5 px-2 rounded-full text-bodyExtraSmall whitespace-nowrap capitalize lg:text-bodyExtraSmallDesktop">
                                        {{ $tag }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                        <i class="fa-light fa-arrow-up-right w-[9px] h-3.5 px-[5.5px] py-[3px] self-center transition-all text-bodyExtraSmall text-[#ACB6C8] ml-auto group-hover:text-[#303945]"></i>
                    </a>
                </li>
            @endforeach
        </ul>

        <div class="flex flex-col gap-6 lg:gap-2 lg:flex-row">
            @foreach($ctaNarrow as $cta)
                @php
                    $ctaTitle = get_post_meta($cta->ID, '_menu_item_cta_narrow_title', true);
                    $description = $cta->description;
                    $buttonText = $cta->title;
                    $buttonLink = $cta->url;
                    $imageId = get_post_meta($cta->ID, '_menu_item_image_id', true);
                    $bgImage = wp_get_attachment_url($imageId) ?: '';
                @endphp
                <div class="flex bg-[#F4F6FA] w-full relative flex flex-col items-start rounded-3xl overflow-hidden p-6 lg:w-1/2 xl:hidden">
                    <span class="text-bodyLarge text-[#222831] z-10 font-normal lg:text-bodyLargeDesktop">
                        {{ $ctaTitle }}
                    </span>
                    <span class="text-bodySmall mb-4 text-[#222831] z-10 lg:text-bodySmallDesktop">
                        {{ $description }}
                    </span>
                    <x-button type="link" variant="secondary" size="extra-small" icon="link" :href="$buttonLink">
                        {{ $buttonText }}
                    </x-button>
                    <img class="absolute right-0 top-0 w-1/2 h-full object-cover cutout-fade" src="{{ $bgImage }}" />
                </div>
            @endforeach
            @foreach($ctaWide as $cta)
                @php
                    $ctaTitle = get_post_meta($cta->ID, '_menu_item_cta_wide_title', true);
                    $description = $cta->description;
                    $buttonText = $cta->title;
                    $buttonLink = $cta->url;
                    $imageId = get_post_meta($cta->ID, '_menu_item_image_id', true);
                    $bgImage = wp_get_attachment_url($imageId) ?: '';
                @endphp
                <div class="w-full relative flex flex-col items-start rounded-3xl overflow-hidden p-6 lg:w-1/2 xl:w-full" style="background: linear-gradient(90deg, #001D2A 0.37%, #3F4A5A 100.37%);">
                    <span class="text-bodyLarge z-10 text-white font-normal lg:text-bodyLargeDesktop">
                        {{ $ctaTitle }}
                    </span>
                    <span class="text-bodySmall z-10 mb-4 text-white lg:text-bodySmallDesktop">
                        {{ $description }}
                    </span>
                    <x-button type="link" variant="tertiary" size="extra-small" icon="link" :href="$buttonLink">
                        {{ $buttonText }}
                    </x-button>
                    <img class="absolute right-0 top-0 w-1/2 h-full object-cover cutout-fade xl:mask-none" src="{{ $bgImage }}" />
                </div>
            @endforeach
        </div>
    </div>

    @foreach($ctaNarrow as $cta)
        <div class="hidden flex-col justify-between min-w-[266px] bg-[#F4F6FA] w-[266px] rounded-3xl overflow-hidden lg:flex lg:hidden xl:flex">
            @php
                $ctaTitle = get_post_meta($cta->ID, '_menu_item_cta_narrow_title', true);
                $description = $cta->description;
                $buttonText = $cta->title;
                $buttonLink = $cta->url;
                $imageId = get_post_meta($cta->ID, '_menu_item_image_id', true);
                $bgImage = wp_get_attachment_url($imageId) ?: '';
            @endphp
            <img class="h-[188px]" src="{{ $bgImage }}" style="mask:url('{{ asset('images/ellipse-fade.svg') }}')" />
            <div class="px-6 pb-6 flex flex-col items-start">
                <span class="text-bodyLarge text-[#222831] font-normal lg:text-bodyLargeDesktop">
                    {{ $ctaTitle }}
                </span>
                <span class="text-bodySmall mb-4 text-[#222831] lg:text-bodySmallDesktop">
                    {{ $description }}
                </span>
                <x-button type="link" variant="secondary" size="extra-small" icon="link" :href="$buttonLink">
                    {{ $buttonText }}
                </x-button>
            </div>
        </div>
    @endforeach
</div>
