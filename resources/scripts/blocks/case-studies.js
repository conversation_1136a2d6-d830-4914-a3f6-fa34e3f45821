import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps
} from '@wordpress/block-editor';
import { Panel, PanelBody } from '@wordpress/components';
import { useState } from '@wordpress/element';
import { commonAttributes } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    ButtonEditor
} from './editor';

registerBlockType('sage/case-studies', {
    apiVersion: 2,
    title: 'Case Studies',
    icon: 'star-filled',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string',
            default: ''
        },
        subtitle: {
            type: 'string',
            default: ''
        },
        buttonText: {
            type: 'string',
            default: ''
        },
        buttonLink: {
            type: 'string',
            default: ''
        },
        items: {
            type: 'array',
            default: [
                {
                    title: '',
                    description: '',
                    buttonText: '',
                    buttonLink: '',
                    topBoxTitle: '',
                    topBoxDescription: '',
                    topBoxImage: '',
                    bottomBoxTitle: '',
                    bottomBoxDescription: '',
                    bottomBoxImage: '',
                    caseStudiesTitle: '',
                    caseStudiesList: [],
                    caseStudyButtonText: '',
                    caseStudyButtonLink: '',
                },
            ],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { title, subtitle, buttonText, buttonLink, items } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        const handleItemChange = (index, field, value) => {
            const newItems = [...items];
            newItems[index][field] = value;
            setAttributes({ items: newItems });
        };

        const addItem = () => {
            const newItems = [
                ...items,
                {
                    title: '',
                    description: '',
                    buttonText: '',
                    buttonLink: '',
                    topBoxTitle: '',
                    topBoxDescription: '',
                    topBoxImage: '',
                    bottomBoxTitle: '',
                    bottomBoxDescription: '',
                    bottomBoxImage: '',
                    caseStudiesTitle: '',
                    caseStudiesList: [],
                    caseStudyButtonText: '',
                    caseStudyButtonLink: '',
                },
            ];
            setAttributes({ items: newItems });
            setActiveTab(newItems.length - 1);
        };

        const removeItem = (index) => {
            const newItems = [...items];
            newItems.splice(index, 1);
            setAttributes({ items: newItems });
            setActiveTab(Math.max(activeTab - 1, 0));
        };

        return (
            <div {...useBlockProps()}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Case Studies</h2>
                    </div>
                    <TextControl
                        label="Main Title"
                        value={title}
                        onChange={(value) => setAttributes({ title: value })}
                        placeholder="Enter title"
                        tagName="h2"
                    />
                    <TextControl
                        label="Subtitle"
                        value={subtitle}
                        onChange={(value) => setAttributes({ subtitle: value })}
                        placeholder="Enter subtitle"
                        tagName="h3"
                    />

                    <ButtonEditor
                        buttonText={buttonText}
                        buttonLink={buttonLink}
                        onChangeText={(value) => setAttributes({ buttonText: value })}
                        onChangeLink={(value) => setAttributes({ buttonLink: value })}
                        label="Main Button"
                    />

                    <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                        <Panel className="border-0 shadow-none">
                            <PanelBody title="Case Study Items" initialOpen={true}>
                                <TabNavigation
                                    items={items}
                                    activeTab={activeTab}
                                    onTabChange={setActiveTab}
                                    onAddItem={addItem}
                                    onRemoveItem={() => removeItem(activeTab)}
                                    getItemTitle={(_, index) => `Item ${index + 1}`}
                                    itemName="Item"
                                    addButtonTitle="Add New Item"
                                />

                                {items.length > 0 && (
                                    <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30 mt-4">
                                        <ItemEditor
                                            itemIndex={activeTab}
                                            onRemove={() => removeItem(activeTab)}
                                            itemName="Item"
                                            showRemoveButton={items.length > 1}
                                        >
                                            <TextControl
                                                label="Item Title"
                                                value={items[activeTab].title}
                                                onChange={(value) => handleItemChange(activeTab, 'title', value)}
                                                placeholder="Enter item title"
                                                tagName="h3"
                                            />

                                            <TextControl
                                                label="Item Description"
                                                value={items[activeTab].description}
                                                onChange={(value) => handleItemChange(activeTab, 'description', value)}
                                                placeholder="Enter description"
                                                tagName="p"
                                                multiline={true}
                                            />

                                            <ButtonEditor
                                                buttonText={items[activeTab].caseStudyButtonText}
                                                buttonLink={items[activeTab].caseStudyButtonLink}
                                                onChangeText={(value) => handleItemChange(activeTab, 'caseStudyButtonText', value)}
                                                onChangeLink={(value) => handleItemChange(activeTab, 'caseStudyButtonLink', value)}
                                                label="Case Study Button"
                                            />

                                            <div className="mb-6 pb-6 border-b border-neutral-30">
                                                <h3 className="text-base font-medium mb-3">Top Box</h3>

                                                <TextControl
                                                    label="Top Box Title"
                                                    value={items[activeTab].topBoxTitle}
                                                    onChange={(value) => handleItemChange(activeTab, 'topBoxTitle', value)}
                                                    placeholder="Enter top box title"
                                                    tagName="h4"
                                                />

                                                <TextControl
                                                    label="Top Box Description"
                                                    value={items[activeTab].topBoxDescription}
                                                    onChange={(value) => handleItemChange(activeTab, 'topBoxDescription', value)}
                                                    placeholder="Enter top box description"
                                                    tagName="p"
                                                    multiline={true}
                                                />

                                                <ImageUploader
                                                    image={items[activeTab].topBoxImage}
                                                    onSelect={(url) => handleItemChange(activeTab, 'topBoxImage', url)}
                                                    onRemove={() => handleItemChange(activeTab, 'topBoxImage', '')}
                                                    label="Top Box Image"
                                                    description="Upload an image for the top box."
                                                    height={48}
                                                    objectFit="cover"
                                                    altText="Top Box Image"
                                                />
                                            </div>

                                            <div className="mb-6 pb-6 border-b border-neutral-30">
                                                <h3 className="text-base font-medium mb-3">Bottom Box</h3>

                                                <TextControl
                                                    label="Bottom Box Title"
                                                    value={items[activeTab].bottomBoxTitle}
                                                    onChange={(value) => handleItemChange(activeTab, 'bottomBoxTitle', value)}
                                                    placeholder="Enter bottom box title"
                                                    tagName="h4"
                                                />

                                                <TextControl
                                                    label="Bottom Box Description"
                                                    value={items[activeTab].bottomBoxDescription}
                                                    onChange={(value) => handleItemChange(activeTab, 'bottomBoxDescription', value)}
                                                    placeholder="Enter bottom box description"
                                                    tagName="p"
                                                    multiline={true}
                                                />

                                                <ImageUploader
                                                    image={items[activeTab].bottomBoxImage}
                                                    onSelect={(url) => handleItemChange(activeTab, 'bottomBoxImage', url)}
                                                    onRemove={() => handleItemChange(activeTab, 'bottomBoxImage', '')}
                                                    label="Bottom Box Image"
                                                    description="Upload an image for the bottom box."
                                                    height={48}
                                                    objectFit="cover"
                                                    altText="Bottom Box Image"
                                                />
                                            </div>

                                            <div className="mb-4">
                                                <TextControl
                                                    label="Case Studies Title"
                                                    value={items[activeTab].caseStudiesTitle}
                                                    onChange={(value) => handleItemChange(activeTab, 'caseStudiesTitle', value)}
                                                    placeholder="Enter case studies title"
                                                    tagName="h4"
                                                />

                                                <div className="mb-4">
                                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Case Studies List</label>
                                                    <div class="list">
                                                        <RichText
                                                            tagName="ul"
                                                            multiline="li"
                                                            placeholder="Add case studies"
                                                            value={items[activeTab].caseStudiesList}
                                                            onChange={(value) => handleItemChange(activeTab, 'caseStudiesList', value)}
                                                            className="mb-4 list-none last:mb-0 pl-5 p-3 bg-white rounded-md border border-neutral-30 focus:border-primary-70 focus:ring-1 focus:ring-primary-70 outline-none"
                                                            style={{ border: '1px solid #E2E8F0' }}
                                                        />
                                                    </div>
                                                    <p className="mt-1 text-xs text-neutral-60">Add case studies as bullet points</p>
                                                </div>
                                            </div>
                                        </ItemEditor>
                                    </div>
                                )}
                            </PanelBody>
                        </Panel>
                    </div>
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="h3" value={attributes.subtitle} />
                    <RichText.Content tagName="span" value={attributes.buttonText} />
                    {attributes.buttonLink && (
                        <a href={attributes.buttonLink} className="case-study-button">
                            <RichText.Content tagName="span" value={attributes.buttonText} />
                        </a>
                    )}

                    {attributes.items?.map((item, index) => (
                        <div key={`case-study-${index}`}>
                            <RichText.Content tagName="h3" value={item.title} />
                            <RichText.Content tagName="p" value={item.description} />

                            {item.caseStudyButtonText && (
                                <a href={item.caseStudyButtonLink || "#"} className="case-study-button">
                                    <RichText.Content tagName="span" value={item.caseStudyButtonText} />
                                </a>
                            )}

                            <RichText.Content tagName="h4" value={item.topBoxTitle} />
                            <RichText.Content tagName="p" value={item.topBoxDescription} />
                            {item.topBoxImage && (
                                <img
                                    src={item.topBoxImage}
                                    alt={item.topBoxTitle ? `${item.topBoxTitle} - Top Box` : "Top Box Image"}
                                />
                            )}

                            <RichText.Content tagName="h4" value={item.bottomBoxTitle} />
                            <RichText.Content tagName="p" value={item.bottomBoxDescription} />
                            {item.bottomBoxImage && (
                                <img
                                    src={item.bottomBoxImage}
                                    alt={item.bottomBoxTitle ? `${item.bottomBoxTitle} - Bottom Box` : "Bottom Box Image"}
                                />
                            )}

                            <RichText.Content tagName="h4" value={item.caseStudiesTitle} />
                            {item.caseStudiesList?.length > 0 && (
                                <ul>
                                    {item.caseStudiesList.map((study, i) => (
                                        <li key={`case-study-list-${index}-${i}`}>
                                            {study}
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/case-studies"></div>
            </div>
        );
    }
});
