<?php
add_action('init', function () {
    register_nav_menus([
        'primary_navigation' => __('Primary Navigation', 'sage'),
        'footer_navigation'  => __('Footer Navigation', 'sage'),
    ]);
});

add_action('admin_enqueue_scripts', function ($hook) {
    if ($hook !== 'nav-menus.php') {
        return;
    }
    wp_enqueue_media();
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.js-open-media').forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const container = button.closest('.field-image-upload');
                const inputField = container.querySelector('.menu-item-image-id');
                const previewField = container.querySelector('.image-preview');

                const frame = wp.media({
                    title: 'Select or Upload Image',
                    button: { text: 'Use this image' },
                    multiple: false
                });

                frame.on('select', function() {
                    const attachment = frame.state().get('selection').first().toJSON();
                    if (inputField) inputField.value = attachment.id;
                    if (previewField) previewField.src = attachment.url;
                    previewField.style.display = 'block';
                });

                frame.open();
            });
        });
        document.querySelectorAll('.menu-item-tags').forEach(function (input) {
            if (input.dataset.initialized) return;
            input.dataset.initialized = true;

            const hiddenInput = input;
            const container = document.createElement('div');
            container.className = 'tags-container';

            const tagList = document.createElement('div');
            tagList.className = 'tags-list';

            const tagInput = document.createElement('input');
            tagInput.type = 'text';
            tagInput.placeholder = 'Add tags';
            tagInput.className = 'tags-input';

            container.appendChild(tagList);
            container.appendChild(tagInput);
            hiddenInput.parentNode.insertBefore(container, hiddenInput.nextSibling);

            const existingTags = hiddenInput.value.split(',').filter(Boolean);
            existingTags.forEach(addTag);

            function addTag(tagValue) {
                const tag = document.createElement('span');
                tag.className = 'tag';
                tag.textContent = tagValue;

                const removeButton = document.createElement('button');
                removeButton.type = 'button';
                removeButton.className = 'remove-tag';
                removeButton.textContent = '×';

                removeButton.addEventListener('click', function () {
                    tagList.removeChild(tag);
                    updateHiddenInput();
                });

                tag.appendChild(removeButton);
                tagList.appendChild(tag);
                updateHiddenInput();
            }

            function updateHiddenInput() {
                const tags = Array.from(tagList.children).map(tag => tag.textContent.slice(0, -1));
                hiddenInput.value = tags.join(',');
            }

            tagInput.addEventListener('keypress', function (e) {
                if (e.key === ',' || e.key === 'Enter') {
                    e.preventDefault();
                    const value = tagInput.value.trim();
                    if (value) {
                        addTag(value);
                        tagInput.value = '';
                    }
                }
            });
        });
    });
    </script>
        <style>
        .tags-container {
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 5px;
        }
        .tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .tag {
            background: #ddd;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-flex;
            align-items: center;
        }
        .remove-tag {
            background: transparent;
            border: none;
            margin-left: 5px;
            cursor: pointer;
            color: #666;
            padding: 1px 0 3px 0;
        }
        .tags-input {
            flex: 1;
            min-width: 100px;
        }
    </style>
    <?php
});

add_action('wp_nav_menu_item_custom_fields', function ($item_id, $item, $depth, $args) {
    $submenu_type = '';

    if ($depth > 1) {
        $parent_id = $item->menu_item_parent;
        if ($parent_id) {
            $submenu_type = get_post_meta($parent_id, '_menu_item_submenu_type', true);
            $grandparent_id = get_post_meta($parent_id, '_menu_item_menu_item_parent', true);
            if ($grandparent_id) {
                $submenu_type = get_post_meta($grandparent_id, '_menu_item_submenu_type', true);
            }
        }
    } elseif ($depth === 0) {
        $submenu_type = get_post_meta($item_id, '_menu_item_submenu_type', true);
    }

    $section = '';

    if (!empty($item->menu_item_parent)) {
        $parent_item = get_post($item->menu_item_parent);

        if ($parent_item) {
            $section = $parent_item->post_title;
        }
    }
    
    if ($depth === 0) {
        ?>
        <p class="field-submenu-type description description-wide">
            <label for="edit-menu-item-submenu-type-<?php echo esc_attr($item_id); ?>">
                <?php _e('Submenu Type:', 'sage'); ?>
                <select
                    id="edit-menu-item-submenu-type-<?php echo esc_attr($item_id); ?>"
                    class="widefat"
                    name="menu-item-submenu-type[<?php echo esc_attr($item_id); ?>]"
                >
                    <option value=""        <?php selected($submenu_type, ''); ?>>None</option>
                    <option value="company" <?php selected($submenu_type, 'company'); ?>>Company</option>
                    <option value="services"<?php selected($submenu_type, 'services'); ?>>Services</option>
                    <option value="studies" <?php selected($submenu_type, 'studies'); ?>>Studies</option>
                </select>
            </label>
        </p>
        <?php
    }

    $image_id = get_post_meta($item_id, '_menu_item_image_id', true);
    $image_url = get_post_meta($item_id, '_menu_item_image_url', true);

    ?>
    <p class="field-image-upload description description-wide">
        <label>
            <?php _e('Menu Item Image:', 'sage'); ?><br>
            <input type="hidden"
                name="menu-item-image-id[<?php echo esc_attr($item_id); ?>]"
                class="menu-item-image-id"
                value="<?php echo esc_attr($image_id); ?>"
            />
            <img src="<?php echo esc_url(wp_get_attachment_url($image_id)); ?>"
                alt=""
                class="image-preview"
                style="max-width: 100%; height: auto; display: <?php echo $image_id ? 'block' : 'none'; ?>;"
            />
            <button type="button"
                    class="button js-open-media"
                    style="margin-top: 5px;">
                <?php _e('Select/Upload', 'sage'); ?>
            </button>
        </label>
    </p>
    <?php

    if ($submenu_type === 'services' && $section === 'Items') {
        $tags = get_post_meta($item_id, '_menu_item_tags', true);
        ?>
        <p class="field-tags description description-wide">
            <label>
                <?php _e('Tags:', 'sage'); ?><br>
                <input type="text"
                    name="menu-item-tags[<?php echo esc_attr($item_id); ?>]"
                    class="menu-item-tags wp-tag-input"
                    style="display:none;"
                    value="<?php echo esc_attr($tags); ?>"
                    placeholder="<?php _e('Add tags separated by commas or press Enter.', 'sage'); ?>"
                />
            </label>
        </p>
        <?php
    }

    if ($submenu_type === 'services' && $section === 'CTA Wide') {
        $cta_title = get_post_meta($item_id, '_menu_item_cta_wide_title', true);
    
        ?>
        <p class="field-cta-title description description-wide">
            <label>
                <?php _e('CTA Title:', 'sage'); ?><br>
                <input type="text"
                       name="menu-item-cta-wide-title[<?php echo esc_attr($item_id); ?>]"
                       class="widefat code"
                       value="<?php echo esc_attr($cta_title); ?>"
                       placeholder="Enter CTA Title..."
                />
            </label>
        </p>
        <?php
    }

    if ($submenu_type === 'services' && $section === 'CTA Narrow') {
        $cta_title = get_post_meta($item_id, '_menu_item_cta_narrow_title', true);
    
        ?>
        <p class="field-cta-title description description-wide">
            <label>
                <?php _e('CTA Title:', 'sage'); ?><br>
                <input type="text"
                       name="menu-item-cta-narrow-title[<?php echo esc_attr($item_id); ?>]"
                       class="widefat code"
                       value="<?php echo esc_attr($cta_title); ?>"
                       placeholder="Enter CTA Title..."
                />
            </label>
        </p>
        <?php
    }

    if ($submenu_type === 'studies' && $section === 'Industries') {
        $tags = get_post_meta($item_id, '_menu_item_tags', true);
        ?>
        <p class="field-tags description description-wide">
            <label>
                <?php _e('Tags:', 'sage'); ?><br>
                <input type="text"
                    name="menu-item-tags[<?php echo esc_attr($item_id); ?>]"
                    class="menu-item-tags wp-tag-input"
                    style="display:none;"
                    value="<?php echo esc_attr($tags); ?>"
                    placeholder="<?php _e('Add tags separated by commas or press Enter.', 'sage'); ?>"
                />
            </label>
        </p>
        <?php
    }
    
    if ($submenu_type === 'studies' && $section === 'Slider') {
        $slide_title = get_post_meta($item_id, '_menu_item_slide_title', true);
        ?>
        <p class="field-slide-title description description-wide">
            <label>
                <?php _e('Slide Title:', 'sage'); ?><br>
                <input type="text"
                       name="menu-item-slide-title[<?php echo esc_attr($item_id); ?>]"
                       class="widefat code"
                       value="<?php echo esc_attr($slide_title); ?>"
                       placeholder="Enter Slide Title..."
                />
            </label>
        </p>
        <?php
    }

    if ($submenu_type === 'company' && $section === 'CTA') {
        $cta_title = get_post_meta($item_id, '_menu_item_company_cta_title', true);
        ?>
        <p class="field-cta-title description description-wide">
            <label>
                <?php _e('CTA Title:', 'sage'); ?><br>
                <input type="text"
                       name="menu-item-company-cta-title[<?php echo esc_attr($item_id); ?>]"
                       class="widefat code"
                       value="<?php echo esc_attr($cta_title); ?>"
                       placeholder="Enter CTA Title..."
                />
            </label>
        </p>
        <?php
    }
}, 10, 4);

add_action('wp_update_nav_menu_item', function ($menu_id, $menu_item_db_id, $args) {
    $fields = [
        'menu-item-submenu-type'      => '_menu_item_submenu_type',
        'menu-item-image-id'          => '_menu_item_image_id',
        'menu-item-image-url'         => '_menu_item_image_url',
        'menu-item-tags'              => '_menu_item_tags',
        'menu-item-cta-wide-title'    => '_menu_item_cta_wide_title',
        'menu-item-cta-narrow-title'  => '_menu_item_cta_narrow_title',
        'menu-item-slide-title'       => '_menu_item_slide_title',
        'menu-item-company-cta-title' => '_menu_item_company_cta_title', 
    ];
    
    foreach ($fields as $field => $meta_key) {
        if (isset($_POST[$field][$menu_item_db_id])) {
            $value = $_POST[$field][$menu_item_db_id];
    
            if ($field === 'menu-item-image-id') {
                $value = intval($value);
            } elseif ($field === 'menu-item-tags') {
                $tags = explode(',', $value);
                $sanitized_tags = array_map('sanitize_text_field', $tags);
                $value = implode(',', $sanitized_tags);
            } else {
                $value = sanitize_text_field($value);
            }
    
            update_post_meta($menu_item_db_id, $meta_key, $value);
        } else {
            delete_post_meta($menu_item_db_id, $meta_key);
        }
    }
}, 10, 3);

add_action('wp_update_nav_menu', function ($menu_id, $menu_data = null) {
    if (!is_array($menu_data)) {
        $menu_data = [];
    }

    $all_items = wp_get_nav_menu_items($menu_id, ['post_status' => 'any']);
    if (!$all_items) return;

    $meta_map = [];
    foreach ($all_items as $obj) {
        $meta_map[$obj->ID] = [
            'submenu_type' => get_post_meta($obj->ID, '_menu_item_submenu_type', true),
            'parent' => get_post_meta($obj->ID, '_menu_item_menu_item_parent', true),
            'locked' => get_post_meta($obj->ID, '_locked_section', true),
        ];
    }

    foreach ($all_items as $obj) {
        $item_id = $obj->ID;
        $submenu_type = $meta_map[$item_id]['submenu_type'];
        $parent_val = $meta_map[$item_id]['parent'];
        $is_top_level = (empty($parent_val) || (int)$parent_val === 0);

        if ($is_top_level) {
            $locked_sections = [];

            if ($submenu_type === 'services') {
                $locked_sections = [
                    ['slug' => 'services_a', 'title' => 'Items'],
                    ['slug' => 'services_b', 'title' => 'CTA Wide'],
                    ['slug' => 'services_c', 'title' => 'CTA Narrow'],
                ];
            } elseif ($submenu_type === 'studies') {
                $locked_sections = [
                    ['slug' => 'studies_a', 'title' => 'Industries'],
                    ['slug' => 'studies_b', 'title' => 'Slider'],
                    ['slug' => 'studies_c', 'title' => 'Button'],
                ];
            } elseif ($submenu_type === 'company') {
                $locked_sections = [
                    ['slug' => 'company_a', 'title' => 'Image'],
                    ['slug' => 'company_b', 'title' => 'Links'],
                    ['slug' => 'company_c', 'title' => 'CTA'],
                ];
            }

            foreach ($locked_sections as $sec) {
                $existing = get_posts([
                    'post_type' => 'nav_menu_item',
                    'meta_key' => '_locked_section',
                    'meta_value' => $sec['slug'],
                    'post_status' => 'publish',
                    'posts_per_page' => 1,
                    'tax_query' => [
                        [
                            'taxonomy' => 'nav_menu',
                            'field' => 'term_id',
                            'terms' => $menu_id,
                        ]
                    ]
                ]);

                if (empty($existing)) {
                    $child_id = wp_insert_post([
                        'post_type' => 'nav_menu_item',
                        'post_status' => 'publish',
                        'post_title' => $sec['title'],
                    ]);
                    if ($child_id) {
                        update_post_meta($child_id, '_menu_item_menu_item_parent', (string)$item_id);
                        update_post_meta($child_id, '_menu_item_object_id', $child_id);
                        update_post_meta($child_id, '_menu_item_object', 'custom');
                        update_post_meta($child_id, '_menu_item_type', 'custom');
                        update_post_meta($child_id, '_locked_section', $sec['slug']);
                        wp_set_object_terms($child_id, [$menu_id], 'nav_menu', false);
                    }
                } else {
                    $existing_id = $existing[0]->ID;
                    wp_set_object_terms($existing_id, [$menu_id], 'nav_menu', false);
                }
            }
        }
    }
}, 10, 2);

add_action('customize_register', function ($wp_customize) {
    $wp_customize->add_setting('custom_logo');
    $wp_customize->add_control(new WP_Customize_Image_control($wp_customize, 'custom_logo', [
        'label' => __('Upload Logo', 'sage'),
        'section' => 'title_tagline',
        'settings' => 'custom_logo',
    ]));
});

function display_custom_logo($classes = '') {
    if (get_theme_mod('custom_logo')) {
        echo '<img src="' . esc_url(get_theme_mod('custom_logo')) . '" alt="' . get_bloginfo('name') . '" class="' . esc_attr($classes) . '">';
    } else {
        echo '<h1>' . get_bloginfo('name') . '</h1>';
    }
}