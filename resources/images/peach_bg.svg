<svg width="1392" height="1004" viewBox="0 0 1392 1004" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1939_16850)">
<rect width="1392" height="1004" fill="#FAF8F7"/>
<g opacity="0.35" filter="url(#filter0_f_1939_16850)">
<circle cx="454" cy="689" r="316" fill="#E66953"/>
</g>
<g opacity="0.5" filter="url(#filter1_f_1939_16850)">
<circle cx="827" cy="341" r="239" fill="#F5B067"/>
</g>
</g>
<defs>
<filter id="filter0_f_1939_16850" x="-362" y="-127" width="1632" height="1632" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1939_16850"/>
</filter>
<filter id="filter1_f_1939_16850" x="88" y="-398" width="1478" height="1478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1939_16850"/>
</filter>
<clipPath id="clip0_1939_16850">
<rect width="1392" height="1004" fill="white"/>
</clipPath>
</defs>
</svg>
