@php
$headings = [];
if (!empty($attributes['items']) && is_array($attributes['items'])) {
    foreach ($attributes['items'] as $index => $item) {
        if (!empty($item['title'])) {
            $headings[] = [
                'id' => 'accordion-item-' . $index,
                'text' => $item['title'],
            ];
        }
    }
}
@endphp

<div class="accordion-section relative flex justify-center items-center bg-[linear-gradient(180deg,white_0%,#F4F6FA_10%)]">
    <div class="container px-5 py-10 flex gap-20">
        <div>
            @if (!empty($attributes['title']))
                <x-title class="mb-2" variation="large" id="accordion-title">{!! $attributes['title'] !!}</x-title>
            @endif

            @if (!empty($attributes['subtitle']))
                <p class="mb-14">{!! $attributes['subtitle'] !!}</p>
            @endif

            @foreach ($attributes['items'] as $index => $item)
                <div id="accordion-item-{{ $index }}" class="accordion-item overflow-hidden mb-3 last:mb-0 p-2 pb-0 bg-white rounded-xl border border-[#E8EBF3] lg:p-5 lg:mb-5 {{ $index === 0 ? 'mb-8' : '' }}">
                    <div class="accordion-toggle cursor-pointer flex flex-wrap gap-3 mt-1 mb-4 items-center justify-start {{ $index === 0 ? 'top after:w-full after:h-px after:lg:mt-4 after:lg:mb-5' : 'mb-2 lg:my-0' }}">
                        @if (!empty($item['image']))
                            <div class="w-[38px] h-[38px] flex justify-center items-center rounded-full overflow-hidden bg-[#DEE3EE] border-4 border-[#E4E9F1] lg:w-[54px] lg:h-[54px]">
                                <img src="{{ $item['image'] }}" class="object-contain" />
                            </div>
                        @endif

                        <div class="flex flex-col">
                            @if (!empty($item['title']))
                                <h3 class="text-bodyMedium lg:text-bodyMediumDesktop" id="accordion-item-title-{{ $index }}">{!! $item['title'] !!}</h3>
                            @endif

                            @if (!empty($item['subtitle']))
                                <span class="text-bodyExtraSmallDesktop lg:bg-[#E8EBF3] lg:rounded-full lg:px-2">{!! $item['subtitle'] !!}</span>
                            @endif
                        </div>

                        @if($index !== 0)
                            <i class="text-bodyMedium ml-auto relative top-0 ml-0.5 transition-all duration-700 fa-light fa-chevron-down"></i>
                        @endif
                    </div>

                    <div class="{{ $index !== 0 ? 'mt-5 toggle-hide hidden' : '' }}">
                        @if (!empty($item['description']))
                            <p class="text-bodySmall lg:text-bodySmallDesktop mb-8">
                                {!! $item['description'] !!}
                            </p>
                        @endif

                        @if (!empty($item['listTitle']))
                            <h5 class="text-bodyMedium mb-5 lg:text-bodyMediumDesktop">
                                {!! $item['listTitle'] !!}
                            </h5>
                        @endif

                        @if (!empty($item['listItems']) && is_array($item['listItems']))
                            <ul class="flex flex-col mb-8">
                                @foreach ($item['listItems'] as $listItem)
                                    <li class="text-bodySmall flex gap-3 items-start lg:text-bodySmallDesktop mb-5">
                                        <i class="p-1 text-bodyExtraSmall bg-[#CCE9F6] rounded-full fa-solid fa-check"></i>
                                        <span>{!! $listItem !!}</span>
                                    </li>
                                @endforeach
                            </ul>
                        @endif

                        @if (!empty($item['advantages']) && is_array($item['advantages']))
                            <h5 class="text-bodyMedium lg:text-bodyMediumDesktop mb-2">Why choose this service:</h5>
                            <div class="flex flex-col justify-start gap-5 mb-2 lg:flex-row">
                                @foreach ($item['advantages'] as $advantage)
                                    <span class="text-bodySmall bg-[#E8EBF3] rounded-xl block p-3 lg:text-bodySmallDesktop">
                                        {!! $advantage !!}
                                    </span>
                                @endforeach
                            </div>
                        @endif

                        @if($index === 0)
                            <div class="rounded-xl flex flex-col justify-between items-start overflow-hidden relative p-6 mt-8 mb-2 z-10 lg:flex-row lg:items-center lg:mt-6 lg:mb-0">
                                <div class="absolute w-full h-full left-0 top-0 blue-dark-bg saturate-150 -z-10"></div>
                                <span class="text-bodyMedium mb-2 relative lg:text-bodyMediumDesktop lg:my-0">Discover how we work.</span>
                                <x-button 
                                    type="link"
                                    variant="secondary"
                                    icon="down"
                                    size="extra-small"
                                    :href="'#process'">
                                    Our process</x-button>
                            </div>
                        @endif
                    </div>
                </div>

                @if ($index === 0)
                    <x-title class="uppercase font-medium mb-4" variation="subtitle">Specialized services</x-title>
                    <div class="accordion-items">
                @endif
            @endforeach
            </div>
        </div>
        <div class="w-full order-1 hidden lg:block lg:order-2 lg:min-w-66">
            <div class="sticky top-10">
                <h2 class="text-tagline font-semibold uppercase pt-4 px-5 pb-3 mb-5 bg-primary-10 text-primary-70 rounded-lg lg:text-taglineDesktop">Table of Contents</h2>
                <ul class="table-of-contents">
                    @foreach ($headings as $heading)
                        <li class="text-bodySmall font-regular flex items-center transition-all px-[18px] mb-3 min-h-10 lg:text-bodySmallDesktop">
                            <a href="#{{ $heading['id'] }}" class="transition-all text-[#6E7B91]">
                                {{ $heading['text'] }}
                            </a>
                        </li>
                    @endforeach
                </ul>
                <div class="flex flex-col items-start py-8 !bg-[length:300%] rounded-2xl overflow-hidden">
                    <span class="text-bodyMedium font-medium mb-2 lg:text-bodyMediumDesktop">See where our expertise fits in?</span>
                    <x-button type="link" variant="secondary" size="extra-small" :href="'#'">Let's talk!</x-button>
                </div>
            </div>
        </div>
    </div>
</div>
