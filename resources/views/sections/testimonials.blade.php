<div class="testimonials-section flex justify-center items-start relative">
    <div class="container px-5 py-8 lg:py-[62px]">
        <div class="flex flex-col items-center lg:flex-row items-start">
            <div class="mb-6 lg:mb-0 w-full lg:min-w-[416px] lg:mr-6">
                @if(!empty($attributes['title']))
                    <x-title class="mb-2" variation="large">{!! $attributes['title'] !!}</x-title>
                @endif
                @if(!empty($attributes['description']))
                    <p class="text-bodyLarge lg:text-bodyLargeDesktop mb-6">{!! $attributes['description'] !!}</p>
                @endif
                
                <div class="swiper-navigation flex items-center space-x-4 relative">
                    <button class="swiper-button-prev after:content-none static m-0 w-5 p-px cursor-pointer" tabindex="0" aria-label="Previous slide"><i class="fa-thin fa-arrow-left text-[#222831]"></i></button>
                    <button class="swiper-button-next after:content-none static m-0 w-5 p-px cursor-pointer" tabindex="0" aria-label="Next slide"><i class="fa-thin fa-arrow-right text-[#222831]"></i></button>
                </div>
            </div>
            
            <div class="swiper-container w-full lg:w-[calc(100%_-_440px)]" style="mask:linear-gradient(to right,rgba(0,0,0,1) 80%,rgba(0,0,0,0) 99%)">
                <div class="swiper">
                    <div class="swiper-wrapper">
                        @if(!empty($attributes['testimonials']))
                            @foreach($attributes['testimonials'] as $testimonial)
                                <div class="swiper-slide flex flex-col justify-between w-[268px] h-[auto] rounded-3xl overflow-hidden border border-[#E4E9F1] border-3xl lg:w-[330px]">
                                    <div class="p-6">
                                        <img class="w-9 mb-7" src="{{ asset('images/quote.svg') }}" />

                                        @if(!empty($testimonial['text']))
                                            <p>{!! strlen($testimonial['text']) > 134 ? substr($testimonial['text'], 0, 134) . '...' : $testimonial['text'] !!}</p>
                                        @endif
                                    </div>

                                    <div class="flex items-center justify-between bg-[#F0F2F4] p-6">
                                        <div class="flex gap-3">
                                            @if(!empty($testimonial['image']))
                                                <img class="w-10 object-cover" src="{!! $testimonial['image'] !!}" alt="Company Logo">
                                            @endif
                                            @if(!empty($testimonial['companyName']))
                                                <span class="flex flex-col justify-center max-w-36 text-bodySmall lg:textBodySmallDesktop">{!! $testimonial['companyName'] !!}</span>
                                            @endif
                                        </div>
                                        @if(!empty($testimonial['buttonLink']))
                                            <a href="{{ $testimonial['buttonLink'] }}" class="rounded-full border border-[#3F4A5A] w-8 h-8 flex items-center justify-center">
                                                <i class="fa-light fa-arrow-up-right text-[#3F4A5A]"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>