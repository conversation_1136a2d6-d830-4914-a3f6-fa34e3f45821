<div class="blog-hero flex justify-center relative">
    <div class="container px-5 py-16">
        <div class="blog-hero py-2">
            <div class="flex flex-col justify-between items-center lg:flex-row">
                <x-title variation="hero" highlight="dark" class="mb-4 pb-1.5 lg:w-3/5">{!! $attributes['title'] !!}</x-title>
            </div>

            <div class="gap-6 flex flex-col lg:h-96 lg:py-4 lg:flex-row">
                <div class="">
                @if (!empty($attributes['featuredPostId']))
                    @php
                        $featured_post      = get_post($attributes['featuredPostId']);
                        $featured_permalink = get_permalink($featured_post);
                        $featured_title     = get_the_title($featured_post);
                        $featured_excerpt   = str_replace('&hellip;', '...', wp_trim_words(get_the_excerpt($featured_post), 30));
                        $featured_image_url = get_the_post_thumbnail_url($featured_post, 'full');
                        $featured_date      = get_the_date('j F Y', $featured_post);
                        $featured_category  = '';
                        $categories         = get_the_category($featured_post);

                        if (!empty($categories)) {
                            foreach ($categories as $category) {
                                if ($category->name !== 'Popular') {
                                    $featured_category = $category->name;
                                    break;
                                }
                            }
                        }
                    @endphp

                    <div class="featured-article h-full">
                        <article class="h-full">
                            <a class="relative h-full flex flex-col rounded-3xl bg-white/60 overflow-hidden transition-all duration-300 hover:bg-white lg:flex-row" href="{!! $featured_permalink !!}">
                                @if ($featured_image_url)
                                    <div class="relative lg:min-w-[33%] lg:max-w-[33%]">
                                        <img class="w-full h-64 object-cover lg:h-full" src="{{ $featured_image_url }}" alt="{{ esc_attr($featured_title) }}" class="img-fluid mb-3 rounded">
                                        <div class="absolute top-3 left-3 rounded-full backdrop-blur-md bg-white/45 px-3 pt-[5px] pb-[3px]">Featured article</div>
                                    </div>
                                @endif
                                <div class="flex flex-col items-start pt-5 pb-6 px-6">
                                    <div class="w-full flex justify-between mb-8">
                                        <span class="text-subtitle font-semibold uppercase text-[#566276] lg:text-subtitleDesktop">{!! $featured_category !!}</span>
                                        <span class="text-bodyExtraSmall lg:text-bodyExtraSmallDesktop">{!! $featured_date !!}</span>
                                    </div>
                                    <div class="relative h-40 py-0.5 mb-8 lg:mb-14">
                                        <x-title variation="medium" class="mb-2">{!! $featured_title !!}</x-title>
                                        <p class="text-bodySmall text-[#566276] dynamic-clamp lg:text-bodySmallDesktop">{!! $featured_excerpt !!}</p>
                                    </div>
                                    <x-button 
                                        type="button"
                                        variant="primary"
                                        size="extra-small"
                                        class="mt-auto"
                                        :href="$featured_permalink">
                                        Read more
                                    </x-button>
                                </div>
                            </a>
                        </article>
                    </div>
                @else
                    <p>No featured article selected.</p>
                @endif
                </div>

                <div class="p-4 pt-6 rounded-3xl flex relative lg:w-60 bg-white">
                    @php
                        $popular_posts = new WP_Query([
                        'posts_per_page' => 5,
                        'category_name'  => 'popular',
                        ]);
                    @endphp

                    <img class="w-10 object-contain absolute top-40 left-0.5 -mt-1.5" src="{{ asset('images/stars.svg') }}" />

                    @if ($popular_posts->have_posts())
                        <div class="swiper popular-articles-slider !flex flex-col justify-between">
                            <div class="flex items-center mb-4">
                                <span class="text-subtitle uppercase font-semibold text-[#566276] min-w-[60%] lg:text-subtitleDesktop">Popular Articles</span>
                                <div class="swiper-pagination h-4 py-0.5 !static flex justify-end"></div>
                            </div>
                            <div class="swiper-wrapper">
                                @while ($popular_posts->have_posts())
                                    @php $popular_posts->the_post(); @endphp
                                    <div class="swiper-slide popular-article">
                                        <div class="flex flex-col h-full">
                                            <div class="relative h-28 py-1.5">
                                                <span class="block text-bodyMedium mb-2 lg:text-bodyMediumDesktop">{!! get_the_title() !!}</span>
                                                <p class="dynamic-clamp text-bodyExtraSmall lg:text-bodyExtraSmallDesktop">{!! wp_trim_words(get_the_excerpt(), 15, '...') !!}</p>
                                            </div>
                                            @if (has_post_thumbnail())
                                                <div class="relative rounded-3xl overflow-hidden w-full mt-auto lg:h-[156px]">
                                                    <img class="h-full w-full cutout-small object-cover" src="{{ get_the_post_thumbnail_url(null, 'full') }}" />
                                                    <a href="{{ get_permalink() }}" class="absolute right-px bottom-px rounded-full border border-[#3F4A5A] w-10 h-10 flex items-center justify-center">
                                                        <i class="fa-light fa-arrow-up-right text-[#3F4A5A]"></i>
                                                    </a>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endwhile
                                @php wp_reset_postdata(); @endphp
                            </div>
                        </div>
                    @else
                        <p>No popular articles found.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <x-floating-bg color="blue" />
</div>

<div class="blog-articles flex justify-center">
    <div class="container px-5 py-16">
        <div class="pills flex flex-wrap justify-center gap-3">
            <button class="category-pill transition-all pt-[7px] pb-[5px] px-[20px] rounded-full active bg-[#66BCE5] border-2 border-[#99D2ED] text-white whitespace-nowrap" data-category="all">All Blogs</button>
            @php
                $excluded_category_ids = [
                    get_cat_ID('Popular'),
                    get_cat_ID('Top'),
                    get_cat_ID('Uncategorized')
                ];
                $categories = get_categories([
                    'exclude' => $excluded_category_ids,
                    'hide_empty' => true,
                ]);
            @endphp
            @foreach($categories as $category)
                <button class="category-pill border-2 border-[#CFD5E3] text-[#6E7B91] transition-all pt-[7px] pb-[5px] px-[20px] rounded-full" data-category="{{ $category->slug }}">{!! $category->name !!}</button>
            @endforeach
        </div>

        @php
            $posts_per_page = 6;
            $paged          = 1;
            $args           = [
                'posts_per_page' => $posts_per_page,
                'post_status'    => 'publish',
                'paged'          => $paged,
            ];
            $query = new WP_Query($args);
        @endphp

        <div class="posts-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-10" id="posts-grid" data-max-pages="{{ $query->max_num_pages }}" data-found-posts="{{ $query->found_posts }}">
            @foreach ($query->posts as $post)
                @php
                    setup_postdata($post);
                    $post_categories = get_the_category($post->ID);
                    $category_slugs  = $post_categories ? array_map(fn($cat) => $cat->slug, $post_categories) : [];
                    $author_id       = $post->post_author;
                    $author_name     = get_the_author_meta('display_name', $author_id);
                    $author_avatar   = get_avatar_url($author_id);
                    $permalink       = get_permalink($post->ID);
                @endphp

                @include('partials.blog-post-item', [
                    'permalink'      => $permalink,
                    'post_id'        => $post->ID,
                    'date'           => get_the_date('d.m.Y', $post->ID),
                    'has_thumbnail'  => has_post_thumbnail($post->ID),
                    'thumbnail_url'  => get_the_post_thumbnail_url($post->ID, 'full'),
                    'title'          => get_the_title($post->ID),
                    'excerpt'        => wp_trim_words(get_the_excerpt($post->ID), 20),
                    'author_avatar'  => $author_avatar,
                    'author_name'    => $author_name,
                    'categories'     => $post_categories,
                    'category_slugs' => $category_slugs,
                ])

                @php wp_reset_postdata(); @endphp
            @endforeach
        </div>

        <div class="load-more-container text-center mt-10">
            <x-button id="load-more" type="link" variant="secondary" size="small" :href="'#'">
                Load 6 more
            </x-button>
        </div>
    </div>
</div>
