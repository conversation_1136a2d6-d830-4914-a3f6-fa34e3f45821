@php
// Query the three latest case studies
$related_case_studies = new WP_Query([
    'post_type' => 'case_study',
    'posts_per_page' => 3,
    'orderby' => 'date',
    'order' => 'DESC',
]);
@endphp

@if($related_case_studies->have_posts())
    <div class="flex justify-center items-center">
        <div class="container flex flex-col items-start justify-between px-5 py-8 lg:flex-row">
            <div class="mb-4 lg:max-w-md lg:w-3/5 lg:pr-6 lg:mb-0">
                <x-title class="mb-2" variation="large">{!! $attributes['title'] !!}</x-title>
                <p class="text-bodyLarge lg:text-bodyLargeDesktop">
                    {!! $attributes['description'] !!}
                </p>
            </div>
            <div>
                @if(!empty($attributes['buttonText']) && !empty($attributes['buttonLink']))
                    <x-button type="link" variant="secondary" :href="$attributes['buttonLink']">
                        {!! $attributes['buttonText'] !!}
                    </x-button>
                @endif
            </div>
        </div>
    </div>

    <div class="container px-5 py-10 mx-auto scroll-smooth">
        <div class="related-case-studies grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-10">
            @while($related_case_studies->have_posts())
                @php
                    $related_case_studies->the_post();
                    $primary_industry = get_yoast_primary_term(get_the_ID(), 'industry');
                    $primary_service = get_yoast_primary_term(get_the_ID(), 'service');
                    $permalink = get_permalink();
                    $excerpt = has_excerpt() ? wp_strip_all_tags(get_the_excerpt()) : wp_trim_words(wp_strip_all_tags(get_the_content()), 20);
                @endphp
                <div class="case-study-item border border-[#CFD5E3] rounded-3xl overflow-hidden hover:scale-105 transition-all relative">
                    <a class="absolute left-0 top-0 w-full h-full z-10" href="{{ $permalink }}"></a>
                    @if(has_post_thumbnail())
                        <div class="case-study-featured-image">
                            <img class="w-full h-48 object-cover" src="{{ get_the_post_thumbnail_url() }}" alt="{{ get_the_title() }}">
                        </div>
                    @endif
                    <div class="flex flex-col items-start p-6">
                        <div class="flex flex-wrap gap-2 items-center mb-4">
                            @if($primary_industry)
                                <span class="tag block text-[#566276] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#E8EBF3] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                    {!! $primary_industry->name !!}
                                </span>
                            @endif
                            @if($primary_service)
                                <span class="tag block text-[#566276] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#E8EBF3] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                    {!! $primary_service->name !!}
                                </span>
                            @endif
                        </div>
                        <h3 class="text-bodyLarge font-medium mb-2 lg:text-bodyLargeDesktop">{{ get_the_title() }}</h3>
                        <p class="text-bodySmall text-[#566276] mb-4 lg:text-bodySmallDesktop">{{ $excerpt }}</p>
                        <span class="text-primary-70 text-bodySmall font-medium lg:text-bodySmallDesktop">Read more</span>
                    </div>
                </div>
            @endwhile
            @php wp_reset_postdata(); @endphp
        </div>
    </div>
@endif
