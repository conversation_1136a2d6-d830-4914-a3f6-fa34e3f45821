<div class="case-studies-collection flex justify-center items-start relative">
    <div class="container px-5 py-8">
        <div class="flex flex-col gap-4 justify-between mb-8 lg:gap-0 lg:mb-12 lg:flex-row">
            <div>
                @if( !empty( $attributes['title'] ) )
                    <x-title variation="medium">{!! $attributes['title'] !!}</x-title>
                @endif
                @if( !empty( $attributes['description'] ) )
                    <p class="text-[#001D2A]">{!! $attributes['description'] !!}</p>
                @endif

                @php
                    $services = get_terms([
                        'taxonomy'   => 'service',
                        'hide_empty' => false,
                    ]);
                @endphp
            </div>
            @if( !empty( $services ) && ! is_wp_error( $services ) )
                <div class="flex flex-col justify-end">
                    <label class="text-bodyMedium text-[#566276] lg:text-bodyMediumDesktop" for="service-filter">Services</label>
                    <div class="flex relative">
                        <select class="w-full appearance-none text-bodyMedium border border-[#CFD5E3] bg-[#FBFBFD] text-[#6E7B91] rounded-2xl px-6 pt-3 pb-2 lg:text-bodyMediumDesktop" id="service-filter">
                            <option value="">All</option>
                            @foreach( $services as $service )
                                <option value="{!! esc_attr( $service->slug ) !!}">{!! esc_html( $service->name ) !!}</option>
                            @endforeach
                        </select>
                        <i class="absolute right-6 top-1/2 -translate-y-1/2 text-[#6E7B91] fa-regular fa-chevron-down"></i>
                    </div>
                </div>
            @endif
        </div>

        @if( !empty( $attributes['featuredCaseStudyId'] ) )
            @php
                $featured = get_post( $attributes['featuredCaseStudyId'] );
                $featured_primary_industry = get_yoast_primary_term( $featured->ID, 'industry' );
                $featured_primary_service  = get_yoast_primary_term( $featured->ID, 'service' );
                $all_featured_services = get_the_terms( $featured->ID, 'service' );
                $featured_service_slugs = [];
                if( !empty( $all_featured_services ) && !is_wp_error( $all_featured_services ) ) {
                    foreach( $all_featured_services as $term ) {
                        $featured_service_slugs[] = $term->slug;
                    }
                }
                $featured_services_data = implode(' ', $featured_service_slugs);
            @endphp

            <div id="featured-original" class="featured-case-study flex flex-wrap gray after:w-full after:h-px after:mt-14 after:mb-14">
                <div class="flex flex-col gap-6 lg:flex-row">
                    @if( !empty( $attributes['featuredCaseStudyImage'] ) )
                        <div class="w-full lg:w-1/2">
                            <img class="w-full object-cover h-80 rounded-xl overflow-hidden" src="{!! esc_url( $attributes['featuredCaseStudyImage'] ) !!}" alt="{!! esc_attr( $attributes['featuredCaseStudyTitle'] ) !!}">
                        </div>
                    @endif
                    <div class="w-full flex flex-col justify-center items-start lg:w-1/2">
                        <div class="flex flex-wrap gap-2 items-center mb-5">
                            @if( $featured_primary_industry )
                                <span class="tag block text-[#566276] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#E8EBF3] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                    {!! $featured_primary_industry->name !!}
                                </span>
                            @endif
                            @if( $featured_primary_service )
                                <span class="tag block text-[#566276] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#E8EBF3] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                    {!! $featured_primary_service->name !!}
                                </span>
                            @endif
                        </div>
                        <x-title class="dynamic-clamp clamp-2 mb-3" variation="medium">{!! $attributes['featuredCaseStudyTitle'] !!}</x-title>
                        <div class="dynamic-clamp clamp-3 mb-5">{!! $attributes['featuredCaseStudyExcerpt'] !!}</div>
                        <x-button
                            type="link"
                            variant="primary"
                            size="extra-small"
                            icon="link"
                            :href="get_permalink( $featured->ID )"
                        >
                            Read More
                        </x-button>
                    </div>
                </div>
            </div>
        @endif

        @php
            $exclude = !empty( $attributes['featuredCaseStudyId'] ) ? [ $attributes['featuredCaseStudyId'] ] : [];
            $args = [
                'post_type'      => 'case_study',
                'posts_per_page' => -1,
                'post__not_in'   => $exclude,
            ];
            $caseStudiesQuery = new WP_Query( $args );
        @endphp

        @if( $caseStudiesQuery->have_posts() )
            <div class="other-case-studies grid grid-cols-1 md:grid-cols-2 gap-6">
                @if( !empty( $attributes['featuredCaseStudyId'] ) )
                    <div id="featured-duplicate" class="case-study-item border border-[#CFD5E3] rounded-3xl overflow-hidden" data-services="{{ $featured_services_data }}">
                        @if( has_post_thumbnail( $featured->ID ) )
                            <img class="w-full h-48 object-cover" src="{{ get_the_post_thumbnail_url( $featured->ID ) }}" alt="{{ get_the_title( $featured->ID ) }}">
                        @endif
                        <div class="flex flex-col items-start p-6">
                            <div class="flex flex-wrap gap-2 items-center mb-4">
                                @if( $featured_primary_industry )
                                    <span class="tag block text-[#566276] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#E8EBF3] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                        {!! $featured_primary_industry->name !!}
                                    </span>
                                @endif
                                @if( $featured_primary_service )
                                    <span class="tag block text-[#566276] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#E8EBF3] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                        {!! $featured_primary_service->name !!}
                                    </span>
                                @endif
                            </div>
                            <x-title class="mb-1" variation="small">{!! $featured->post_title !!}</x-title>
                            <div class="mb-4 text-[#3F4A5A] text-bodySmall lg:text-bodySmallDesktop dynamic-clamp clamp-3">
                                {!! $featured->post_excerpt !!}
                            </div>
                            <x-button
                                type="link"
                                variant="secondary"
                                size="extra-small"
                                icon="link"
                                :href="get_permalink( $featured->ID )"
                            >
                                Read More
                            </x-button>
                        </div>
                    </div>
                @endif

                @while( $caseStudiesQuery->have_posts() )
                    @php
                        $caseStudiesQuery->the_post();
                        $primary_industry = get_yoast_primary_term( get_the_ID(), 'industry' );
                        $primary_service  = get_yoast_primary_term( get_the_ID(), 'service' );
                        $all_services = get_the_terms( get_the_ID(), 'service' );
                        $service_slugs = [];
                        if ( !empty( $all_services ) && !is_wp_error( $all_services ) ) {
                            foreach( $all_services as $term ) {
                                $service_slugs[] = $term->slug;
                            }
                        }
                        $servicesData = implode(' ', $service_slugs);
                    @endphp
                    <div class="case-study-item border border-[#CFD5E3] rounded-3xl overflow-hidden" data-services="{{ $servicesData }}">
                        @if( has_post_thumbnail() )
                            <img class="w-full h-48 object-cover" src="{{ get_the_post_thumbnail_url() }}" alt="{{ get_the_title() }}">
                        @endif
                        <div class="flex flex-col items-start p-6">
                            <div class="flex flex-wrap gap-2 items-center mb-4">
                                @if( $primary_industry )
                                    <span class="tag block text-[#566276] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#E8EBF3] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                        {!! $primary_industry->name !!}
                                    </span>
                                @endif
                                @if( $primary_service )
                                    <span class="tag block text-[#566276] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#E8EBF3] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                        {!! $primary_service->name !!}
                                    </span>
                                @endif
                            </div>
                            <x-title class="mb-1" variation="small">{!! get_the_title() !!}</x-title>
                            <div class="mb-4 text-[#3F4A5A] text-bodySmall lg:text-bodySmallDesktop dynamic-clamp clamp-3">
                                {!! get_the_excerpt() !!}
                            </div>
                            <x-button
                                type="link"
                                variant="secondary"
                                size="extra-small"
                                icon="link"
                                :href="get_permalink()"
                            >
                                Read More
                            </x-button>
                        </div>
                    </div>
                @endwhile
                @php wp_reset_postdata(); @endphp
            </div>
            <div class="flex justify-center items-center mt-6 lg:mt-12">
                <x-button
                    id="load"
                    type="button"
                    variant="secondary"
                    size="small"
                    :href="'#'"
                >
                    Load more stories
                </x-button>
            </div>
        @else
            <p>No case studies found.</p>
        @endif
    </div>
</div>
