import React from 'react';

/**
 * Main container for block editors
 *
 * This component adds styling to the block editor without interfering with WordPress block functionality.
 * It preserves all the original block props to ensure proper selection and toolbar functionality.
 *
 * @param {Object} props Component props including blockProps from useBlockProps()
 * @param {React.ReactNode} props.children Child components
 * @param {string} props.blockTitle Title of the block to display
 * @returns {JSX.Element} EditorContainer component
 */
const EditorContainer = ({ children, blockTitle, ...blockProps }) => {
    return (
        <div {...blockProps} className={`${blockProps.className || ''} font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30`}>
            {blockTitle && (
                <div className="mb-4 border-b border-neutral-30">
                    <h2 className="text-2xl font-bold text-neutral-90">{blockTitle}</h2>
                </div>
            )}
            {children}
        </div>
    );
};

export default EditorContainer;
