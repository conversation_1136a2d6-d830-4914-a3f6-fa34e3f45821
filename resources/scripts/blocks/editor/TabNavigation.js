import React from 'react';

/**
 * Tab navigation component for managing multiple items
 *
 * @param {Object} props Component props
 * @param {Array} props.items Array of items
 * @param {number} props.activeTab Index of the active tab
 * @param {Function} props.onTabChange Callback for tab change
 * @param {Function} props.onAddItem Callback for adding a new item
 * @param {Function} props.onRemoveItem Callback for removing the active item
 * @param {Function} props.getItemTitle Function to get the title of an item
 * @param {string} props.itemName Name of the item type (e.g., "Item", "Slide", "Logo")
 * @param {string} props.addButtonTitle Title for the add button
 * @returns {JSX.Element} TabNavigation component
 */
const TabNavigation = ({
    items,
    activeTab,
    onTabChange,
    onAddItem,
    onRemoveItem,
    getItemTitle = (item, index) => item.title && item.title.trim().length > 0 ? item.title : `Item ${index + 1}`,
    itemName = 'Item',
    addButtonTitle = 'Add New Item'
}) => {
    return (
        <div className="mb-6">
            <div className="mb-3">
                <h3 className="text-sm font-medium text-neutral-70">{itemName}s</h3>
            </div>

            <div className="p-3 bg-white rounded-md border border-neutral-30 mb-3">
                <div className="flex flex-wrap gap-2 mb-2 items-center">
                    {items.map((item, index) => {
                        const displayTitle = getItemTitle(item, index);

                        return (
                            <button
                                key={index}
                                className={`border-0 py-2 px-4 inline-flex items-center text-sm font-medium rounded-lg transition-all ${
                                    index === activeTab
                                        ? 'text-white bg-primary-80 shadow-sm'
                                        : 'cursor-pointer text-neutral-70 hover:text-primary-80 bg-neutral-20 hover:bg-neutral-30'
                                }`}
                                onClick={() => onTabChange(index)}
                            >
                                <span>{displayTitle}</span>
                            </button>
                        );
                    })}
                    <button
                        className="border-0 bg-neutral-20 hover:bg-neutral-30 py-2 px-4 inline-flex items-center justify-center text-sm font-medium text-neutral-70 hover:text-primary-80 rounded-lg cursor-pointer transition-all w-9 h-9"
                        onClick={onAddItem}
                        title={addButtonTitle}
                    >
                        +
                    </button>
                </div>

                <div className="flex justify-between items-center">
                    <div className="text-xs text-neutral-60">{items.length} {items.length === 1 ? itemName.toLowerCase() : itemName.toLowerCase() + 's'}</div>

                    {items.length > 1 && (
                        <button
                            className="bg-transparent border-0 p-0 inline-flex items-center text-xs font-medium text-red-600 hover:text-red-700 cursor-pointer transition-all"
                            onClick={onRemoveItem}
                        >
                            <span className="dashicons dashicons-trash text-xs mr-1" style={{ fontSize: '14px' }}></span>
                            Remove {itemName}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TabNavigation;
