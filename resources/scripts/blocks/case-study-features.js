import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import {
    Panel,
    PanelBody
} from '@wordpress/components';
import { useState } from '@wordpress/element';
import { commonAttributes } from './common';
import {
    BlockSettings,
    TabNavigation,
    ItemEditor,
    TextControl
} from './editor';

const DEFAULT_FEATURE = {
    featureTitle: '',
    featureDescription: '',
};

registerBlockType('sage/case-study-features', {
    apiVersion: 2,
    title: 'Case Study: Features',
    icon: 'list-view',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        icon: {
            type: 'string',
        },
        accentColor: {
            type: 'string',
        },
        items: {
            type: 'array',
            default: [ DEFAULT_FEATURE ],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, icon, accentColor, items } = attributes;
        const [ activeTab, setActiveTab ] = useState(0);

        const handleItemChange = ( index, field, value ) => {
            const newItems = [ ...items ];
            newItems[ index ][ field ] = value;
            setAttributes({ items: newItems });
        };

        const addItem = () => {
            const newItems = [
                ...items,
                { ...DEFAULT_FEATURE },
            ];
            setAttributes({ items: newItems });
            setActiveTab( newItems.length - 1 );
        };

        const removeItem = ( index ) => {
            const newItems = [ ...items ];
            newItems.splice( index, 1 );
            setAttributes({ items: newItems });
            setActiveTab( Math.max( activeTab - 1, 0 ) );
        };

        return (
            <div {...blockProps}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Case Study: Features</h2>
                    </div>
                    <BlockSettings
                        title={title}
                        onTitleChange={(val) => setAttributes({ title: val })}
                        icon={icon}
                        onIconSelect={(url) => setAttributes({ icon: url })}
                        onIconRemove={() => setAttributes({ icon: '' })}
                        accentColor={accentColor}
                        onAccentColorChange={(color) => setAttributes({ accentColor: color })}
                    />

                    <TabNavigation
                        items={items}
                        activeTab={activeTab}
                        onTabChange={setActiveTab}
                        onAddItem={addItem}
                        onRemoveItem={() => removeItem(activeTab)}
                        getItemTitle={(item, index) => item.featureTitle && item.featureTitle.trim().length > 0 ? item.featureTitle : `Feature ${index + 1}`}
                        itemName="Feature"
                        addButtonTitle="Add New Feature"
                    />

                    { items.length > 0 && (
                        <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                            <Panel className="border-0 shadow-none">
                                <PanelBody
                                    title={`Feature Details: "${items[activeTab].featureTitle || `Feature ${activeTab + 1}`}"`}
                                    initialOpen={true}
                                >
                                    <ItemEditor
                                        itemIndex={activeTab}
                                        onRemove={() => removeItem(activeTab)}
                                        itemName="Feature"
                                        showRemoveButton={items.length > 1}
                                    >
                                        <TextControl
                                            label="Feature Title"
                                            value={items[activeTab].featureTitle}
                                            onChange={(val) => handleItemChange(activeTab, 'featureTitle', val)}
                                            placeholder="Enter feature title"
                                            tagName="h3"
                                        />

                                        <TextControl
                                            label="Feature Description"
                                            value={items[activeTab].featureDescription}
                                            onChange={(val) => handleItemChange(activeTab, 'featureDescription', val)}
                                            placeholder="Enter feature description"
                                            tagName="div"
                                            multiline={true}
                                        />
                                    </ItemEditor>
                                </PanelBody>
                            </Panel>
                        </div>
                    ) }
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        const { title, icon, accentColor, items } = attributes;

        return (
            <div { ...blockProps }>
                <div style={ { display: 'none' } } data-accent-color={ accentColor }>
                    <RichText.Content tagName="h2" value={ title } />
                    { icon && <img src={ icon } alt="Case Study Icon" /> }
                    { items.map( ( item, index ) => (
                        <div key={ `item-${ index }` }>
                            <RichText.Content tagName="h3" value={ item.featureTitle } />
                            <RichText.Content tagName="p" value={ item.featureDescription } />
                        </div>
                    ) ) }
                </div>
                <div data-dynamic="sage/case-study-features"></div>
            </div>
        );
    },
});
