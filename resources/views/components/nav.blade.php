@php
    $locations = get_nav_menu_locations();
    $menu_id = $locations['primary_navigation'] ?? null;
    $menu_items = $menu_id ? wp_get_nav_menu_items($menu_id) : [];
    $menuItemsByParent = collect($menu_items)->groupBy('menu_item_parent');
@endphp

@if($menu_items)
    <ul class="menu-container overflow-hidden h-0 max-h-[calc(100vh_-_72px)] px-6 bg-white absolute left-0 top-[72px] flex flex-col items-start w-full shadow-fade lg:shadow-none lg:gap-6 lg:px-0 lg:py-0 lg:static lg:flex-row lg:w-auto lg:max-h-none lg:h-auto xl:overflow-visible" style="transition:all 0.3s ease-in-out;">
        @foreach($menuItemsByParent->get(0, []) as $item)
        @php
            $submenu_type = get_post_meta($item->ID, '_menu_item_submenu_type', true);
            $childItems = $menuItemsByParent->get($item->ID, []);
        @endphp

        <li class="@if($childItems) cursor-pointer menu-expandable menu-item-has-children group @endif relative [&:first-child>svg]:top-[2px] [&:first-child>a]:pt-0 [&:last-child>a]:pb-4 overflow-hidden w-full text-bodySmall lg:[&:last-child>a]:pb-0 lg:w-fit lg:static lg:text-bodySmallDesktop text-[#454545] lg:flex lg:pb-5 lg:pt-6 xl:relative xl:overflow-visible">
            <a class="w-full inline-flex py-3 mr-0.5 lg:inline lg:py-0" href="{{ $item->url }}">{{ $item->title }}</a>
            @if($childItems) <i class="text-bodySmall absolute right-0 top-[14px] w-4 h-4 ml-0.5 transition-all duration-700 lg:relative lg:w-3 lg:h-3 lg:top-0.5 lg:group-hover:rotate-180 lg:group-hover:top-[3px] fa-regular fa-chevron-down"></i> @endif
            @if($childItems && $submenu_type)
                <div class="submenu-container w-full max-h-0 overflow-hidden top-[64px] flex group-hover:visible group-hover:opacity-100 lg:h-auto lg:overflow-visible lg:pt-10 lg:opacity-0 lg:invisible lg:absolute lg:left-1/2 lg:-translate-x-1/2 xl:left-0 xl:translate-x-0" style="transition:all 0.3s ease-in-out;">
                    @include('components.submenu', [
                        'parent'   => $item,
                        'type'     => $submenu_type,
                        'children' => $childItems,
                    ])
                </div>
            @endif
        </li>
        @endforeach
    </ul>
@endif
