@php
    $title = $attributes['title'] ?? '';
    $link = $attributes['buttonLink'] ?? '#';
@endphp

<div class="simple-cta-section relative flex justify-center items-center mb-8">
    <div class="container lg:max-w-[856px] lg:m-auto">
        <a href="{{ $link }}" target="_blank" rel="noopener noreferrer" class="block w-full p-6 flex flex-col justify-between items-start relative overflow-hidden lg:rounded-3xl" style="background: linear-gradient(to right, #90ceec, #7ac4e8);">
            {{-- Noise texture overlay --}}
            <div class="absolute inset-0 z-0 opacity-20 bg-repeat" style="background-image: url('{{ asset('images/noise.svg') }}');"></div>

            @if(!empty($title))
                <div class="rounded-full text-white border border-white w-8 h-8 mb-6 flex items-center justify-center lg:mb-10 relative z-10">
                    <i class="fa-light fa-arrow-up-right text-base"></i>
                </div>
                <x-title variation="large" class="text-white relative z-10">{!! $title !!}</x-title>
            @endif
        </a>
    </div>
</div>
