import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import { Button } from '@wordpress/components';
import { Fragment, useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor
} from './editor';

registerBlockType('sage/accordion', {
    apiVersion: 2,
    title: 'Accordion',
    icon: 'list-view',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        items: {
            type: 'array',
            default: [
                {
                    title: '',
                    subtitle: '',
                    image: '',
                    description: '',
                    listTitle: '',
                    listItems: [],
                    advantages: [],
                },
            ],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { title, subtitle, items } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        const handleItemChange = (index, field, value) => {
            const updatedItems = [...items];
            updatedItems[index][field] = value;
            setAttributes({ items: updatedItems });
        };

        const handleListChange = (index, field, subIndex, value) => {
            const updatedItems = [...items];
            updatedItems[index][field][subIndex] = value;
            setAttributes({ items: updatedItems });
        };

        const addItemToList = (index, field) => {
            const updatedItems = [...items];
            updatedItems[index][field].push('');
            setAttributes({ items: updatedItems });
        };

        const removeItemFromList = (index, field, subIndex) => {
            const updatedItems = [...items];
            updatedItems[index][field].splice(subIndex, 1);
            setAttributes({ items: updatedItems });
        };

        const addAccordionItem = () => {
            const newItems = [...items, {
                title: '',
                subtitle: '',
                image: '',
                description: '',
                listTitle: '',
                listItems: [],
                advantages: [],
            }];
            setAttributes({ items: newItems });
            setActiveTab(newItems.length - 1);
        };

        const removeAccordionItem = (index) => {
            const updatedItems = [...items];
            updatedItems.splice(index, 1);
            setAttributes({ items: updatedItems });
            setActiveTab(Math.max(activeTab - 1, 0));
        };

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Accordion</h2>
                        </div>

                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter accordion title"
                            tagName="h2"
                        />

                        <TextControl
                            label="Subtitle"
                            value={subtitle}
                            onChange={(value) => setAttributes({ subtitle: value })}
                            placeholder="Enter accordion subtitle"
                            tagName="p"
                        />

                        <div className="mb-6 mt-6">
                            <TabNavigation
                                items={items}
                                activeTab={activeTab}
                                onTabChange={setActiveTab}
                                onAddItem={addAccordionItem}
                                onRemoveItem={() => removeAccordionItem(activeTab)}
                                getItemTitle={(item, index) => item.title && item.title.trim().length > 0 ? item.title : `Item ${index + 1}`}
                                itemName="Accordion Item"
                                addButtonTitle="Add New Item"
                            />

                            {items.length > 0 && (
                                <ItemEditor
                                    itemIndex={activeTab}
                                    onRemove={() => removeAccordionItem(activeTab)}
                                    itemName="Accordion Item"
                                    showRemoveButton={items.length > 1}
                                >
                                    <TextControl
                                        label="Item Title"
                                        value={items[activeTab].title}
                                        onChange={(value) => handleItemChange(activeTab, 'title', value)}
                                        placeholder="Enter item title"
                                        tagName="h4"
                                    />

                                    <TextControl
                                        label="Item Subtitle"
                                        value={items[activeTab].subtitle}
                                        onChange={(value) => handleItemChange(activeTab, 'subtitle', value)}
                                        placeholder="Enter item subtitle"
                                        tagName="p"
                                    />

                                    <ImageUploader
                                        image={items[activeTab].image}
                                        onSelect={(url) => handleItemChange(activeTab, 'image', url)}
                                        onRemove={() => handleItemChange(activeTab, 'image', '')}
                                        label="Item Image"
                                        description="Upload an image for this accordion item."
                                        height={48}
                                        objectFit="cover"
                                        altText="Accordion Item Image"
                                    />

                                    <TextControl
                                        label="Item Description"
                                        value={items[activeTab].description}
                                        onChange={(value) => handleItemChange(activeTab, 'description', value)}
                                        placeholder="Enter item description"
                                        tagName="p"
                                        multiline={true}
                                    />

                                    <TextControl
                                        label="List Title"
                                        value={items[activeTab].listTitle}
                                        onChange={(value) => handleItemChange(activeTab, 'listTitle', value)}
                                        placeholder="Enter list title"
                                        tagName="h5"
                                    />

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">List Items</label>
                                        <div className="p-3 bg-white rounded-md border border-neutral-30">
                                            {items[activeTab].listItems.length === 0 && (
                                                <p className="text-neutral-60 text-sm mb-2">No list items added yet.</p>
                                            )}

                                            {items[activeTab].listItems.map((listItem, i) => (
                                                <div key={i} className="flex items-center gap-2 mb-2 last:mb-0">
                                                    <div className="flex-grow p-2 bg-neutral-10 rounded-md border border-neutral-30">
                                                        <RichText
                                                            tagName="span"
                                                            placeholder="Enter list item"
                                                            value={listItem}
                                                            onChange={(value) => handleListChange(activeTab, 'listItems', i, value)}
                                                            className="text-sm"
                                                            inlineToolbar
                                                            allowedFormats={['core/bold']}
                                                        />
                                                    </div>
                                                    <Button
                                                        className="p-1 text-xs bg-red-50 border border-red-300 text-red-600 rounded"
                                                        onClick={() => removeItemFromList(activeTab, 'listItems', i)}
                                                        icon="trash"
                                                        iconSize={16}
                                                        aria-label="Remove list item"
                                                    />
                                                </div>
                                            ))}

                                            <Button
                                                className="mt-2 border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 hover:text-primary-80 py-1.5 px-3 rounded-md transition-all text-sm flex items-center"
                                                onClick={() => addItemToList(activeTab, 'listItems')}
                                                icon="plus-alt2"
                                            >
                                                Add List Item
                                            </Button>
                                        </div>
                                        <p className="mt-1 text-xs text-neutral-60">Add items to display in a list format</p>
                                    </div>

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">Advantages</label>
                                        <div className="p-3 bg-white rounded-md border border-neutral-30">
                                            {items[activeTab].advantages.length === 0 && (
                                                <p className="text-neutral-60 text-sm mb-2">No advantages added yet.</p>
                                            )}

                                            {items[activeTab].advantages.map((advantage, i) => (
                                                <div key={i} className="flex items-center gap-2 mb-2 last:mb-0">
                                                    <div className="flex-grow p-2 bg-neutral-10 rounded-md border border-neutral-30">
                                                        <RichText
                                                            tagName="span"
                                                            placeholder="Enter advantage"
                                                            value={advantage}
                                                            onChange={(value) => handleListChange(activeTab, 'advantages', i, value)}
                                                            className="text-sm"
                                                            inlineToolbar
                                                            allowedFormats={['core/bold']}
                                                        />
                                                    </div>
                                                    <Button
                                                        className="p-1 text-xs bg-red-50 border border-red-300 text-red-600 rounded"
                                                        onClick={() => removeItemFromList(activeTab, 'advantages', i)}
                                                        icon="trash"
                                                        iconSize={16}
                                                        aria-label="Remove advantage"
                                                    />
                                                </div>
                                            ))}

                                            <Button
                                                className="mt-2 border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 hover:text-primary-80 py-1.5 px-3 rounded-md transition-all text-sm flex items-center"
                                                onClick={() => addItemToList(activeTab, 'advantages')}
                                                icon="plus-alt2"
                                            >
                                                Add Advantage
                                            </Button>
                                        </div>
                                        <p className="mt-1 text-xs text-neutral-60">Add advantages to highlight key benefits</p>
                                    </div>
                                </ItemEditor>
                            )}
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" value={attributes.subtitle} />

                    {attributes.items?.map((item, index) => (
                        <div key={`accordion-${index}`}>
                            <RichText.Content tagName="h4" value={item.title} />
                            <RichText.Content tagName="p" value={item.subtitle} />

                            {item.image && (
                                <img
                                    src={item.image}
                                    alt={item.title ? `${item.title} - Accordion Image` : "Accordion Image"}
                                />
                            )}

                            <RichText.Content tagName="p" value={item.description} />
                            <RichText.Content tagName="h5" value={item.listTitle} />

                            <ul>
                                {item.listItems?.map((listItem, i) => (
                                    <li key={`listItem-${index}-${i}`}>
                                        <RichText.Content tagName="span" value={listItem} />
                                    </li>
                                ))}
                            </ul>

                            <h4>Advantages</h4>
                            <ul>
                                {item.advantages?.map((advantage, i) => (
                                    <li key={`advantage-${index}-${i}`}>
                                        <RichText.Content tagName="span" value={advantage} />
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/accordion"></div>
            </div>
        );
    }
});
