<div class="internship-benefits-section flex justify-center items-center">
    <div class="container px-5 py-8 lg:py-[62px]">
        <div class="flex flex-col lg:flex-row lg:gap-16">
            <div class="lg:w-1/2 mb-8 lg:mb-0">
                @if(!empty($attributes['title']))
                    <x-title variation="large" class="mb-4">{!! $attributes['title'] !!}</x-title>
                @endif
                @if(!empty($attributes['description']))
                    <p class="lg:max-w-[470px]">{!! $attributes['description'] !!}</p>
                @endif
            </div>
            
            @if(!empty($attributes['benefits']) && count($attributes['benefits']) > 0)
                <div class="lg:w-1/2 flex flex-col gap-3 lg:max-w-[416px]">
                    @foreach($attributes['benefits'] as $benefit)
                        <div class="benefit-item border border-solid border-[#E8EBF3] rounded-[20px] p-4 flex gap-4">
                            <div class="icon-container relative w-9 h-9 flex-shrink-0">
                                <div class="absolute inset-0 rounded-[8px]" style="
                                    background: linear-gradient(54.97deg, #CCEFFF -1.61%, #CCEFFF 53.98%, #D7F4E1 100%);
                                    padding: 3px;
                                    z-index: 1;
                                ">
                                    <div class="w-full h-full bg-white/80 rounded-md flex items-center justify-center">
                                        @if(!empty($benefit['icon']))
                                            <img src="{{ $benefit['icon'] }}" alt="" class="w-6 h-6 object-contain">
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="benefit-content">
                                @if(!empty($benefit['title']))
                                    <span class="block text-bodyMedium font-normal lg:text-bodyMediumDesktop">{!! $benefit['title'] !!}</span>
                                @endif
                                @if(!empty($benefit['description']))
                                    <p class="text-bodySmall lg:text-bodySmallDesktop text-[#566276]">{!! $benefit['description'] !!}</p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
