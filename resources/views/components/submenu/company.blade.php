@php
$menuName = 'primary_navigation';
$menuLocations = get_nav_menu_locations();
$menuId = $menuLocations[$menuName] ?? 0;
$allItems = wp_get_nav_menu_items($menuId) ?? [];
$company = null;
$imageParent = null;
$linksParent = null;
$ctaParent = null;

foreach ($allItems as $item) {
    $submenuType = get_post_meta($item->ID, '_menu_item_submenu_type', true);
    if ($item->menu_item_parent == 0 && $submenuType === 'company') {
        $company = $item;
        break;
    }
}

if ($company) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $company->ID) {
            if ($child->title === 'Image') {
                $imageParent = $child;
            } elseif ($child->title === 'Links') {
                $linksParent = $child;
            } elseif ($child->title === 'CTA') {
                $ctaParent = $child;
            }
        }
    }
}

$imageItems = [];
if ($imageParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $imageParent->ID) {
            $imageItems[] = $child;
        }
    }
}

$linksSections = [];
if ($linksParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $linksParent->ID) {
            $linksSections[] = $child;
        }
    }
}

$ctaItems = [];
if ($ctaParent) {
    foreach ($allItems as $child) {
        if ($child->menu_item_parent == $ctaParent->ID) {
            $ctaItems[] = $child;
        }
    }
}
@endphp

<div class="submenu w-full cursor-default top-2.5 flex flex-col gap-6 bg-white rounded-max lg:w-[882px] lg:flex-row lg:p-4 lg:border lg:border-[#E8EBF3] lg:shadow-fade lg:gap-4 lg:absolute lg:left-1/2 lg:-translate-x-1/2 xl:left-0 xl:translate-x-0">
    <div class="relative flex items-end lg:w-[268px]">
        @if(!empty($imageItems))
            @php
                $imgItem = $imageItems[0];
                $imgSrc = '';
                $imgId = get_post_meta($imgItem->ID, '_menu_item_image_id', true);
                if ($imgId) {
                    $imgSrc = wp_get_attachment_url($imgId) ?: '';
                }
            @endphp
            <img class="rounded-3xl overflow-hidden object-cover w-full h-full absolute inset-0 z-0" src="{{ $imgSrc }}" />
            <div class="relative flex flex-col z-20 p-6">
                <span class="text-bodyLarge text-white lg:text-bodyLargeDesktop">
                    {{ $imgItem->title }}
                </span>
                <span class="text-bodySmall text-white lg:text-bodySmallDesktop">
                    {{ $imgItem->description }}
                </span>
            </div>
        @endif
        <div class="rounded-3xl overflow-hidden absolute w-full h-full left-0 bottom-0 pointer-events-none z-10 bg-[linear-gradient(180deg,rgba(1,101,159,0)_0%,#042031_100%)]"></div>
    </div>
    <div class="flex flex-col gap-6 lg:gap-8 lg:w-[568px]">
        <div class="flex flex-wrap gap-y-4 lg:block lg:columns-3 lg:gap-3">
            @foreach($linksSections as $section)
                @php
                    $subLinks = [];
                    foreach ($allItems as $child) {
                        if ($child->menu_item_parent == $section->ID) {
                            $subLinks[] = $child;
                        }
                    }
                @endphp
                <div class="w-1/2 sm:w-1/3 flex flex-col lg:w-fit">
                    <x-title variation="subtitle" level="span" class="mb-4">
                        <span class="text-bodySmallDesktop lg:text-subtitleDesktop">{{ $section->title }}</span>
                    </x-title>
                    <ul>
                        @foreach($subLinks as $link)
                            <li class="text-bodySmallDesktop">
                                <a href="{{ $link->url }}">{{ $link->title }}</a>
                            </li>
                        @endforeach
                    </ul>
                </div>
            @endforeach
        </div>
        <div class="flex flex-col items-start justify-between blue-bg rounded-3xl overflow-hidden p-6 lg:flex-row">
            @if(!empty($ctaItems))
                @php
                    $ctaItem = $ctaItems[0];
                    $ctaTitle = get_post_meta($ctaItem->ID, '_menu_item_company_cta_title', true) ?: '';
                @endphp
                <div class="mb-4 max-w-80 flex flex-col lg:mb-0">
                    <span class="text-bodyLarge text-[#222831] font-normal lg:text-bodyLargeDesktop">
                        {{ $ctaTitle }}
                    </span>
                    <span class="text-bodySmall pr-4 text-[#566276] lg:text-bodySmallDesktop">
                        {{ $ctaItem->description }}
                    </span>
                </div>
                <x-button
                    type="link"
                    variant="secondary"
                    size="extra-small"
                    icon="link"
                    :href="$ctaItem->url"
                >
                    {{ $ctaItem->title }}
                </x-button>
            @endif
        </div>
    </div>
</div>
