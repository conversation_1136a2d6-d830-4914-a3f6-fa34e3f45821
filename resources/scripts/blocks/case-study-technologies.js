import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import {
    Button,
    Panel,
    PanelBody,
} from '@wordpress/components';
import { useState, useEffect } from '@wordpress/element';
import { commonAttributes } from './common';
import {
    BlockSettings,
    TabNavigation,
    TextControl
} from './editor';

const DEFAULT_SECTION = {
    sectionTitle: '',
    images: [],
    techIcons: [],
};

registerBlockType('sage/case-study-technologies', {
    apiVersion: 2,
    title: 'Case Study: Technologies',
    icon: 'admin-tools',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        icon: {
            type: 'string',
        },
        accentColor: {
            type: 'string',
        },
        sections: {
            type: 'array',
            default: [],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, icon, accentColor, sections } = attributes;
        const [ activeSection, setActiveSection ] = useState( 0 );
        const [ availableTechIcons, setAvailableTechIcons ] = useState( [] );
        const [ iconSearchTerm, setIconSearchTerm ] = useState( '' );
        const [ showAllIcons, setShowAllIcons ] = useState( false );

        // Load available technology icons from WordPress options
        useEffect(() => {
            console.log('Technology Icons Data:', window.technologyIconsData);

            // Check if we have the data in the window object
            if (window.technologyIconsData && window.technologyIconsData.icons) {
                console.log('Icons found in window object:', window.technologyIconsData.icons);
                setAvailableTechIcons(window.technologyIconsData.icons);
            } else {
                console.log('No technology icons data found in window object');

                // Fallback: Try to fetch the data via AJAX
                fetch('/wp-json/wp/v2/settings/technology_icons')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Fetched technology icons:', data);
                        if (data && Array.isArray(data)) {
                            setAvailableTechIcons(data);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching technology icons:', error);

                        // Hardcoded fallback for testing
                        const hardcodedIcons = [
                            {
                                id: 1055,
                                name: "asfdasdf",
                                url: "http://abh.local/wp-content/uploads/2025/04/icon-22.svg"
                            },
                            {
                                id: 1034,
                                name: "342rewfd",
                                url: "http://abh.local/wp-content/uploads/2025/04/icon-15.svg"
                            }
                        ];
                        console.log('Using hardcoded icons for testing:', hardcodedIcons);
                        setAvailableTechIcons(hardcodedIcons);
                    });
            }
        }, []);

        const handleSectionTitleChange = ( index, value ) => {
            const newSections = [ ...sections ];
            newSections[ index ].sectionTitle = value;
            setAttributes({ sections: newSections });
        };

        const addSection = () => {
            const newSections = [ ...sections, { ...DEFAULT_SECTION } ];
            setAttributes({ sections: newSections });
            setActiveSection( newSections.length - 1 );
        };

        const removeSection = ( index ) => {
            const newSections = [ ...sections ];
            newSections.splice( index, 1 );
            setAttributes({ sections: newSections });
            setActiveSection( Math.max( activeSection - 1, 0 ) );
        };

        const addInnerImages = ( sectionIndex, mediaArray, mode = 'replace' ) => {
            const newImages = mediaArray.map( media => media.url );
            const newSections = [ ...sections ];

            if (mode === 'append' && newSections[sectionIndex].images) {
                // Add new images to existing ones
                newSections[sectionIndex].images = [...newSections[sectionIndex].images, ...newImages];
            } else {
                // Replace all images
                newSections[sectionIndex].images = newImages;
            }

            setAttributes({ sections: newSections });
        };

        const clearAllLegacyImages = (sectionIndex) => {
            const newSections = [...sections];
            newSections[sectionIndex].images = [];
            setAttributes({ sections: newSections });
        };

        const removeInnerImage = ( sectionIndex, imageIndex ) => {
            const newSections = [ ...sections ];
            newSections[ sectionIndex ].images.splice( imageIndex, 1 );
            setAttributes({ sections: newSections });
        };

        const toggleTechIcon = ( sectionIndex, iconId ) => {
            const newSections = [ ...sections ];
            if (!newSections[sectionIndex].techIcons) {
                newSections[sectionIndex].techIcons = [];
            }

            const iconIndex = newSections[sectionIndex].techIcons.indexOf(iconId);

            if (iconIndex === -1) {
                // Add the icon
                newSections[sectionIndex].techIcons.push(iconId);
            } else {
                // Remove the icon
                newSections[sectionIndex].techIcons.splice(iconIndex, 1);
            }

            setAttributes({ sections: newSections });
        };

        const isTechIconSelected = ( sectionIndex, iconId ) => {
            if (!sections[sectionIndex] || !sections[sectionIndex].techIcons) {
                return false;
            }
            return sections[sectionIndex].techIcons.includes(iconId);
        };

        if ( sections.length === 0 ) {
            addSection();
        }

        const currentSection = sections[ activeSection ] || { sectionTitle: '', images: [] };

        return (
            <div {...blockProps}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Case Study: Technologies</h2>
                    </div>
                    <BlockSettings
                        title={title}
                        onTitleChange={(val) => setAttributes({ title: val })}
                        icon={icon}
                        onIconSelect={(url) => setAttributes({ icon: url })}
                        onIconRemove={() => setAttributes({ icon: '' })}
                        accentColor={accentColor}
                        onAccentColorChange={(color) => setAttributes({ accentColor: color })}
                    />

                    <TabNavigation
                        items={sections}
                        activeTab={activeSection}
                        onTabChange={setActiveSection}
                        onAddItem={addSection}
                        onRemoveItem={() => removeSection(activeSection)}
                        getItemTitle={(section, index) => section.sectionTitle ? section.sectionTitle : `Section ${index + 1}`}
                        itemName="Section"
                        addButtonTitle="Add New Section"
                    />
                    <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                        <div className="mb-4 pb-3 border-b border-neutral-20">
                            <TextControl
                                label="Section Title"
                                value={ currentSection.sectionTitle }
                                onChange={ ( val ) => handleSectionTitleChange( activeSection, val ) }
                                placeholder="Enter section title"
                                tagName="h3"
                            />
                        </div>
                        <Panel className="border-0 shadow-none">
                            <PanelBody
                                title={`Technology Icons for "${currentSection.sectionTitle || `Section ${activeSection + 1}`}"`}
                                initialOpen={true}
                            >

                                {/* Preview of selected icons */}
                                {currentSection.techIcons && currentSection.techIcons.length > 0 && (
                                    <div className="mb-6">
                                        <h4 className="text-sm font-medium text-neutral-70 mb-3">Selected Icons for this Section</h4>
                                        <div className="flex flex-wrap gap-3 p-4 bg-white rounded-lg border border-neutral-30">
                                            {currentSection.techIcons.map((iconId, idx) => {
                                                const icon = availableTechIcons.find(i => i.id === iconId);
                                                if (!icon) return null;

                                                return (
                                                    <div key={idx} className="relative group">
                                                        <div className="technology-icon-preview flex items-center justify-center w-16 h-16 bg-white rounded-lg overflow-hidden border border-neutral-30 group-hover:border-primary-70 transition-all shadow-sm">
                                                            <img src={icon.url} alt={icon.name} className="object-contain w-10 h-10" />
                                                        </div>
                                                        <button
                                                            className="absolute -top-1 -right-1 cursor-pointer text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity bg-white border-0 rounded-md shadow-sm"
                                                            onClick={() => toggleTechIcon(activeSection, iconId)}
                                                            title="Remove Icon"
                                                            style={{ fontSize: '18px', lineHeight: 1, paddingBottom: 0, boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}
                                                        >
                                                            ×
                                                        </button>
                                                        <div className="text-center mt-1 text-xs text-neutral-60 truncate max-w-[64px]">{icon.name}</div>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>
                                )}

                                <div className="mb-4">
                                    <div className="flex justify-between items-center mb-3">
                                        <h4 className="text-sm font-medium text-neutral-70">Available Technology Icons</h4>
                                        <div className="text-xs text-neutral-60">{availableTechIcons.length} icons</div>
                                    </div>

                                    {availableTechIcons && availableTechIcons.length > 0 ? (
                                        <>
                                            <div className="mb-3 relative">
                                                <input
                                                    type="text"
                                                    className="w-full py-2 px-3 pr-8 border border-neutral-30 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary-70 focus:border-primary-70"
                                                    placeholder="Search icons..."
                                                    value={iconSearchTerm}
                                                    onChange={(e) => setIconSearchTerm(e.target.value)}
                                                />
                                                {iconSearchTerm && (
                                                    <button
                                                        className="absolute bg-transparent border-none cursor-pointer right-2 top-1/2 transform -translate-y-1/2 text-neutral-50 hover:text-neutral-70"
                                                        onClick={() => setIconSearchTerm('')}
                                                    >
                                                        ×
                                                    </button>
                                                )}
                                            </div>

                                            <div className="technology-icons-container p-4 bg-white rounded-lg border border-neutral-30 max-h-60 overflow-y-auto">
                                                {/* Filter icons based on search term */}
                                                {(() => {
                                                    const filteredIcons = iconSearchTerm
                                                        ? availableTechIcons.filter(icon =>
                                                            icon.name.toLowerCase().includes(iconSearchTerm.toLowerCase()))
                                                        : availableTechIcons;

                                                    // Determine if we should show all icons or just a subset
                                                    const displayIcons = !showAllIcons && filteredIcons.length > 20 && !iconSearchTerm
                                                        ? filteredIcons.slice(0, 20)
                                                        : filteredIcons;

                                                    if (filteredIcons.length === 0) {
                                                        return (
                                                            <div className="text-center py-4 text-neutral-60 text-sm">
                                                                No icons match your search
                                                            </div>
                                                        );
                                                    }

                                                    return (
                                                        <>
                                                            <div className="technology-icons-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(70px, 1fr))', gap: '10px' }}>
                                                                {displayIcons.map((icon, idx) => {
                                                                    const isSelected = isTechIconSelected(activeSection, icon.id);
                                                                    return (
                                                                        <div
                                                                            key={idx}
                                                                            className={`technology-icon-item relative group transition-all ${isSelected ? 'scale-105' : ''}`}
                                                                            onClick={() => toggleTechIcon(activeSection, icon.id)}
                                                                        >
                                                                            <div className={`flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all ${isSelected ? 'bg-primary-20 border border-primary-70' : 'bg-neutral-10 hover:bg-neutral-20 border border-transparent'}`}>
                                                                                <div className="w-10 h-10 flex items-center justify-center mb-1">
                                                                                    <img src={icon.url} alt={icon.name} className="max-w-full max-h-full object-contain" />
                                                                                </div>
                                                                                <p className="m-0 text-xs text-center truncate w-full">{icon.name}</p>
                                                                                {isSelected && (
                                                                                    <div className="absolute -top-1 -right-1 text-primary-80" style={{ fontSize: '14px' }}>
                                                                                        <span className="dashicons dashicons-yes-alt"></span>
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        </div>
                                                                    );
                                                                })}
                                                            </div>

                                                            {/* Show "Load More" button if needed */}
                                                            {!showAllIcons && filteredIcons.length > 20 && !iconSearchTerm && (
                                                                <div className="mt-3 text-center">
                                                                    <button
                                                                        className="text-primary-80 hover:text-primary-90 text-sm font-medium"
                                                                        onClick={() => setShowAllIcons(true)}
                                                                    >
                                                                        Show all {filteredIcons.length} icons
                                                                    </button>
                                                                </div>
                                                            )}
                                                        </>
                                                    );
                                                })()}
                                            </div>
                                        </>
                                    ) : (
                                        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800 text-sm">
                                            <p>No technology icons available. Please add some in <a href="/wp-admin/admin.php?page=theme-settings-technology-icons" target="_blank" className="text-primary-80 underline">Theme Settings &gt; Technology Icons</a>.</p>
                                        </div>
                                    )}
                                </div>
                            </PanelBody>

                            <PanelBody title="Custom Images (Legacy)" initialOpen={false}>
                                <div className="p-3 mb-4 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800 text-sm">
                                    <p>This is a legacy feature. It's recommended to use the Technology Icons from the <a href="/wp-admin/admin.php?page=theme-settings-technology-icons" target="_blank" className="text-primary-80 underline">theme settings</a> instead.</p>
                                </div>

                                <div className="mb-4">
                                    <div className="flex justify-between items-center mb-3">
                                        <h4 className="text-sm font-medium text-neutral-70">Custom Images</h4>
                                        {currentSection.images && currentSection.images.length > 0 && (
                                            <span className="text-xs text-neutral-60">{currentSection.images.length} images</span>
                                        )}
                                    </div>

                                    {currentSection.images && currentSection.images.length > 0 ? (
                                        <>
                                            <div className="flex flex-wrap gap-3 p-4 bg-white rounded-lg border border-neutral-30 mb-3">
                                                {currentSection.images.map((img, idx) => (
                                                    <div key={idx} className="relative group">
                                                        <div className="custom-image-preview flex items-center justify-center w-16 h-16 bg-white rounded-lg overflow-hidden border border-neutral-30 group-hover:border-red-300 transition-all shadow-sm">
                                                            <img src={img} alt="Custom Technology Image" className="object-contain w-10 h-10" />
                                                        </div>
                                                        <button
                                                            className="absolute -top-1 -right-1 cursor-pointer text-red-500 hover:text-red-700 opacity-0 group-hover:opacity-100 transition-opacity bg-white border-0 rounded-md shadow-sm"
                                                            onClick={() => removeInnerImage(activeSection, idx)}
                                                            title="Remove Image"
                                                            style={{ fontSize: '18px', lineHeight: 1, paddingBottom: 0, boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}
                                                        >
                                                            ×
                                                        </button>
                                                    </div>
                                                ))}
                                            </div>


                                            <div className="mb-4">
                                                <h4 className="text-sm font-medium text-neutral-70 mb-2">Add More Images</h4>
                                                <Button
                                                    onClick={() => {
                                                        // Open a new media upload dialog
                                                        const mediaUploader = window.wp.media({
                                                            title: 'Add More Legacy Images',
                                                            button: { text: 'Add Images' },
                                                            multiple: true,
                                                            library: { type: 'image' }
                                                        });

                                                        mediaUploader.on('select', function() {
                                                            const mediaArray = mediaUploader.state().get('selection').toJSON();
                                                            addInnerImages(activeSection, mediaArray, 'append');
                                                        });

                                                        mediaUploader.open();
                                                    }}
                                                    className="w-full justify-center border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 py-2 rounded-md transition-all"
                                                    icon="plus"
                                                >
                                                    Add More Images
                                                </Button>
                                            </div>

                                            <div className="flex gap-2 mb-3">
                                                <Button
                                                    onClick={() => {
                                                        // Just open a new media upload dialog
                                                        // We can't pre-select the images because we only have URLs, not IDs
                                                        const mediaUploader = window.wp.media({
                                                            title: 'Replace Legacy Images',
                                                            button: { text: 'Select Images' },
                                                            multiple: true,
                                                            library: { type: 'image' }
                                                        });

                                                        mediaUploader.on('select', function() {
                                                            const mediaArray = mediaUploader.state().get('selection').toJSON();
                                                            addInnerImages(activeSection, mediaArray, 'replace');
                                                        });

                                                        mediaUploader.open();
                                                    }}
                                                    className="flex-1 justify-center border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 py-1.5 rounded-md transition-all text-sm"
                                                    icon="edit"
                                                >
                                                    Replace Images
                                                </Button>

                                                <Button
                                                    onClick={() => clearAllLegacyImages(activeSection)}
                                                    className="border border-red-300 bg-white hover:bg-red-50 text-red-600 hover:text-red-700 py-1.5 rounded-md transition-all text-sm"
                                                    icon="trash"
                                                >
                                                    Clear All
                                                </Button>
                                            </div>
                                        </>
                                    ) : (
                                        <Button
                                            onClick={() => {
                                                // Open a new media upload dialog
                                                const mediaUploader = window.wp.media({
                                                    title: 'Upload Legacy Images',
                                                    button: { text: 'Select Images' },
                                                    multiple: true,
                                                    library: { type: 'image' }
                                                });

                                                mediaUploader.on('select', function() {
                                                    const mediaArray = mediaUploader.state().get('selection').toJSON();
                                                    addInnerImages(activeSection, mediaArray);
                                                });

                                                mediaUploader.open();
                                            }}
                                            className="w-full justify-center border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 py-2 rounded-md transition-all"
                                            icon="upload"
                                        >
                                            Upload Custom Images
                                        </Button>
                                    )}
                                </div>
                            </PanelBody>
                        </Panel>
                    </div>
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        const { title, icon, accentColor, sections } = attributes;

        return (
            <div { ...blockProps }>
                <div style={ { display: 'none' } } data-accent-color={ accentColor }>
                    <RichText.Content tagName="h2" value={ title } />
                    { icon && <img src={ icon } alt="Case Study Icon" /> }
                    { sections.map( ( section, index ) => (
                        <div key={ `section-${ index }` }>
                            <RichText.Content tagName="h3" value={ section.sectionTitle } />

                            {/* Store tech icons IDs */}
                            { section.techIcons && section.techIcons.map( ( iconId, iconIdx ) => (
                                <div key={ `tech-icon-${ iconIdx }` } className="tech-icon-reference" data-icon-id={ iconId }></div>
                            ) ) }

                            {/* Legacy custom images */}
                            { section.images && section.images.map( ( img, idx ) => (
                                <div key={ `img-${ idx }` }>
                                    { img && <img src={ img } alt="Technology Image" /> }
                                </div>
                            ) ) }
                        </div>
                    ) ) }
                </div>
                <div data-dynamic="sage/case-study-technologies"></div>
            </div>
        );
    },
});
