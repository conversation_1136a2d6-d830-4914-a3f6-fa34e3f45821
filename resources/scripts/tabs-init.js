import Swiper from 'swiper';
import { Autoplay, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

document.addEventListener('DOMContentLoaded', function () {
    const tabContainers = document.querySelectorAll('.tabs-section');
    if (!tabContainers.length) return;

    tabContainers.forEach((container) => {
        const buttons = container.querySelectorAll('.tabs-navigation button');
        const contents = container.querySelectorAll('.tab-content-item');
        const floatingBgLayers = container.querySelectorAll('.floating-bg-layer');

        let swiper;
        let tabsInitialized = false;

        function changeFloatingBgColor(tabIndex) {
            floatingBgLayers.forEach((layer, idx) => {
                idx === tabIndex
                    ? layer.classList.replace('opacity-0', 'opacity-100')
                    : layer.classList.replace('opacity-100', 'opacity-0');
            });
        }

        function handleTabs() {
            if (!buttons.length || !contents.length) return;
            if (tabsInitialized) return;
            tabsInitialized = true;

            buttons.forEach((btn, idx) => {
                idx === 0 ? btn.classList.add('bg-white') : btn.classList.add('bg-transparent');
            });

            contents.forEach((content, idx) => {
                if (idx === 0) {
                    content.classList.remove('hidden', 'opacity-0');
                    content.classList.add('opacity-100');
                } else {
                    content.classList.add('hidden', 'opacity-0');
                    content.classList.remove('opacity-100');
                }
            });

            buttons.forEach((button, idx) => {
                button.addEventListener('click', function () {
                    const tabIndex = this.getAttribute('data-tab-index');
                    buttons.forEach((btn) => btn.classList.replace('bg-white', 'bg-transparent'));
                    this.classList.replace('bg-transparent', 'bg-white');

                    contents.forEach((content) => {
                        if (content.getAttribute('data-tab-content') === tabIndex) {
                            setTimeout(() => {
                                content.classList.remove('hidden');
                                setTimeout(() => {
                                    content.classList.replace('opacity-0', 'opacity-100');
                                }, 500);
                            }, 500);
                        } else {
                            content.classList.replace('opacity-100', 'opacity-0');
                            setTimeout(() => content.classList.add('hidden'), 500);
                        }
                    });

                    changeFloatingBgColor(idx);
                });
            });
        }

        function initSwiper() {
            const swiperEl = container.querySelector('.swiper');
            const paginationEl = container.querySelector('.swiper-pagination');
            if (!swiperEl || !paginationEl) return;

            swiper = new Swiper(swiperEl, {
                modules: [Autoplay, Pagination],
                loop: true,
                autoplay: true,
                pagination: {
                    el: paginationEl,
                    clickable: true,
                },
            });

            swiper.on('slideChange', function () {
                changeFloatingBgColor(swiper.realIndex);
            });
        }

        function resetSlides() {
            const swiperWrapper = container.querySelector('.swiper-wrapper');
            if (swiperWrapper) {
                swiperWrapper.classList.remove('swiper-wrapper');
                swiperWrapper.querySelectorAll('.swiper-slide').forEach((slide) => {
                    slide.classList.remove('swiper-slide');
                    slide.removeAttribute('style');
                });
            }
            const tabsContent = container.querySelector('.tabs-content');
            if (tabsContent) {
                tabsContent.classList.remove('swiper');
                tabsContent.removeAttribute('style');
            }
            container.querySelectorAll('.swiper-initialized, .swiper-horizontal').forEach((el) => {
                el.classList.remove('swiper-initialized', 'swiper-horizontal');
            });
        }

        function handleResize() {
            const tabsContent = container.querySelector('.tabs-content');
            if (window.innerWidth < 768) {
                if (!swiper) {
                    if (tabsInitialized) {
                        buttons.forEach((button) => {
                            const newButton = button.cloneNode(true);
                            button.parentNode.replaceChild(newButton, button);
                        });
                        tabsInitialized = false;
                    }
                    if (tabsContent) {
                        tabsContent.classList.add('swiper');
                        const slidesContainer = tabsContent.querySelector('div');
                        if (!slidesContainer) return;
                        slidesContainer.classList.add('swiper-wrapper');
                        slidesContainer.querySelectorAll('.tab-content-item').forEach((slide) => {
                            slide.classList.add('swiper-slide');
                            slide.classList.remove('hidden');
                            slide.removeAttribute('style');
                        });
                        initSwiper();
                    }
                }
            } else {
                if (swiper) {
                    swiper.destroy(true, true);
                    swiper = null;
                    resetSlides();
                }
                handleTabs();
            }
        }

        handleResize();
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(handleResize, 150);
        });
    });
});
