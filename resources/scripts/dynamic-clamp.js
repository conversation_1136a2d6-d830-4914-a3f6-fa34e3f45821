document.addEventListener('DOMContentLoaded', () => {
    const calculateClamp = () => {
        document.querySelectorAll('.dynamic-clamp').forEach((el) => {
            const parent = el.parentElement;

            if (!parent) return;

            const parentHeight = parent.offsetHeight;
            const availableHeight = parentHeight - el.offsetTop;

            const style = window.getComputedStyle(el);
            const lineHeight = parseFloat(style.lineHeight || '0');

            if (availableHeight < el.scrollHeight) {
                let lines = Math.floor(availableHeight / lineHeight);
                if (lines < 1) lines = 1;
                el.style.webkitLineClamp = lines;
            } else {
                el.style.webkitLineClamp = 'unset';
            }
        });
    };

    calculateClamp();
    window.addEventListener('resize', calculateClamp);
});
