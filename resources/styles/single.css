@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .single .post-content * + :is(h1, h2, h3, h4, h5, h6) {
    margin-top: 2.5rem;
    margin-bottom: 1rem;
  }

  .single .post-content :is(h1, h2, h3, h4, h5, h6) {
    color: #222831;
    @apply font-normal text-neutral-100;
  }

  .single .post-content h2,
  .single .post-content h3 {
    @apply text-medium lg:text-mediumDesktop;
  }

  .single .post-content h4 {
    @apply text-small lg:text-smallDesktop;
  }

  .single .post-content h5 {
    @apply text-bodyLarge lg:text-bodyLargeDesktop;
  }

  .single .post-content h6 {
    @apply text-bodyMedium lg:text-bodyMediumDesktop;
  }

  .single .post-content :is(h2, h3, h4, h5, h6) strong {
    @apply font-normal;
  }
}
