import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    BlockControls,
    AlignmentToolbar,
    useBlockProps,
} from '@wordpress/block-editor';
import { Fragment } from '@wordpress/element';
import { Button } from '@wordpress/components';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ButtonEditor
} from './editor';

registerBlockType('sage/description', {
    apiVersion: 2,
    title: 'Description',
    icon: 'admin-users',
    category: 'layout',
    attributes: {
        ...commonAttributes,
    },
    edit: ({ attributes, setAttributes }) => {
        const { tagline, title, description, alignment, buttonText, buttonLink } = attributes;

        return (
            <Fragment>
                <BlockControls>
                    <AlignmentToolbar
                        value={alignment}
                        onChange={(newAlignment) => setAttributes({ alignment: newAlignment })}
                    />
                </BlockControls>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Description</h2>
                        </div>

                        <TextControl
                            label="Tagline"
                            value={tagline}
                            onChange={(value) => setAttributes({ tagline: value })}
                            placeholder="Enter tagline"
                            tagName="h5"
                        />

                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter title"
                            tagName="h2"
                        />

                        <TextControl
                            label="Description"
                            value={description}
                            onChange={(value) => setAttributes({ description: value })}
                            placeholder="Enter description"
                            tagName="p"
                            multiline={true}
                        />

                        <ButtonEditor
                            buttonText={buttonText}
                            buttonLink={buttonLink}
                            onChangeText={(value) => setAttributes({ buttonText: value })}
                            onChangeLink={(value) => setAttributes({ buttonLink: value })}
                            label="Button"
                        />

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-neutral-70 mb-2">Text Alignment</label>
                            <div className="p-3 bg-white rounded-md border border-neutral-30">
                                <div className="flex gap-2">
                                    <Button
                                        className={`py-2 px-4 rounded-md ${alignment === 'left' || !alignment ? 'bg-primary-80 text-white' : 'bg-neutral-20 text-neutral-70'}`}
                                        onClick={() => setAttributes({ alignment: 'left' })}
                                    >
                                        Left
                                    </Button>
                                    <Button
                                        className={`py-2 px-4 rounded-md ${alignment === 'center' ? 'bg-primary-80 text-white' : 'bg-neutral-20 text-neutral-70'}`}
                                        onClick={() => setAttributes({ alignment: 'center' })}
                                    >
                                        Center
                                    </Button>
                                    <Button
                                        className={`py-2 px-4 rounded-md ${alignment === 'right' ? 'bg-primary-80 text-white' : 'bg-neutral-20 text-neutral-70'}`}
                                        onClick={() => setAttributes({ alignment: 'right' })}
                                    >
                                        Right
                                    </Button>
                                </div>
                            </div>
                            <p className="mt-1 text-xs text-neutral-60">Choose the alignment for the text content</p>
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h5" value={attributes.tagline} />
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" value={attributes.description} />
                    <RichText.Content tagName="span" value={attributes.buttonText} />
                </div>
                <div data-dynamic="sage/description"></div>
            </div>
        );
    }
});
