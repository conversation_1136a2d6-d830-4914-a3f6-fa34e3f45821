import Swiper from 'swiper';
import 'swiper/css';
import 'swiper/css/pagination';

document.addEventListener('DOMContentLoaded', function () {
    const gridSection = document.querySelector('.grid-section');
    if (!gridSection) return;

    const desktopGrid = gridSection.querySelector('.desktop-grid');
    const mobileSwiperContainer = gridSection.querySelector('.mobile-swiper .swiper-wrapper');
    const leftItemsContainer = gridSection.querySelector('.left-items');
    const rightItemsContainer = gridSection.querySelector('.right-items');

    if (!desktopGrid || !mobileSwiperContainer || !leftItemsContainer || !rightItemsContainer) return;

    const originalLeftHTML = leftItemsContainer.innerHTML;
    const originalRightHTML = rightItemsContainer.innerHTML;

    let swiper = null;
    let initialized = false;

    function createNodesFromHTML(html) {
        const template = document.createElement('template');
        template.innerHTML = html.trim();
        return Array.from(template.content.childNodes);
    }

    function toMobileView() {
        mobileSwiperContainer.innerHTML = '';
        const leftNodes = createNodesFromHTML(originalLeftHTML);
        const rightNodes = createNodesFromHTML(originalRightHTML);

        [...leftNodes, ...rightNodes].forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
                mobileSwiperContainer.appendChild(node);
            }
        });

        swiper = new Swiper('.mobile-swiper .swiper', {
            slidesPerView: "auto",
            spaceBetween: 20,
            loop: false,
        });
    }

    function toDesktopView() {
        if (swiper) {
            swiper.destroy(true, true);
            swiper = null;
        }
        leftItemsContainer.innerHTML = originalLeftHTML;
        rightItemsContainer.innerHTML = originalRightHTML;
    }

    function handleResize() {
        if (window.innerWidth < 1024 && !initialized) {
            toMobileView();
            initialized = true;
        } else if (window.innerWidth >= 1024 && initialized) {
            toDesktopView();
            initialized = false;
        }
    }

    handleResize();
    window.addEventListener('resize', handleResize);
});
