@props(['color' => 'blue', 'type' => 'static', 'background' => null, 'backgroundSize' => 'cover', 'backgroundPosition' => 'center'])

<div
    {{ $attributes->class([
        'absolute w-full h-full overflow-hidden -z-10 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 lg:rounded-3xl lg:w-[calc(100%_-_3rem)]',
    ]) }}
>
    @if($background)
        <div
            class="floating-bg-layer w-full h-full absolute top-0 left-0 rounded-[inherit] transition-all duration-1000 ease-in-out"
            style="background-image: url('{{ $background }}'); background-size: {{ $backgroundSize }}; background-position: {{ $backgroundPosition }};"
        ></div>
    @elseif($type === 'dynamic')
        @foreach (['blue', 'green', 'peach', 'pink', 'gray', 'blue-dark'] as $bgColor)
            <div
                class="floating-bg-layer opacity-{{ $color === $bgColor ? '100' : '0' }} w-full h-full absolute top-0 left-0 rounded-[inherit] transition-all duration-1000 ease-in-out {{ $bgColor }}-bg lg:duration-[2000ms]"
            ></div>
        @endforeach
    @else
        <div
            class="floating-bg-layer w-full h-full absolute top-0 left-0 rounded-[inherit] transition-all duration-1000 ease-in-out {{ $color }}-bg"
        ></div>
    @endif
</div>
