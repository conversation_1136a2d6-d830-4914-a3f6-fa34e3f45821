import React from 'react';
import { RichText } from '@wordpress/block-editor';
import { URLInputButton } from '@wordpress/block-editor';

/**
 * Button editor component with preview, text, and URL fields
 * 
 * @param {Object} props Component props
 * @param {string} props.buttonText Button text
 * @param {string} props.buttonLink Button URL
 * @param {Function} props.onChangeText Callback for text change
 * @param {Function} props.onChangeLink Callback for URL change
 * @param {string} props.label Optional custom label
 * @returns {JSX.Element} ButtonEditor component
 */
const ButtonEditor = ({ 
    buttonText, 
    buttonLink, 
    onChangeText, 
    onChangeLink,
    label = 'Button'
}) => {
    return (
        <div className="mb-4">
            <label className="block text-sm font-medium text-neutral-70 mb-2">{label}</label>
            <div className="p-4 bg-neutral-10 rounded-lg border border-neutral-30">
                {/* Button Text */}
                <div className="mb-3">
                    <label className="block text-xs font-medium text-neutral-60 mb-1">Button Text:</label>
                    <RichText
                        tagName="span"
                        placeholder="Enter button text"
                        value={buttonText}
                        onChange={onChangeText}
                        className="p-3 bg-white rounded-md focus:border-primary-70 focus:ring-1 focus:ring-primary-70 outline-none text-base block w-auto" 
                        style={{ border: '1px solid #E2E8F0' }}
                    />
                </div>
                
                {/* Button URL */}
                <div>
                    <label className="block text-xs font-medium text-neutral-60 mb-1">Button URL:</label>
                    <div className="flex items-center gap-2 relative">
                        <div className="flex-grow relative">
                            <input 
                                type="text" 
                                value={buttonLink || ''}
                                onChange={(e) => onChangeLink(e.target.value)}
                                placeholder="https://example.com"
                                className="p-3 bg-white rounded-md focus:border-primary-70 focus:ring-1 focus:ring-primary-70 outline-none text-base block w-full" 
                                style={{ border: '1px solid #E2E8F0' }}
                            />
                            {buttonLink && (
                                <button 
                                    onClick={() => onChangeLink('')}
                                    className="absolute right-9 pt-1 cursor-pointer border-0 bg-transparent top-1/2 -translate-y-1/2 text-neutral-50 hover:text-neutral-70 transition-colors"
                                    title="Clear URL"
                                >
                                    <span className="dashicons dashicons-no-alt" style={{ fontSize: '16px' }}></span>
                                </button>
                            )}
                        </div>
                        <div style={{ position: 'absolute', right: '0', top: '1px' }}>
                            <URLInputButton
                                url={buttonLink}
                                onChange={onChangeLink}
                                className="flex-shrink-0 bg-neutral-20 hover:bg-neutral-30 text-neutral-70 border-0 rounded-md"
                                icon="admin-links"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <p className="mt-2 text-xs text-neutral-60">Configure the button that will appear in this section.</p>
        </div>
    );
};

export default ButtonEditor;
