<?php
function my_register_case_studies_cpt() {
    $labels = array(
        'name'               => 'Case Studies',
        'singular_name'      => 'Case Study',
        'menu_name'          => 'Case Studies',
        'name_admin_bar'     => 'Case Study',
        'add_new'            => 'Add New',
        'add_new_item'       => 'Add New Case Study',
        'new_item'           => 'New Case Study',
        'edit_item'          => 'Edit Case Study',
        'view_item'          => 'View Case Study',
        'all_items'          => 'All Case Studies',
        'search_items'       => 'Search Case Studies',
        'not_found'          => 'No case studies found.',
        'not_found_in_trash' => 'No case studies found in Trash.',
    );

    $args = array(
        'labels'       => $labels,
        'public'       => true,
        'has_archive'  => false,
        'rewrite'      => array( 'slug' => 'case-studies' ),
        'supports'     => array( 'title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments' ),
        'show_in_rest' => true,
    );

    register_post_type( 'case_study', $args );
}
add_action( 'init', 'my_register_case_studies_cpt' );

function my_register_case_study_taxonomies() {
    $labels_industries = array(
        'name'              => 'Industries',
        'singular_name'     => 'Industry',
        'search_items'      => 'Search Industries',
        'all_items'         => 'All Industries',
        'parent_item'       => 'Parent Industry',
        'parent_item_colon' => 'Parent Industry:',
        'edit_item'         => 'Edit Industry',
        'update_item'       => 'Update Industry',
        'add_new_item'      => 'Add New Industry',
        'new_item_name'     => 'New Industry Name',
        'menu_name'         => 'Industries',
    );

    $args_industries = array(
        'hierarchical'      => true,
        'labels'            => $labels_industries,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array( 'slug' => 'industry' ),
        'show_in_rest'      => true,
    );

    register_taxonomy( 'industry', array( 'case_study' ), $args_industries );

    $labels_services = array(
        'name'              => 'Services',
        'singular_name'     => 'Service',
        'search_items'      => 'Search Services',
        'all_items'         => 'All Services',
        'parent_item'       => 'Parent Service',
        'parent_item_colon' => 'Parent Service:',
        'edit_item'         => 'Edit Service',
        'update_item'       => 'Update Service',
        'add_new_item'      => 'Add New Service',
        'new_item_name'     => 'New Service Name',
        'menu_name'         => 'Services',
    );

    $args_services = array(
        'hierarchical'      => true,
        'labels'            => $labels_services,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array( 'slug' => 'service' ),
        'show_in_rest'      => true,
    );

    register_taxonomy( 'service', array( 'case_study' ), $args_services );
}
add_action( 'init', 'my_register_case_study_taxonomies' );

function my_add_default_terms() {
    $industries = array(
        'human-resources'    => 'Human Resources',
        'media-advertising'   => 'Media & Advertising',
        'technology'          => 'Technology',
        'shipping-logistics'  => 'Shipping & Logistics',
        'insurance'           => 'Insurance',
        'automotive'          => 'Automotive',
        'fintech'             => 'Fintech',
        'gis'                 => 'GIS',
    );
    foreach ( $industries as $slug => $name ) {
        if ( ! term_exists( $name, 'industry' ) ) {
            wp_insert_term( $name, 'industry', array( 'slug' => $slug ) );
        }
    }

    $services = array(
        'software-development'   => 'Software Development',
        'product-design'         => 'Product Design',
        'data-science-analytics' => 'Data Science & Analytics',
        'data-engineering'       => 'Data Engineering',
    );
    foreach ( $services as $slug => $name ) {
        if ( ! term_exists( $name, 'service' ) ) {
            wp_insert_term( $name, 'service', array( 'slug' => $slug ) );
        }
    }
}
add_action( 'init', 'my_add_default_terms' );

function get_yoast_primary_term( $post_id, $taxonomy ) {
    $primary_id = get_post_meta( $post_id, '_yoast_wpseo_primary_' . $taxonomy, true );
    if ( $primary_id ) {
        $term = get_term( $primary_id, $taxonomy );
        if ( ! is_wp_error( $term ) ) {
            return $term;
        }
    }
    $terms = get_the_terms( $post_id, $taxonomy );
    if ( ! empty( $terms ) && ! is_wp_error( $terms ) ) {
        return current( $terms );
    }
    return false;
}

function add_hero_image_meta_box() {
    add_meta_box(
        'hero_image',
        'Hero Image',
        'hero_image_meta_box_callback',
        'case_study',
        'side',
        'low'
    );
}
add_action( 'add_meta_boxes', 'add_hero_image_meta_box' );

function hero_image_meta_box_callback( $post ) {
    $hero_image_id = get_post_meta( $post->ID, '_hero_image_id', true );
    wp_nonce_field( 'hero_image_nonce', 'hero_image_nonce_field' );
    ?>
    <div id="hero-image-container">
        <?php if ( $hero_image_id ) : ?>
            <?php echo wp_get_attachment_image( $hero_image_id, 'medium' ); ?>
        <?php endif; ?>
    </div>
    <input type="hidden" id="hero_image_id" name="hero_image_id" value="<?php echo esc_attr( $hero_image_id ); ?>" />
    <p>
        <a href="#" id="upload_hero_image_button" class="button">Upload Hero Image</a>
        <a href="#" id="remove_hero_image_button" class="button" style="<?php echo $hero_image_id ? '' : 'display:none;'; ?>">Remove Hero Image</a>
    </p>
    <script>
    jQuery(document).ready(function($){
        var frame;
        $('#upload_hero_image_button').on('click', function(e){
            e.preventDefault();
            if ( frame ) {
                frame.open();
                return;
            }
            frame = wp.media({
                title: 'Select or Upload Hero Image',
                button: { text: 'Use this image' },
                multiple: false
            });
            frame.on('select', function(){
                var attachment = frame.state().get('selection').first().toJSON();
                $('#hero_image_id').val(attachment.id);
                $('#hero-image-container').html('<img src="'+attachment.sizes.medium.url+'" style="max-width:100%;" />');
                $('#remove_hero_image_button').show();
            });
            frame.open();
        });
        $('#remove_hero_image_button').on('click', function(e){
            e.preventDefault();
            $('#hero_image_id').val('');
            $('#hero-image-container').html('');
            $(this).hide();
        });
    });
    </script>
    <?php
}

function save_hero_image_meta_box_data( $post_id ) {
    if ( ! isset( $_POST['hero_image_nonce_field'] ) || ! wp_verify_nonce( $_POST['hero_image_nonce_field'], 'hero_image_nonce' ) ) {
        return;
    }
    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
        return;
    }
    if ( isset( $_POST['post_type'] ) && 'case_study' === $_POST['post_type'] ) {
        if ( ! current_user_can( 'edit_post', $post_id ) ) {
            return;
        }
    }
    if ( isset( $_POST['hero_image_id'] ) ) {
        update_post_meta( $post_id, '_hero_image_id', sanitize_text_field( $_POST['hero_image_id'] ) );
    }
}
add_action( 'save_post', 'save_hero_image_meta_box_data' );
