import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import {
    RangeControl,
    Panel,
    PanelBody,
    TextControl as WPTextControl,
    Button,
    SearchControl
} from '@wordpress/components';
import { useState, useEffect } from '@wordpress/element';
import { commonAttributes } from './common';
import {
    BlockSettings,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    TextControl
} from './editor';

const DEFAULT_TIMELINE_ITEM = {
    position: 50,
    date: '',
    event: '',
};

const DEFAULT_POSITION_ITEM = {
    text: '',
    icon: '',
    positionId: 0, // ID from the central repository (0 means manually added)
};

registerBlockType('sage/case-study-timeline', {
    apiVersion: 2,
    title: 'Case Study: Timeline',
    icon: 'schedule',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        icon: { type: 'string' },
        accentColor: { type: 'string' },
        timeline: {
            type: 'array',
            default: [ DEFAULT_TIMELINE_ITEM ],
        },
        positions: {
            type: 'array',
            default: [ DEFAULT_POSITION_ITEM ],
            // Define the schema for each position item
            schema: {
                type: 'object',
                properties: {
                    text: { type: 'string' },
                    icon: { type: 'string' },
                    positionId: { type: 'number', default: 0 }
                }
            }
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, description, icon, accentColor, timeline, positions } = attributes;
        const [ timelineActive, setTimelineActive ] = useState(0);
        const [ positionsActive, setPositionsActive ] = useState(0);
        const [ availablePositions, setAvailablePositions ] = useState([]);
        const [ positionSearchTerm, setPositionSearchTerm ] = useState('');
        const [ showAllPositions, setShowAllPositions ] = useState(false);

        // One-time migration for existing positions to ensure they're marked as manual positions
        useEffect(() => {
            // Check if we need to migrate positions (if any position doesn't have positionId defined)
            const needsMigration = positions.some(position => typeof position.positionId === 'undefined');

            if (needsMigration) {
                console.log('Migrating existing positions to include positionId=0');
                const migratedPositions = positions.map(position => ({
                    ...position,
                    positionId: 0 // Mark all existing positions as manual
                }));
                setAttributes({ positions: migratedPositions });
            }
        }, []);

        const handleTimelineChange = ( index, field, value ) => {
            const newTimeline = [ ...timeline ];
            newTimeline[index][field] = value;
            setAttributes({ timeline: newTimeline });
        };
        const addTimelineItem = () => {
            const newTimeline = [ ...timeline, { ...DEFAULT_TIMELINE_ITEM } ];
            setAttributes({ timeline: newTimeline });
            setTimelineActive(newTimeline.length - 1);
        };
        const removeTimelineItem = ( index ) => {
            if ( timeline.length === 1 ) return;
            const newTimeline = [ ...timeline ];
            newTimeline.splice(index, 1);
            setAttributes({ timeline: newTimeline });
            setTimelineActive(Math.max(timelineActive - 1, 0));
        };

        const handlePositionTextChange = ( index, value ) => {
            const newPositions = [ ...positions ];
            newPositions[index].text = value;
            // Make sure we're not changing a repository position
            if (newPositions[index].positionId !== 0) {
                newPositions[index].positionId = 0; // Convert to manual position if editing
            }
            setAttributes({ positions: newPositions });
        };
        const handlePositionIconChange = ( index, value ) => {
            const newPositions = [ ...positions ];
            newPositions[index].icon = value;
            // Make sure we're not changing a repository position
            if (newPositions[index].positionId !== 0) {
                newPositions[index].positionId = 0; // Convert to manual position if editing
            }
            setAttributes({ positions: newPositions });
        };
        const addPositionItem = () => {
            const newPositions = [ ...positions, { ...DEFAULT_POSITION_ITEM } ];
            setAttributes({ positions: newPositions });
            setPositionsActive(newPositions.length - 1);
        };
        const removePositionItem = ( index ) => {
            if ( positions.length === 1 ) return;
            const newPositions = [ ...positions ];
            newPositions.splice(index, 1);
            setAttributes({ positions: newPositions });
            setPositionsActive(Math.max(positionsActive - 1, 0));
        };

        // Load available positions from WordPress options
        useEffect(() => {
            console.log('Positions Data:', window.positionsData);

            // Check if we have the data in the window object
            if (window.positionsData && window.positionsData.positions) {
                console.log('Positions found in window object:', window.positionsData.positions);
                setAvailablePositions(window.positionsData.positions);
            } else {
                console.log('No positions data found in window object');

                // Fallback: Try to fetch the data via AJAX
                fetch('/wp-json/wp/v2/settings/positions')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Fetched positions:', data);
                        if (data && Array.isArray(data)) {
                            setAvailablePositions(data);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching positions:', error);
                        setAvailablePositions([]);
                    });
            }
        }, []);

        // Function to check if a position is already selected
        const isPositionSelected = (positionId) => {
            return positions.some(position => position.positionId === positionId);
        };

        // Function to add a position from the repository
        const addPositionFromRepository = (positionData) => {
            const newPositions = [...positions];
            newPositions.push({
                text: positionData.name,
                icon: positionData.url,
                positionId: positionData.id
            });
            setAttributes({ positions: newPositions });
            setPositionsActive(newPositions.length - 1);
        };

        // Function to toggle a position from the repository
        const togglePosition = (positionData) => {
            const isSelected = isPositionSelected(positionData.id);

            if (isSelected) {
                // Remove the position
                const newPositions = positions.filter(position => position.positionId !== positionData.id);
                setAttributes({ positions: newPositions.length ? newPositions : [DEFAULT_POSITION_ITEM] });
                setPositionsActive(0);
            } else {
                // Add the position
                addPositionFromRepository(positionData);
            }
        };

        return (
            <div {...blockProps}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Case Study: Timeline</h2>
                    </div>
                    <BlockSettings
                        title={title}
                        onTitleChange={(val) => setAttributes({ title: val })}
                        description={description}
                        onDescriptionChange={(val) => setAttributes({ description: val })}
                        icon={icon}
                        onIconSelect={(url) => setAttributes({ icon: url })}
                        onIconRemove={() => setAttributes({ icon: '' })}
                        accentColor={accentColor}
                        onAccentColorChange={(color) => setAttributes({ accentColor: color })}
                    />

                    <TabNavigation
                        items={timeline}
                        activeTab={timelineActive}
                        onTabChange={setTimelineActive}
                        onAddItem={addTimelineItem}
                        onRemoveItem={() => removeTimelineItem(timelineActive)}
                        getItemTitle={(_, index) => `Indicator ${index + 1}`}
                        itemName="Indicator"
                        addButtonTitle="Add New Indicator"
                    />

                    { timeline[timelineActive] && (
                        <div className="bg-neutral-20 rounded-xl p-6 mb-6">
                            <Panel className="border-0 shadow-none">
                                <PanelBody
                                    title={`Indicator ${timelineActive + 1} Details`}
                                    initialOpen={true}
                                >
                                    <ItemEditor
                                        itemIndex={timelineActive}
                                        onRemove={() => removeTimelineItem(timelineActive)}
                                        itemName="Indicator"
                                        showRemoveButton={timeline.length > 1}
                                    >
                                        <div className="mb-4">
                                            <label className="block text-sm font-medium text-neutral-70 mb-2">Position on Timeline (%)</label>
                                            <RangeControl
                                                value={ timeline[timelineActive].position }
                                                onChange={ ( val ) => handleTimelineChange(timelineActive, 'position', val) }
                                                min={0}
                                                max={100}
                                                className="mb-2"
                                            />
                                            <p className="text-xs text-neutral-60">Set the position of this indicator on the timeline (0% = start, 100% = end)</p>
                                        </div>

                                        <TextControl
                                            label="Date"
                                            value={ timeline[timelineActive].date }
                                            onChange={ ( val ) => handleTimelineChange(timelineActive, 'date', val) }
                                            placeholder="Enter Date"
                                            tagName="p"
                                        />

                                        <TextControl
                                            label="Event"
                                            value={ timeline[timelineActive].event }
                                            onChange={ ( val ) => handleTimelineChange(timelineActive, 'event', val) }
                                            placeholder="Enter Event"
                                            tagName="p"
                                        />
                                    </ItemEditor>
                                </PanelBody>
                            </Panel>
                        </div>
                    ) }

                    <TabNavigation
                        items={positions}
                        activeTab={positionsActive}
                        onTabChange={setPositionsActive}
                        onAddItem={addPositionItem}
                        onRemoveItem={() => removePositionItem(positionsActive)}
                        getItemTitle={(item, index) => item.text?.trim().length > 0 ? item.text : `Position ${index + 1}`}
                        itemName="Position"
                        addButtonTitle="Add New Position"
                    />

                    <div className="bg-neutral-20 rounded-xl p-6 mb-6">
                        <Panel className="border-0 shadow-none">
                            <PanelBody
                                title="Positions Repository"
                                initialOpen={true}
                            >
                                <div className="mb-4">
                                    <p className="text-sm text-neutral-70 mb-2">Select positions from the central repository or add custom positions.</p>

                                    <SearchControl
                                        value={positionSearchTerm}
                                        onChange={setPositionSearchTerm}
                                        className="mb-3"
                                        placeholder="Search positions..."
                                    />

                                    <div className="positions-container p-4 bg-white rounded-lg border border-neutral-30 max-h-60 overflow-y-auto">
                                        {(() => {
                                            const filteredPositions = positionSearchTerm
                                                ? availablePositions.filter(position =>
                                                    position.name.toLowerCase().includes(positionSearchTerm.toLowerCase()))
                                                : availablePositions;

                                            // Determine if we should show all positions or just a subset
                                            const displayPositions = !showAllPositions && filteredPositions.length > 20 && !positionSearchTerm
                                                ? filteredPositions.slice(0, 20)
                                                : filteredPositions;

                                            if (filteredPositions.length === 0) {
                                                return (
                                                    <div className="text-center py-4 text-neutral-60 text-sm">
                                                        No positions match your search
                                                    </div>
                                                );
                                            }

                                            return (
                                                <>
                                                    <div className="positions-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(70px, 1fr))', gap: '10px' }}>
                                                        {displayPositions.map((position, idx) => {
                                                            const isSelected = isPositionSelected(position.id);
                                                            return (
                                                                <div
                                                                    key={idx}
                                                                    className={`position-item relative group transition-all ${isSelected ? 'scale-105' : ''}`}
                                                                    onClick={() => togglePosition(position)}
                                                                >
                                                                    <div className={`flex flex-col items-center p-2 rounded-lg cursor-pointer transition-all ${isSelected ? 'bg-primary-20 border border-primary-70' : 'bg-neutral-10 hover:bg-neutral-20 border border-transparent'}`}>
                                                                        <div className="w-10 h-10 flex items-center justify-center mb-1">
                                                                            <img src={position.url} alt={position.name} className="max-w-full max-h-full object-contain" />
                                                                        </div>
                                                                        <span className="text-xs text-center line-clamp-2 h-8 overflow-hidden">{position.name}</span>
                                                                        {isSelected && (
                                                                            <div className="absolute -top-1 -right-1 bg-primary-70 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                                                                                ✓
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>

                                                    {!showAllPositions && filteredPositions.length > 20 && !positionSearchTerm && (
                                                        <div className="text-center mt-4">
                                                            <Button
                                                                variant="link"
                                                                onClick={() => setShowAllPositions(true)}
                                                                className="text-primary-70"
                                                            >
                                                                Show all {filteredPositions.length} positions
                                                            </Button>
                                                        </div>
                                                    )}
                                                </>
                                            );
                                        })()}
                                    </div>
                                </div>
                            </PanelBody>
                        </Panel>
                    </div>

                    { positions[positionsActive] && (
                        <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                            <Panel className="border-0 shadow-none">
                                <PanelBody
                                    title={`Position ${positionsActive + 1} Details`}
                                    initialOpen={true}
                                >
                                    <ItemEditor
                                        itemIndex={positionsActive}
                                        onRemove={() => removePositionItem(positionsActive)}
                                        itemName="Position"
                                        showRemoveButton={positions.length > 1}
                                    >
                                        {/* All positions are editable, but we show different UI based on source */}
                                        {positions[positionsActive].positionId === 0 ? (
                                            <>
                                                <div className="m-0 p-0 bg-white rounded-lg border border-neutral-30">
                                                    <p className="text-sm text-neutral-70 mb-2">This is a manually added position. You can edit its details below.</p>
                                                </div>

                                                <ImageUploader
                                                    image={positions[positionsActive].icon}
                                                    onSelect={(url) => handlePositionIconChange(positionsActive, url)}
                                                    onRemove={() => handlePositionIconChange(positionsActive, '')}
                                                    label="Position Icon"
                                                    description="Upload an icon for this position."
                                                    height={24}
                                                    objectFit="contain"
                                                    altText="Position Icon"
                                                />

                                                <TextControl
                                                    label="Position Text"
                                                    value={positions[positionsActive].text}
                                                    onChange={(val) => handlePositionTextChange(positionsActive, val)}
                                                    placeholder="Enter position name"
                                                    tagName="p"
                                                />
                                            </>
                                        ) : (
                                            <>
                                                <div className="p-3 bg-white rounded-lg border border-neutral-30 mb-4">
                                                    <p className="text-sm text-neutral-70 mb-3">This position is from the central repository.</p>
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center">
                                                            <img src={positions[positionsActive].icon} alt={positions[positionsActive].text} className="max-w-full max-h-full object-contain" />
                                                        </div>
                                                        <div className="font-medium">{positions[positionsActive].text}</div>
                                                    </div>
                                                    <p className="text-xs text-neutral-60 mt-2">Changes made here will convert this to a custom position.</p>
                                                </div>

                                                <ImageUploader
                                                    image={positions[positionsActive].icon}
                                                    onSelect={(url) => handlePositionIconChange(positionsActive, url)}
                                                    onRemove={() => handlePositionIconChange(positionsActive, '')}
                                                    label="Position Icon"
                                                    description="Upload an icon for this position."
                                                    height={24}
                                                    objectFit="contain"
                                                    altText="Position Icon"
                                                />

                                                <TextControl
                                                    label="Position Text"
                                                    value={positions[positionsActive].text}
                                                    onChange={(val) => handlePositionTextChange(positionsActive, val)}
                                                    placeholder="Enter position name"
                                                    tagName="p"
                                                />
                                            </>
                                        )}
                                    </ItemEditor>
                                </PanelBody>
                            </Panel>
                        </div>
                    ) }
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        const { title, description, icon, accentColor, timeline, positions } = attributes;
        return (
            <div { ...blockProps }>
                <div style={{ display: 'none' }} data-accent-color={ accentColor }>
                    <RichText.Content tagName="h2" value={ title } />
                    <RichText.Content tagName="p" value={ description } />
                    { icon && <img src={ icon } alt="Case Study Icon" /> }
                    { timeline.map( ( item, index ) => (
                        <div key={ `timeline-${ index }` }>
                            <span>{ item.position }%</span>
                            <RichText.Content tagName="p" value={ item.date } />
                            <RichText.Content tagName="p" value={ item.event } />
                        </div>
                    ) ) }
                    { positions.map( ( item, index ) => {
                        // Ensure positionId is properly serialized
                        const positionId = typeof item.positionId !== 'undefined' ? item.positionId : 0;
                        return (
                            <div key={ `position-${ index }` } data-position-id={ positionId }>
                                { item.icon && <img src={ item.icon } alt="Position Icon" /> }
                                <RichText.Content tagName="p" value={ item.text } />
                                <span className="position-id">{ positionId }</span>
                            </div>
                        );
                    } ) }
                </div>
                <div data-dynamic="sage/case-study-timeline"></div>
            </div>
        );
    },
});
