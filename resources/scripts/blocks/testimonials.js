import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps
} from '@wordpress/block-editor';
import { Panel, PanelBody } from '@wordpress/components';
import { useState } from '@wordpress/element';
import { commonAttributes } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    ButtonEditor
} from './editor';

registerBlockType('sage/testimonials', {
    apiVersion: 2,
    title: 'Testimonials',
    icon: 'format-quote',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        testimonials: {
            type: 'array',
            default: [{
                text: '',
                image: '',
                companyName: '',
                buttonLink: ''
            }],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { testimonials, title, description } = attributes;
        const [activeTab, setActiveTab] = useState(0);

        const handleTestimonialChange = (index, field, value) => {
            const newTestimonials = [...testimonials];
            newTestimonials[index][field] = value;
            setAttributes({ testimonials: newTestimonials });
        };

        const addTestimonial = () => {
            const newTestimonials = [...testimonials, { text: '', image: '', companyName: '', buttonLink: '' }];
            setAttributes({ testimonials: newTestimonials });
            setActiveTab(newTestimonials.length - 1);
        };

        const removeTestimonial = (index) => {
            const newTestimonials = [...testimonials];
            newTestimonials.splice(index, 1);
            setAttributes({ testimonials: newTestimonials });
            setActiveTab(Math.max(activeTab - 1, 0));
        };

        return (
            <div {...useBlockProps()}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Testimonials</h2>
                    </div>
                    <TextControl
                        label="Main Title"
                        value={title}
                        onChange={(value) => setAttributes({ title: value })}
                        placeholder="Enter title"
                        tagName="h2"
                    />
                    <TextControl
                        label="Description"
                        value={description}
                        onChange={(value) => setAttributes({ description: value })}
                        placeholder="Enter description"
                        tagName="p"
                        multiline={true}
                    />
                    <div className="bg-neutral-20 rounded-xl p-6 mb-4">
                        <Panel className="border-0 shadow-none">
                            <PanelBody title="Testimonials" initialOpen={true}>
                                <TabNavigation
                                    items={testimonials}
                                    activeTab={activeTab}
                                    onTabChange={setActiveTab}
                                    onAddItem={addTestimonial}
                                    onRemoveItem={() => removeTestimonial(activeTab)}
                                    getItemTitle={(_, index) => `Testimonial ${index + 1}`}
                                    itemName="Testimonial"
                                    addButtonTitle="Add New Testimonial"
                                />
                                {testimonials.length > 0 && (
                                    <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30 mt-4">
                                        <ItemEditor
                                            itemIndex={activeTab}
                                            onRemove={() => removeTestimonial(activeTab)}
                                            itemName="Testimonial"
                                            showRemoveButton={testimonials.length > 1}
                                        >
                                            <TextControl
                                                label="Testimonial Text"
                                                value={testimonials[activeTab].text}
                                                onChange={(value) => handleTestimonialChange(activeTab, 'text', value)}
                                                placeholder="Enter testimonial text"
                                                tagName="p"
                                                multiline={true}
                                            />

                                            <TextControl
                                                label="Company Name"
                                                value={testimonials[activeTab].companyName}
                                                onChange={(value) => handleTestimonialChange(activeTab, 'companyName', value)}
                                                placeholder="Enter company name"
                                                tagName="h3"
                                            />

                                            <ButtonEditor
                                                buttonText="Visit Website"
                                                buttonLink={testimonials[activeTab].buttonLink}
                                                onChangeText={() => {}}
                                                onChangeLink={(value) => handleTestimonialChange(activeTab, 'buttonLink', value)}
                                                label="Company Website Link"
                                            />

                                            <ImageUploader
                                                image={testimonials[activeTab].image}
                                                onSelect={(url) => handleTestimonialChange(activeTab, 'image', url)}
                                                onRemove={() => handleTestimonialChange(activeTab, 'image', '')}
                                                label="Company Logo"
                                                description="Upload a logo for this testimonial."
                                                height={32}
                                                objectFit="contain"
                                                altText="Company Logo"
                                                padding={4}
                                            />
                                        </ItemEditor>
                                    </div>
                                )}
                            </PanelBody>
                        </Panel>
                    </div>
                </div>
            </div>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" value={attributes.description} />

                    {attributes.testimonials?.map((testimonial, index) => (
                        <div key={`testimonial-${index}`}>
                            <RichText.Content tagName="p" value={testimonial.text} />
                            <RichText.Content tagName="h3" value={testimonial.companyName} />
                            {testimonial.buttonLink && (
                                <a href={testimonial.buttonLink} rel="noopener noreferrer">
                                    Visit Website
                                </a>
                            )}
                            {testimonial.image && (
                                <img
                                    src={testimonial.image}
                                    alt={`Testimonial from ${testimonial.companyName || 'Company'}`}
                                />
                            )}
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/testimonials"></div>
            </div>
        );
    }
});
