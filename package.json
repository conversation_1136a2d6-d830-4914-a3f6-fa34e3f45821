{"name": "sage", "private": true, "browserslist": ["extends @roots/browserslist-config"], "engines": {"node": ">=20.0.0"}, "type": "module", "scripts": {"dev": "bud dev", "build": "bud build", "translate": "npm run translate:pot && npm run translate:update", "translate:pot": "wp i18n make-pot . ./resources/lang/sage.pot --include=\"theme.json,patterns,app,resources\"", "translate:update": "for file in ./resources/lang/*.po; do wp i18n update-po ./resources/lang/sage.pot $file; done", "translate:compile": "npm run translate:mo && npm run translate:js", "translate:js": "wp i18n make-json ./resources/lang --pretty-print", "translate:mo": "wp i18n make-mo ./resources/lang ./resources/lang"}, "devDependencies": {"@roots/bud": "6.20.0", "@roots/bud-tailwindcss": "6.20.0", "@roots/sage": "6.20.0", "sass": "^1.77.8"}, "dependencies": {"@tailwindcss/line-clamp": "^0.4.4", "just-validate": "^4.3.0", "swiper": "^11.1.14"}}