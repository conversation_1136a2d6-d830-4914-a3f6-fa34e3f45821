@php
    $alignment = $attributes['alignment'] ?? 'center';
@endphp

<div class="description-section flex justify-center text-{{ $alignment }}">
    <div class="container px-5 py-8 flex flex-col justify-between lg:pt-[62px]">
        @if(!empty($attributes['tagline']))
            {{-- <x-subtitle class="animation-delay-300" data-animate="fade-in" variation="tagline">{!! $attributes['tagline'] !!}</x-subtitle> --}}
            {{-- <x-subtitle class="text-[#008FD3]" variation="tagline">{!! $attributes['tagline'] !!}</x-subtitle> --}}
            <x-title variation="tagline" class="uppercase !text-[#008FD3] mb-4">{!! $attributes['tagline'] !!}</x-title>
        @endif

        @if(!empty($attributes['title']))
            {{-- <x-title class="animation-delay-600" data-animate="fade-in" variation="large">{!! $attributes['title'] !!}</x-title> --}}
            <x-title variation="large">{!! $attributes['title'] !!}</x-title>
        @endif

        @if(!empty($attributes['description']))
            <p class="text-bodyMedium {{ $alignment === 'center' ? 'px-10 mx-auto max-w-4xl' : 'mx-0 px-0 max-w-2xl' }} lg:text-bodyMediumDesktop">
                {!! $attributes['description'] !!}
            </p>
        @endif

        @if(!empty($attributes['buttonText']))
            <x-button 
                type="link"
                variant="secondary"
                :href="$attributes['buttonLink'] ?? '#'"
                class="
                    {{ $alignment === 'center' ? 'mx-auto' :
                       ($alignment === 'left' ? 'mr-auto' :
                        ($alignment === 'right' ? 'ml-auto' : ''))
                    }}">
                {{ $attributes['buttonText'] }}
            </x-button>
        @endif
    </div>
</div>
