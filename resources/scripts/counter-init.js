document.addEventListener('DOMContentLoaded', function() {
    const counterSections = document.querySelectorAll('.counter-section');

    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 1.0
    };

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const section = entry.target;
                const startNumberElement = section.querySelector('h4.inline:first-child');
                const endNumber = parseInt(startNumberElement.textContent, 10);
                const startNumber = 0;

                if (isNaN(startNumber) || isNaN(endNumber)) return;

                const computedStyle = window.getComputedStyle(section);
                if (computedStyle.opacity === "1") {
                    let currentNumber = startNumber;
                    const increment = Math.ceil(endNumber / 100);

                    const updateCount = () => {
                        if (currentNumber < endNumber) {
                            currentNumber += increment;
                            startNumberElement.innerText = currentNumber;
                            requestAnimationFrame(updateCount);
                        } else {
                            startNumberElement.innerText = endNumber;
                        }
                    };

                    updateCount();

                    observer.unobserve(section);
                }
            }
        });
    }, observerOptions);

    counterSections.forEach(section => {
        observer.observe(section);
    });
});
