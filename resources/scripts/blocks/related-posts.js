import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { SelectControl } from '@wordpress/components';
import { useSelect } from '@wordpress/data';
import { Fragment } from '@wordpress/element';
import { commonSave } from './common';

registerBlockType('sage/related-posts', {
    apiVersion: 2,
    title: 'Related Posts',
    icon: 'networking',
    category: 'layout',
    attributes: {
        category1: { type: 'string', default: '' },
        category2: { type: 'string', default: '' },
        category3: { type: 'string', default: '' },
    },
    edit: (props) => {
        const { attributes: { category1, category2, category3 }, setAttributes } = props;

        const categories = useSelect(
            (select) =>
                select('core').getEntityRecords('taxonomy', 'category', { per_page: -1 }),
            []
        );

        const categoryOptions = categories
            ? categories.map((cat) => ({ label: cat.name, value: cat.id.toString() }))
            : [];

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Related Posts</h2>
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-neutral-70 mb-2">Slot 1 Category</label>
                            <div className="p-3 bg-white rounded-md border border-neutral-30">
                                <SelectControl
                                    value={category1}
                                    options={[{ label: 'Select a category', value: '' }, ...categoryOptions]}
                                    onChange={(val) => setAttributes({ category1: val })}
                                />
                            </div>
                            <p className="mt-1 text-xs text-neutral-60">Select a category for the first related post slot</p>
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-neutral-70 mb-2">Slot 2 Category</label>
                            <div className="p-3 bg-white rounded-md border border-neutral-30">
                                <SelectControl
                                    value={category2}
                                    options={[{ label: 'Select a category', value: '' }, ...categoryOptions]}
                                    onChange={(val) => setAttributes({ category2: val })}
                                />
                            </div>
                            <p className="mt-1 text-xs text-neutral-60">Select a category for the second related post slot</p>
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-neutral-70 mb-2">Slot 3 Category</label>
                            <div className="p-3 bg-white rounded-md border border-neutral-30">
                                <SelectControl
                                    value={category3}
                                    options={[{ label: 'Select a category', value: '' }, ...categoryOptions]}
                                    onChange={(val) => setAttributes({ category3: val })}
                                />
                            </div>
                            <p className="mt-1 text-xs text-neutral-60">Select a category for the third related post slot</p>
                        </div>

                        <div className="p-3 bg-neutral-20 rounded-md mt-6">
                            <p className="text-sm text-neutral-70">This block will display related posts from the selected categories. If no categories are selected, posts from the same category as the current post will be shown.</p>
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: commonSave,
});
