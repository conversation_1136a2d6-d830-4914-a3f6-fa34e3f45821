import Swiper from 'swiper';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';

document.addEventListener('DOMContentLoaded', function() {
    const sliderLargeSections = document.querySelectorAll('.slider-large-section');

    if (!sliderLargeSections.length) return;

    sliderLargeSections.forEach(section => {
        const swiperContainer = section.querySelector('.swiper');
        if (!swiperContainer) return;

        const prevButton = section.querySelector('.swiper-button-prev');
        const nextButton = section.querySelector('.swiper-button-next');

        if (!swiperContainer) return;

        // Initialize Swiper
        new Swiper(swiperContainer, {
            modules: [Navigation],
            navigation: {
                nextEl: nextButton,
                prevEl: prevButton,
            },
            slidesPerView: 1,
            spaceBetween: 24,
            breakpoints: {
                // when window width is >= 768px
                768: {
                    slidesPerView: 2,
                    spaceBetween: 24,
                }
            },
            grabCursor: true,
            speed: 300,
            watchSlidesProgress: true,
            watchOverflow: true
        });
    });
});
