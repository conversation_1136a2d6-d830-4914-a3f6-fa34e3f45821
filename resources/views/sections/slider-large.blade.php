@php
    $title = $attributes['title'] ?? '';
    $description = $attributes['description'] ?? '';
    $selectedUsers = $attributes['selectedUsers'] ?? [];
@endphp

<div class="slider-large-section p-8 lg:p-[62px]">
    <div class="container mx-auto">
        <div class="flex flex-col mb-12 lg:flex-row lg:items-end lg:justify-between lg:mb-16">
            <div class="max-w-3xl">
                @if(!empty($title))
                    <div class="mb-4">
                        <x-title variation="large">
                            {!! $title !!}
                        </x-title>
                    </div>
                @endif

                @if(!empty($description))
                    <div>
                        <p class="text-bodyMedium lg:max-w-lg lg:text-bodyMediumDesktop">
                            {!! $description !!}
                        </p>
                    </div>
                @endif
            </div>

            @if(count($selectedUsers) > 2)
                {{-- <div class="swiper-navigation flex items-center space-x-4 relative">
                    <button class="swiper-button-prev after:content-none static m-0 w-5 p-px cursor-pointer" tabindex="0" aria-label="Previous slide"><i class="fa-thin fa-arrow-left text-[#222831]"></i></button>
                    <button class="swiper-button-next after:content-none static m-0 w-5 p-px cursor-pointer" tabindex="0" aria-label="Next slide"><i class="fa-thin fa-arrow-right text-[#222831]"></i></button>
                </div> --}}
            
                <div class="slider-navigation flex items-center space-x-4 relative">
                    <button class="swiper-button-prev after:content-none static m-0 w-5 p-px cursor-pointer" tabindex="0" aria-label="Previous slide"><i class="fa-thin fa-arrow-left text-[#222831]"></i></button>

                    {{-- <button class="swiper-button-prev p-2 rounded-full bg-white border border-[#E8EBF3] disabled:opacity-50" aria-label="Previous slide">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15 18L9 12L15 6" stroke="#3F4A5A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button> --}}

                    <button class="swiper-button-next after:content-none static m-0 w-5 p-px cursor-pointer" tabindex="0" aria-label="Next slide"><i class="fa-thin fa-arrow-right text-[#222831]"></i></button>

                    {{-- <button class="swiper-button-next p-2 rounded-full bg-white border border-[#E8EBF3] disabled:opacity-50" aria-label="Next slide">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 6L15 12L9 18" stroke="#3F4A5A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button> --}}
                </div>
            @endif
        </div>

        @if(!empty($selectedUsers))
            <div class="swiper">
                <div class="swiper-wrapper flex">
                    @foreach($selectedUsers as $user)
                        <div class="swiper-slide h-auto self-stretch">
                            <div class="bg-[#F4F6FA] h-full rounded-3xl p-5">
                                <div class="flex items-center mb-6">
                                    <div class="w-12 h-12 mr-3 flex-shrink-0">
                                        <img
                                            src="{{ $user['profileImage'] }}"
                                            alt="{{ $user['name'] }}"
                                            class="w-12 h-12 rounded-full object-cover"
                                        />
                                    </div>
                                    <div>
                                        <div class="text-bodyMedium lg:text-bodyMediumDesktop">{{ $user['name'] }}</div>
                                        @if(!empty($user['position']))
                                            <div class="text-bodySmall lg:text-bodySmallDesktop text-[#566276]">{{ $user['position'] }}</div>
                                        @endif
                                    </div>
                                </div>

                                @if(!empty($user['mentorFeedback']))
                                    <div class="mentor-feedback">
                                        <p class="text-bodySmall lg:text-bodySmallDesktop text-[#3F4A5A]">
                                            {!! $user['mentorFeedback'] !!}
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @else
            <div class="text-center py-8 text-neutral-60">
                No users selected. Please edit this block to add users.
            </div>
        @endif
    </div>
</div>
