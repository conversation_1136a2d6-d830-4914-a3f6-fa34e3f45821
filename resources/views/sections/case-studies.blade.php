@php
    $bgColors = [
        'blue-bg' => ['bg' => '#F1F6FA', 'icon' => '#0072A9', 'left' => asset('images/cs_blue_left.svg'), 'right' => asset('images/cs_blue_right.svg')],
        'green-bg' => ['bg' => '#ECF8F0', 'icon' => '#538564', 'left' => asset('images/cs_green_left.svg'), 'right' => asset('images/cs_green_right.svg')],
        'peach-bg' => ['bg' => '#FAF3F1', 'icon' => '#E04328', 'left' => asset('images/cs_peach_left.svg'), 'right' => asset('images/cs_peach_right.svg')],
    ];
@endphp

<div class="case-studies-section relative flex justify-center items-center">
    <div class="container px-5 py-16">
        <div class="mb-16 max-w-xl m-auto lg:text-center">
            @if(!empty($attributes['title']))
                <x-title class="mb-2" variation="large">{!! $attributes['title'] !!}</x-title>
            @endif
            @if(!empty($attributes['subtitle']))
                <p class="text-bodyLarge lg:text-bodyLargeDesktop">{!! $attributes['subtitle'] !!}</p>
            @endif
        </div>

        <div>
            <div class="swiper">
                <div class="swiper-pagination !top-60 !left-40 max-w-44 pr-1 -ml-1 pl-px h-4 lg:hidden"></div>
                <div class="swiper-wrapper">
                    @if(!empty($attributes['items']))
                        @foreach($attributes['items'] as $index => $caseStudy)
                            @php
                                $bgClass = match($index % 3) {
                                    0 => 'blue-bg',
                                    1 => 'green-bg',
                                    2 => 'peach-bg',
                                };
                                $colors = $bgColors[$bgClass];
                            @endphp
                            <div class="swiper-slide flex flex-col gap-6 lg:flex-row">
                                <div class="flex flex-col pointer-events-none items-start p-6 pt-8 rounded-3xl overflow-hidden lg:w-1/3 lg:cutout-br-large lg:h-[280px]" style="background-image: url('{{ $colors['left'] }}'); background-size: cover;">
                                    <div class="mb-6 relative overflow-hidden lg:h-40">
                                        @if(!empty($caseStudy['title']))
                                            <x-title class="mb-2" variation="small">{!! $caseStudy['title'] !!}</x-title>
                                        @endif
                                        @if(!empty($caseStudy['description']))
                                            <p class="text-bodySmall dynamic-clamp lg:text-bodyExtraSmallDesktop">
                                                {!! $caseStudy['description'] !!}
                                            </p>
                                        @endif
                                    </div>
                                    @if(!empty($caseStudy['caseStudyButtonText']))
                                        <x-button 
                                            type="link"
                                            variant="primary"
                                            size="small"
                                            :href="$caseStudy['caseStudyButtonLink'] ?? '#'">
                                            {!! $caseStudy['caseStudyButtonText'] !!}
                                        </x-button>
                                    @endif
                                </div>
                                <div class="flex flex-col justify-between gap-4 lg:w-1/3">
                                    <div class="flex rounded-3xl p-6 relative h-full overflow-hidden" style="background-color: {{ $colors['bg'] }};">
                                        @if(!empty($caseStudy['topBoxTitle']) || !empty($caseStudy['topBoxDescription']))
                                            <div class="flex flex-col w-28">
                                                @if(!empty($caseStudy['topBoxTitle']))
                                                    <x-title variation="medium">{!! $caseStudy['topBoxTitle'] !!}</x-title>
                                                @endif
                                                @if(!empty($caseStudy['topBoxDescription']))
                                                    <p class="text-bodySmall lg:text-bodySmallDesktop">{!! $caseStudy['topBoxDescription'] !!}</p>
                                                @endif
                                            </div>
                                        @endif
                                        @if(!empty($caseStudy['topBoxImage']))
                                            <img src="{!! $caseStudy['topBoxImage'] !!}" alt="Top Box Image" class="absolute right-0 top-0 h-full w-48 object-cover cutout-fade rounded-3xl overflow-hidden" />
                                        @endif
                                    </div>

                                    <div class="flex rounded-3xl p-6 relative h-full overflow-hidden" style="background-color: {{ $colors['bg'] }};">
                                        @if(!empty($caseStudy['bottomBoxTitle']) || !empty($caseStudy['bottomBoxDescription']))
                                            <div class="flex flex-col w-28">
                                                @if(!empty($caseStudy['bottomBoxTitle']))
                                                    <x-title variation="medium">{!! $caseStudy['bottomBoxTitle'] !!}</x-title>
                                                @endif
                                                @if(!empty($caseStudy['bottomBoxDescription']))
                                                    <p class="text-bodySmall lg:text-bodySmallDesktop">{!! $caseStudy['bottomBoxDescription'] !!}</p>
                                                @endif
                                            </div>
                                        @endif
                                        @if(!empty($caseStudy['bottomBoxImage']))
                                            <img src="{!! $caseStudy['bottomBoxImage'] !!}" alt="Bottom Box Image" class="absolute right-0 top-0 h-full w-44 object-cover cutout-fade rounded-3xl overflow-hidden" />
                                        @endif
                                    </div>
                                </div>
                                <div class="flex flex-col px-6 pb-4 pt-8 rounded-3xl overflow-hidden lg:w-1/3" style="background-image: url('{{ $colors['right'] }}'); background-size: cover;">
                                    @if(!empty($caseStudy['caseStudiesTitle']))
                                        <x-title class="mb-3" variation="small">{!! $caseStudy['caseStudiesTitle'] !!}</x-title>
                                    @endif
                                    @if(!empty($caseStudy['caseStudiesList']) && is_array($caseStudy['caseStudiesList']))
                                        <ul class="">
                                            @foreach($caseStudy['caseStudiesList'] as $listItem)
                                                @php
                                                    $parsed = '';

                                                    if (is_string($listItem)) {
                                                        $parsed = $listItem;
                                                    } elseif (is_array($listItem)) {
                                                        if (isset($listItem['props']['children']) && is_array($listItem['props']['children'])) {
                                                            foreach ($listItem['props']['children'] as $child) {
                                                                if (is_string($child)) {
                                                                    $parsed .= $child;
                                                                }
                                                            }
                                                        }
                                                    }
                                                @endphp

                                                @if($parsed !== '')
                                                    <div class="flex gap-2 mb-3">
                                                        <div class="flex items-center justify-center rounded-full min-w-8 h-8" style="background-color: {{ $colors['bg'] }};">
                                                            <i class="fa-regular fa-check" style="color: {{ $colors['icon'] }};"></i>
                                                        </div>
                                                        <li class="w-full flex items-center text-bodySmall lg:text-bodyExtraSmallDesktop">{!! $parsed !!}</li>
                                                    </div>
                                                @endif
                                            @endforeach
                                        </ul>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>

        @if(!empty($attributes['buttonText']))
            <div class="flex justify-center mt-8">
                <x-button 
                    type="link"
                    variant="secondary"
                    icon="link"
                    :href="$attributes['buttonLink'] ?? '#'">
                    {!! $attributes['buttonText'] !!}
                </x-button>
            </div>
        @endif
    </div>
</div>
