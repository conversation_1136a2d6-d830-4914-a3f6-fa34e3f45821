@php
    $title = $attributes['title'] ?? 'Our Statistics';
    $stats = $attributes['stats'] ?? [];

    // Calculate the grid layout
    $totalItems = count($stats) + 1; // +1 for the title
    $gridClass = 'grid-cols-1';

    if ($totalItems <= 2) {
        $gridClass = 'grid-cols-1 md:grid-cols-2';
    } elseif ($totalItems <= 4) {
        $gridClass = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-' . $totalItems;
    } else {
        $gridClass = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
    }
@endphp

<div class="statistics-section">
    <div class="container px-5 py-8 mx-auto lg:py-[62px]">
        <div class="grid {!! $gridClass !!} gap-6">
            {{-- Title block --}}
            <div class="flex items-end mt-auto">
                <x-title variation="large">{!! $title !!}</x-title>
            </div>

            {{-- Stats blocks --}}
            @foreach($stats as $stat)
                @php
                    // Calculate height based on percentage (min 116px, max 354px)
                    $minHeight = 116;
                    $maxHeight = 354;
                    $heightRange = $maxHeight - $minHeight;
                    $percentage = $stat['height'] ?? 0;
                    $height = $minHeight + ($heightRange * ($percentage / 100));
                @endphp

                @php
                    $statId = 'stat-' . $loop->index;
                @endphp
                <div id="{{ $statId }}" class="stat-item mt-auto bg-[#66BCE5] rounded-[24px] flex flex-col justify-end p-6 relative overflow-hidden min-h-[116px] h-auto">
                    {{-- Background with noise overlay --}}
                    <div class="absolute inset-0 bg-[#66BCE5] -z-10"></div>
                    <div class="absolute inset-0 -z-5" style="background-image: url('{{ asset('images/noise.svg') }}'); background-size: 200px; opacity: 0.15;"></div>

                    <div class="relative z-10">
                        @if(!empty($stat['value']))
                            <x-title variation="hero" level="h2" class="text-white">{!! $stat['value'] !!}</x-title>
                        @endif

                        @if(!empty($stat['label']))
                            <span class="block text-bodySmall lg:text-bodySmallDesktop text-[#F4F6FA]">{!! $stat['label'] !!}</span>
                        @endif
                    </div>
                </div>

                {{-- Custom style for this specific stat's height on desktop --}}
                <style>
                    @media (min-width: 1024px) {
                        #{{ $statId }} {
                            height: {{ $height }}px;
                        }
                    }
                </style>
            @endforeach
        </div>
    </div>
</div>
