import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps,
} from '@wordpress/block-editor';
import { Button, SelectControl } from '@wordpress/components';
import { Fragment, useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor
} from './editor';

registerBlockType('sage/text-image', {
    apiVersion: 2,
    title: 'Text & Image',
    icon: 'format-image',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        blockBackground: {
            type: 'string',
            default: '',
        },
        items: {
            type: 'array',
            default: [],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { blockBackground, items } = attributes;
        const [ activeTab, setActiveTab ] = useState(0);

        const addItem = () => {
            const newItem = {
                type: 'simple',
                title: '',
                description: '',
                image: '',
                advancedFields: {
                    background: 'light',
                    subRepeater: [],
                    buttonLink: '',
                    buttonText: '',
                    description: '',
                    logo: '',
                    companyName: '',
                },
            };
            const newItems = [ ...items, newItem ];
            setAttributes({ items: newItems });
            setActiveTab(newItems.length - 1);
        };

        if ( items.length === 0 ) {
            addItem();
        }

        const handleItemChange = (index, field, value) => {
            const updatedItems = [ ...items ];
            updatedItems[index] = { ...updatedItems[index], [field]: value };
            setAttributes({ items: updatedItems });
        };

        const handleAdvancedFieldChange = (index, field, value) => {
            const updatedItems = [ ...items ];
            updatedItems[index].advancedFields = {
                ...updatedItems[index].advancedFields,
                [field]: value,
            };
            setAttributes({ items: updatedItems });
        };

        const addRepeaterItem = (index) => {
            const updatedItems = [ ...items ];
            const subRepeater = updatedItems[index].advancedFields.subRepeater || [];
            subRepeater.push({ count: '', label: '' });
            updatedItems[index].advancedFields.subRepeater = subRepeater;
            setAttributes({ items: updatedItems });
        };

        const removeRepeaterItem = (index, subIndex) => {
            const updatedItems = [ ...items ];
            const subRepeater = updatedItems[index].advancedFields.subRepeater || [];
            subRepeater.splice(subIndex, 1);
            updatedItems[index].advancedFields.subRepeater = subRepeater;
            setAttributes({ items: updatedItems });
        };

        const handleTypeChange = (value) => {
            handleItemChange(activeTab, 'type', value);
            if ( value === 'advanced' && !items[activeTab].advancedFields ) {
                handleAdvancedFieldChange(activeTab, 'background', 'light');
            }
        };

        const blockProps = useBlockProps();

        return (
            <Fragment>
                <div {...blockProps}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Text & Image</h2>
                        </div>

                        <ImageUploader
                            image={blockBackground}
                            onSelect={(url) => setAttributes({ blockBackground: url })}
                            onRemove={() => setAttributes({ blockBackground: '' })}
                            label="Background Image"
                            description="Upload a background image for the entire section."
                            height={48}
                            objectFit="cover"
                            altText="Background Image"
                        />

                        <div className="mb-6 mt-6">
                            <TabNavigation
                                items={items}
                                activeTab={activeTab}
                                onTabChange={setActiveTab}
                                onAddItem={addItem}
                                onRemoveItem={() => {
                                    const updatedItems = [...items];
                                    updatedItems.splice(activeTab, 1);
                                    setAttributes({ items: updatedItems });
                                    setActiveTab(Math.max(activeTab - 1, 0));
                                }}
                                getItemTitle={(item, index) => item.title && item.title.trim().length > 0 ? item.title : `Item ${index + 1}`}
                                itemName="Item"
                                addButtonTitle="Add New Item"
                            />

                            {items.length > 0 && (
                                <ItemEditor
                                    itemIndex={activeTab}
                                    onRemove={() => {
                                        const updatedItems = [...items];
                                        updatedItems.splice(activeTab, 1);
                                        setAttributes({ items: updatedItems });
                                        setActiveTab(Math.max(activeTab - 1, 0));
                                    }}
                                    itemName="Item"
                                    showRemoveButton={items.length > 1}
                                >
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">Item Type</label>
                                        <div className="p-3 bg-white rounded-md border border-neutral-30">
                                            <SelectControl
                                                value={items[activeTab]?.type}
                                                options={[
                                                    { label: 'Simple', value: 'simple' },
                                                    { label: 'Advanced', value: 'advanced' },
                                                ]}
                                                onChange={handleTypeChange}
                                            />
                                        </div>
                                        <p className="mt-1 text-xs text-neutral-60">Select the type of content to display</p>
                                    </div>

                                    <TextControl
                                        label="Title"
                                        value={items[activeTab]?.title}
                                        onChange={(value) => handleItemChange(activeTab, 'title', value)}
                                        placeholder="Enter title"
                                        tagName="h4"
                                    />

                                    <TextControl
                                        label="Description"
                                        value={items[activeTab]?.description}
                                        onChange={(value) => handleItemChange(activeTab, 'description', value)}
                                        placeholder="Enter description"
                                        tagName="p"
                                        multiline={true}
                                    />

                                    <ImageUploader
                                        image={items[activeTab].image}
                                        onSelect={(url) => handleItemChange(activeTab, 'image', url)}
                                        onRemove={() => handleItemChange(activeTab, 'image', '')}
                                        label="Item Image"
                                        description="Upload an image for this item."
                                        height={48}
                                        objectFit="cover"
                                        altText="Item Image"
                                    />

                                    {items[activeTab]?.type === 'advanced' && (
                                        <div className="mt-6 p-4 bg-neutral-20 rounded-md">
                                            <div className="mb-4 border-b border-neutral-30 pb-2">
                                                <h3 className="text-base font-medium text-neutral-90">Advanced Settings</h3>
                                            </div>

                                            <div className="mb-4">
                                                <label className="block text-sm font-medium text-neutral-70 mb-2">Background Style</label>
                                                <div className="p-3 bg-white rounded-md border border-neutral-30">
                                                    <SelectControl
                                                        value={items[activeTab]?.advancedFields?.background}
                                                        options={[
                                                            { label: 'Light', value: 'light' },
                                                            { label: 'Dark', value: 'dark' },
                                                        ]}
                                                        onChange={(value) => handleAdvancedFieldChange(activeTab, 'background', value)}
                                                    />
                                                </div>
                                                <p className="mt-1 text-xs text-neutral-60">Choose the background style for this item</p>
                                            </div>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                <TextControl
                                                    label="Button Text"
                                                    value={items[activeTab]?.advancedFields?.buttonText}
                                                    onChange={(value) => handleAdvancedFieldChange(activeTab, 'buttonText', value)}
                                                    placeholder="Enter button text"
                                                    tagName="p"
                                                />

                                                <TextControl
                                                    label="Button Link"
                                                    value={items[activeTab]?.advancedFields?.buttonLink}
                                                    onChange={(value) => handleAdvancedFieldChange(activeTab, 'buttonLink', value)}
                                                    placeholder="Enter URL"
                                                />
                                            </div>

                                            <TextControl
                                                label="Advanced Description"
                                                value={items[activeTab]?.advancedFields?.description}
                                                onChange={(value) => handleAdvancedFieldChange(activeTab, 'description', value)}
                                                placeholder="Enter advanced description"
                                                tagName="p"
                                                multiline={true}
                                            />

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                <div>
                                                    <ImageUploader
                                                        image={items[activeTab]?.advancedFields?.logo}
                                                        onSelect={(url) => handleAdvancedFieldChange(activeTab, 'logo', url)}
                                                        onRemove={() => handleAdvancedFieldChange(activeTab, 'logo', '')}
                                                        label="Company Logo"
                                                        description="Upload a company logo."
                                                        height={48}
                                                        objectFit="contain"
                                                        altText="Company Logo"
                                                    />
                                                </div>

                                                <TextControl
                                                    label="Company Name"
                                                    value={items[activeTab]?.advancedFields?.companyName}
                                                    onChange={(value) => handleAdvancedFieldChange(activeTab, 'companyName', value)}
                                                    placeholder="Enter company name"
                                                    tagName="p"
                                                />
                                            </div>

                                            <div className="mb-4">
                                                <div className="flex justify-between items-center mb-2">
                                                    <label className="block text-sm font-medium text-neutral-70">Stats</label>
                                                    <Button
                                                        className="border border-neutral-30 bg-white hover:bg-neutral-10 text-neutral-70 hover:text-primary-80 py-1.5 px-3 rounded-md transition-all text-sm flex items-center"
                                                        onClick={() => addRepeaterItem(activeTab)}
                                                        icon="plus-alt2"
                                                    >
                                                        Add Stat
                                                    </Button>
                                                </div>
                                                <div className="p-3 bg-white rounded-md border border-neutral-30">
                                                    {!items[activeTab]?.advancedFields?.subRepeater?.length && (
                                                        <p className="text-neutral-60 text-sm mb-2">No stats added yet.</p>
                                                    )}

                                                    {items[activeTab]?.advancedFields?.subRepeater?.map((subItem, i) => (
                                                        <div key={i} className="flex items-center gap-2 mb-2 last:mb-0">
                                                            <div className="grid grid-cols-2 gap-2 flex-grow p-2 bg-neutral-10 rounded-md border border-neutral-30">
                                                                <div>
                                                                    <label className="block text-xs font-medium text-neutral-70 mb-1">Count</label>
                                                                    <RichText
                                                                        tagName="span"
                                                                        className="block w-full p-1 text-sm"
                                                                        placeholder="Count"
                                                                        value={subItem.count}
                                                                        onChange={(value) =>
                                                                            handleAdvancedFieldChange(
                                                                                activeTab,
                                                                                'subRepeater',
                                                                                items[activeTab].advancedFields.subRepeater.map((r, idx) =>
                                                                                    idx === i ? { ...r, count: value } : r
                                                                                )
                                                                            )
                                                                        }
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <label className="block text-xs font-medium text-neutral-70 mb-1">Label</label>
                                                                    <RichText
                                                                        tagName="span"
                                                                        className="block w-full p-1 text-sm"
                                                                        placeholder="Label"
                                                                        value={subItem.label}
                                                                        onChange={(value) =>
                                                                            handleAdvancedFieldChange(
                                                                                activeTab,
                                                                                'subRepeater',
                                                                                items[activeTab].advancedFields.subRepeater.map((r, idx) =>
                                                                                    idx === i ? { ...r, label: value } : r
                                                                                )
                                                                            )
                                                                        }
                                                                    />
                                                                </div>
                                                            </div>
                                                            <Button
                                                                className="p-1 text-xs bg-red-50 border border-red-300 text-red-600 rounded"
                                                                onClick={() => removeRepeaterItem(activeTab, i)}
                                                                icon="trash"
                                                                iconSize={16}
                                                                aria-label="Remove stat"
                                                            />
                                                        </div>
                                                    ))}
                                                </div>
                                                <p className="mt-1 text-xs text-neutral-60">Add statistics with count and label</p>
                                            </div>
                                        </div>
                                    )}
                                </ItemEditor>
                            )}
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: commonSave,
});
