import Swiper from 'swiper';
import { Autoplay, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';

document.addEventListener('DOMContentLoaded', function () {
    const blogHero = document.querySelector('.blog-hero');
    const blogArticles = document.querySelector('.blog-articles');

    if (blogHero && blogArticles) {
        const popularArticlesSlider = new Swiper('.popular-articles-slider', {
            modules: [Autoplay, Pagination],
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.popular-articles-slider .swiper-pagination',
                clickable: true,
            },
            slidesPerView: 1,
            spaceBetween: 24,
        });

        var ajaxurl = '/wp-admin/admin-ajax.php';
        var currentPage = 1;
        var postsPerPage = 6;
        var postsGrid = document.getElementById('posts-grid');
        var maxPages = parseInt(postsGrid.getAttribute('data-max-pages'));
        var totalPosts = parseInt(postsGrid.getAttribute('data-found-posts'));
        var postsDisplayed = document.querySelectorAll('.post-item').length;
        var currentCategory = 'all';

        var pills = document.querySelectorAll('.category-pill');
        pills.forEach(function(pill) {
            pill.addEventListener('click', function(e) {
                e.preventDefault();
                var category = this.getAttribute('data-category');
                currentCategory = category;
                currentPage = 1;

                pills.forEach(function(p) {
                    p.classList.remove('active', 'bg-[#66BCE5]', '!border-[#99D2ED]', 'text-white');
                    p.classList.add('border-[#CFD5E3]', 'text-[#6E7B91]');
                });

                this.classList.remove('border-[#CFD5E3]', 'text-[#6E7B91]');
                this.classList.add('active', 'bg-[#66BCE5]', '!border-[#99D2ED]', 'text-white');

                fetchPosts(category, currentPage, true);
            });
        });

        var loadMoreButton = document.getElementById('load-more');
        if (loadMoreButton) {
            loadMoreButton.addEventListener('click', function(e) {
                e.preventDefault();

                if (currentPage < maxPages) {
                    currentPage++;
                    fetchPosts(currentCategory, currentPage, false);
                } else {
                    loadMoreButton.style.display = 'none';
                    loadMoreButton.parentElement.style.display = 'none';
                }
            });
        }

        function fetchPosts(category, page, replacePosts) {
            var data = {
                action: 'load_more_posts',
                category: category,
                page: page,
                posts_per_page: postsPerPage,
            };

            fetch(ajaxurl, {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(data),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (replacePosts) {
                        postsGrid.innerHTML = data.data.posts_html;
                        postsDisplayed = data.data.posts_returned;
                    } else {
                        postsGrid.insertAdjacentHTML('beforeend', data.data.posts_html);
                        postsDisplayed += data.data.posts_returned;
                    }

                    maxPages = data.data.max_pages;
                    totalPosts = data.data.found_posts;

                    updateLoadMoreButton();
                } else {
                    console.error(data.data);
                    loadMoreButton.style.display = 'none';
                    loadMoreButton.parentElement.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error fetching posts:', error);
            });
        }

        function updateLoadMoreButton() {
            var postsRemaining = totalPosts - postsDisplayed;

            if (postsRemaining > 0) {
                var postsToLoad = Math.min(postsRemaining, postsPerPage);
                loadMoreButton.textContent = 'Load ' + postsToLoad + ' more';
                loadMoreButton.style.display = 'inline-block';
            } else {
                loadMoreButton.style.display = 'none';
                loadMoreButton.parentElement.style.display = 'none';
            }
        }

        updateLoadMoreButton();
    }
});
