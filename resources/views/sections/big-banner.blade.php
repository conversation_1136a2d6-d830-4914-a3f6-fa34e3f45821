<div class="big-banner-section flex justify-center items-center">
    <div class="container px-5 py-8 lg:pt-[124px] lg:pb-[62px]">
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-8">
            <div class="lg:w-1/2 lg:pr-12 mb-6 lg:mb-0">
                @if(!empty($attributes['title']))
                    <x-title variation="large">{!! $attributes['title'] !!}</x-title>
                @endif
            </div>
            <div class="lg:w-1/2">
                @if(!empty($attributes['description']))
                    <p>{!! $attributes['description'] !!}</p>
                @endif
            </div>
        </div>

        @if(!empty($attributes['mainImage']))
            <div class="mb-8">
                <img src="{{ $attributes['mainImage'] }}" alt="Banner Image" class="w-full h-auto rounded-3xl object-cover">
            </div>
        @endif

        @if(!empty($attributes['pills']) && count($attributes['pills']) > 0)
            <div class="flex flex-wrap justify-center gap-4 lg:justify-start">
                @foreach($attributes['pills'] as $pill)
                    <div class="flex items-center py-[10px] px-[12px] bg-[#F4F6FA] text-[#00567F] rounded-lg">
                        @if(!empty($pill['image']))
                            <img src="{{ $pill['image'] }}" alt="" class="w-5 h-5 object-contain mr-2">
                        @endif
                        <span class="text-bodySmall lg:text-bodySmallDesktop font-normal pt-0.5 block">{!! $pill['text'] !!}</span>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
</div>
