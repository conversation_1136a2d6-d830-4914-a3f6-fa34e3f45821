document.addEventListener('DOMContentLoaded', function () {
    if (typeof EnlighterJS !== 'undefined') {
        EnlighterJS.init('.post-content pre');
    } else {
        console.error('EnlighterJS is not defined');
    }

    const tocLinks = document.querySelectorAll('.table-of-contents a');
    const headings = Array.from(tocLinks).map(link => {
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        return { link, targetElement };
    });

    document.querySelectorAll('.table-of-contents li').forEach(li => {
        const link = li.querySelector('a');
        if (!link || !link.textContent.trim()) {
            li.remove();
        }
    });

    let lastActiveLink = null;

    function activateCurrentHeading() {
        let currentActive = null;

        headings.forEach(({ link, targetElement }) => {
            const { top } = targetElement.getBoundingClientRect();
            if (top <= window.innerHeight * 0.5) {
                currentActive = link;
            }
        });

        if (currentActive !== lastActiveLink) {
            if (lastActiveLink) {
                lastActiveLink.style.color = '#6E7B91';
                lastActiveLink.style.fontWeight = '400';
                lastActiveLink.parentElement.classList.remove('active');
            }
            if (currentActive) {
                currentActive.style.color = '#222831';
                currentActive.style.fontWeight = '600';
                currentActive.parentElement.classList.add('active');
            }
            lastActiveLink = currentActive;
        }
    }

    tocLinks.forEach(link => {
        link.addEventListener('click', event => {
            event.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    window.addEventListener('scroll', activateCurrentHeading);
});
