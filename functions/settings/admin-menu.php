<?php
/**
 * Admin Menu Setup
 *
 * Sets up the admin menu for theme settings.
 */

/**
 * Register Theme Settings Menu and Submenus
 */
add_action('admin_menu', function() {
    add_menu_page(
        'Theme Settings',
        'Theme Settings',
        'manage_options',
        'theme-settings',
        '__return_false',
        'dashicons-admin-generic',
        60
    );
    add_submenu_page(
        'theme-settings',
        'Contact Forms',
        'Contact Forms',
        'manage_options',
        'theme-settings-contact-forms',
        'contact_forms_page'
    );
    add_submenu_page(
        'theme-settings',
        'Technology Icons',
        'Technology Icons',
        'manage_options',
        'theme-settings-technology-icons',
        'technology_icons_page'
    );
    add_submenu_page(
        'theme-settings',
        'Positions',
        'Positions',
        'manage_options',
        'theme-settings-positions',
        'positions_page'
    );
    add_submenu_page(
        'theme-settings',
        'Case Study Icons',
        'Case Study Icons',
        'manage_options',
        'theme-settings-case-study-icons',
        'case_study_icons_page'
    );
    add_submenu_page(
        'theme-settings',
        'Image Optimization',
        'Image Optimization',
        'manage_options',
        'theme-settings-image-optimization',
        'image_optimization_page'
    );
});
