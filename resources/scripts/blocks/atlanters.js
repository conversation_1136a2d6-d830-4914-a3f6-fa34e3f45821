import { registerBlockType } from '@wordpress/blocks';
import { RichText, useBlockProps } from '@wordpress/block-editor';
import { Button, Modal, ComboboxControl } from '@wordpress/components';
import { Fragment, useState } from '@wordpress/element';
import { useSelect } from '@wordpress/data';
import {
    TextControl,
    ImageUploader
} from './editor';

registerBlockType('sage/atlanters', {
    apiVersion: 2,
    title: 'Atlanters',
    icon: 'groups',
    category: 'layout',
    attributes: {
        tagline: { type: 'string', default: '' },
        title: { type: 'string', default: '' },
        items: { type: 'array', default: [] },
    },
    edit: ({ attributes, setAttributes }) => {
        const { tagline, title, items } = attributes;
        const [activeTab, setActiveTab] = useState(-1);
        const [atlantersSearch, setAtlantersSearch] = useState('');
        const [isStatModalOpen, setIsStatModalOpen] = useState(false);
        const [statTemp, setStatTemp] = useState({
            title: '',
            description: '',
            image: null,
            link: '',
        });
        const [editingStatIndex, setEditingStatIndex] = useState(null);

        const atlanters = useSelect(
            (select) =>
                select('core').getEntityRecords('postType', 'atlanters', {
                    search: atlantersSearch,
                    per_page: 100,
                    context: 'edit',
                    _embed: true,
                }),
            [atlantersSearch]
        );

        const addAtlanterItem = (post) => {
            const featuredImage =
                post._embedded &&
                post._embedded['wp:featuredmedia'] &&
                post._embedded['wp:featuredmedia'][0].source_url;
            const newItem = {
                type: 'atlanter',
                postId: post.id,
                slug: post.slug,
                postTitle: post.title.rendered,
                featuredImage: featuredImage || null,
                position: post.meta && post.meta.position ? post.meta.position : '',
            };
            setAttributes({ items: [...items, newItem] });
            setActiveTab(items.length);
        };

        const addStatItem = (stat) => {
            const newItem = {
                type: 'stat',
                title: stat.title,
                description: stat.description,
                image: stat.image,
                link: stat.link,
            };
            setAttributes({ items: [...items, newItem] });
            setActiveTab(items.length);
        };

        const updateStatItem = (index, stat) => {
            const newItems = [...items];
            newItems[index] = {
                type: 'stat',
                title: stat.title,
                description: stat.description,
                image: stat.image,
                link: stat.link,
            };
            setAttributes({ items: newItems });
        };

        const removeItem = (index) => {
            const newItems = [...items];
            newItems.splice(index, 1);
            setAttributes({ items: newItems });
            setActiveTab(newItems.length ? Math.max(activeTab - 1, 0) : -1);
        };

        const moveItem = (index, direction) => {
            const newIndex = index + direction;
            if (newIndex < 0 || newIndex >= items.length) return;
            const newItems = [...items];
            [newItems[index], newItems[newIndex]] = [newItems[newIndex], newItems[index]];
            setAttributes({ items: newItems });
            setActiveTab(newIndex);
        };

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Atlanters</h2>
                        </div>

                        <TextControl
                            label="Tagline"
                            value={tagline}
                            onChange={(value) => setAttributes({ tagline: value })}
                            placeholder="Enter tagline"
                            tagName="h3"
                        />

                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter title"
                            tagName="h2"
                        />

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-neutral-70 mb-2">Add Team Members</label>
                            <div className="p-3 bg-white rounded-md border border-neutral-30">
                                <ComboboxControl
                                    label="Search Atlanters"
                                    value={atlantersSearch}
                                    onChange={(val) => {
                                        setAtlantersSearch(val);
                                        const selected =
                                            atlanters &&
                                            atlanters.find((post) => post.title.rendered === val);
                                        if (selected) {
                                            addAtlanterItem(selected);
                                            setAtlantersSearch('');
                                        }
                                    }}
                                    options={(atlanters || []).map((post) => ({
                                        value: post.title.rendered,
                                        label: post.title.rendered,
                                    }))}
                                />
                            </div>
                            <p className="mt-1 text-xs text-neutral-60">Search and select team members to add to the grid</p>
                        </div>

                        <div className="mb-4">
                            <div className="flex justify-between items-center mb-2">
                                <label className="block text-sm font-medium text-neutral-70">Stats</label>
                                <Button
                                    variant="primary"
                                    className="bg-primary-80 text-white hover:bg-primary-90 py-1.5 px-3 rounded-md text-sm"
                                    onClick={() => {
                                        setStatTemp({ title: '', description: '', image: null, link: '' });
                                        setEditingStatIndex(null);
                                        setIsStatModalOpen(true);
                                    }}
                                >
                                    Add Stat
                                </Button>
                            </div>
                            <p className="text-xs text-neutral-60 mb-2">Add statistics to display alongside team members</p>
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium text-neutral-70 mb-2">Team Members & Stats</label>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                {items.map((item, index) => (
                                    <div
                                        key={index}
                                        className={`flex flex-col h-36 rounded-lg overflow-hidden relative border border-neutral-30 ${activeTab === index ? 'ring-2 ring-primary-80' : ''} ${
                                            item.type === 'atlanter' ? 'atlanter-card' : ''
                                        }`}
                                        data-slug={item.type === 'atlanter' ? item.slug : undefined}
                                        onClick={() => setActiveTab(index)}
                                    >
                                        {item.type === 'atlanter' ? (
                                            <div
                                                className="w-full h-full bg-cover bg-top"
                                                style={{
                                                    backgroundImage: item.featuredImage
                                                        ? `url(${item.featuredImage})`
                                                        : 'none',
                                                }}
                                            />
                                        ) : (
                                            <div
                                                className="w-full h-full bg-neutral-20 flex items-center justify-center"
                                            >
                                                <span className="text-lg font-medium text-neutral-90">{item.title}</span>
                                            </div>
                                        )}
                                        <div
                                            className="absolute bottom-0 w-full bg-black bg-opacity-50 text-white p-1 text-center text-sm truncate"
                                        >
                                            {item.type === 'atlanter' ? item.postTitle : item.title}
                                        </div>
                                        {activeTab === index && (
                                            <div
                                                className="absolute top-0 right-0 flex justify-end w-full bg-white bg-opacity-80 p-1"
                                                onClick={(e) => e.stopPropagation()}
                                            >
                                                <Button
                                                    className="p-1 text-xs bg-neutral-10 border border-neutral-30 rounded mr-1"
                                                    onClick={() => moveItem(index, -1)}
                                                    icon="arrow-up-alt2"
                                                    iconSize={16}
                                                    aria-label="Move up"
                                                />
                                                <Button
                                                    className="p-1 text-xs bg-neutral-10 border border-neutral-30 rounded mr-1"
                                                    onClick={() => moveItem(index, 1)}
                                                    icon="arrow-down-alt2"
                                                    iconSize={16}
                                                    aria-label="Move down"
                                                />
                                                {item.type === 'stat' && (
                                                    <Button
                                                        className="p-1 text-xs bg-neutral-10 border border-neutral-30 rounded mr-1"
                                                        onClick={() => {
                                                            setStatTemp({
                                                                title: item.title,
                                                                description: item.description,
                                                                image: item.image,
                                                                link: item.link || '',
                                                            });
                                                            setEditingStatIndex(index);
                                                            setIsStatModalOpen(true);
                                                        }}
                                                        icon="edit"
                                                        iconSize={16}
                                                        aria-label="Edit"
                                                    />
                                                )}
                                                <Button
                                                    className="p-1 text-xs bg-red-50 border border-red-300 text-red-600 rounded"
                                                    onClick={() => removeItem(index)}
                                                    icon="trash"
                                                    iconSize={16}
                                                    aria-label="Remove"
                                                />
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                            {items.length === 0 && (
                                <div className="p-4 bg-neutral-20 rounded-md text-center text-neutral-70">
                                    No team members or stats added yet. Use the controls above to add content.
                                </div>
                            )}
                            <p className="mt-1 text-xs text-neutral-60">Click on an item to select it. Selected items can be moved or removed.</p>
                        </div>
                    </div>
                </div>
                {isStatModalOpen && (
                    <Modal
                        title={editingStatIndex !== null ? 'Edit Stat' : 'Add Stat'}
                        onRequestClose={() => setIsStatModalOpen(false)}
                    >
                        <div className="p-4">
                            <TextControl
                                label="Stat Title"
                                value={statTemp.title}
                                onChange={(value) => setStatTemp({ ...statTemp, title: value })}
                                placeholder="Enter stat title"
                            />

                            <TextControl
                                label="Stat Description"
                                value={statTemp.description}
                                onChange={(value) => setStatTemp({ ...statTemp, description: value })}
                                placeholder="Enter stat description"
                                multiline={true}
                            />

                            <TextControl
                                label="Stat Link (Optional)"
                                value={statTemp.link}
                                onChange={(value) => setStatTemp({ ...statTemp, link: value })}
                                placeholder="Enter URL"
                                description="Add a link for this stat (optional)"
                            />

                            <div className="mb-4">
                                <label className="block text-sm font-medium text-neutral-70 mb-2">Stat Image (Optional)</label>
                                <ImageUploader
                                    image={statTemp.image ? statTemp.image.url : ''}
                                    onSelect={(url) => {
                                        // Create a media object with the URL to maintain compatibility
                                        const media = { url };
                                        setStatTemp({ ...statTemp, image: media });
                                    }}
                                    onRemove={() => setStatTemp({ ...statTemp, image: null })}
                                    height={48}
                                    objectFit="cover"
                                    altText="Stat Image"
                                />
                                <p className="mt-1 text-xs text-neutral-60">Upload an image to display with this stat (optional)</p>
                            </div>

                            <div className="flex justify-end mt-6">
                                <Button
                                    className="mr-2 bg-neutral-20 text-neutral-70 hover:bg-neutral-30 py-1.5 px-3 rounded-md text-sm"
                                    onClick={() => setIsStatModalOpen(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="primary"
                                    className="bg-primary-80 text-white hover:bg-primary-90 py-1.5 px-3 rounded-md text-sm"
                                    onClick={() => {
                                        if (editingStatIndex !== null) {
                                            updateStatItem(editingStatIndex, statTemp);
                                            setEditingStatIndex(null);
                                        } else {
                                            addStatItem(statTemp);
                                        }
                                        setIsStatModalOpen(false);
                                    }}
                                >
                                    {editingStatIndex !== null ? 'Update Stat' : 'Add Stat'}
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        const { tagline, title, items } = attributes;
        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h3" value={tagline} />
                    <RichText.Content tagName="h2" value={title} />
                    {items?.map((item, index) => {
                        if (item.type === 'atlanter') {
                            return (
                                <div key={`item-${index}`}>
                                    <RichText.Content tagName="h3" value={item.postTitle} />
                                </div>
                            );
                        }
                        if (item.type === 'stat') {
                            return (
                                <div key={`item-${index}`}>
                                    <RichText.Content tagName="h3" value={item.title} />
                                    <RichText.Content tagName="p" value={item.description} />
                                </div>
                            );
                        }
                        return null;
                    })}
                </div>
                <div data-dynamic="sage/atlanters"></div>
            </div>
        );
    },
});
