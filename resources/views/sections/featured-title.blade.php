@php
    $hasButton       = !empty($attributes['buttonText']);
    $size            = $attributes['size'] ?? 'large';

    $containerClass  = $hasButton
        ? 'relative flex flex-col justify-between items-start px-7 py-20 lg:items-center lg:flex-row lg:px-16 lg:py-36'
        : ($size === 'small'
            ? 'px-7 py-8 lg:px-5 lg:py-[62px]'
            : 'px-7 py-20 lg:px-5 lg:py-36');

    $titleVariation  = $hasButton ? 'small' : ($size === 'small' ? 'small' : 'hero');

    $titleClass      = $hasButton
                        ? 'mb-4 lg:mb-0 lg:w-7/12'
                        : ($size === 'small' ? 'text-center' : '');
@endphp

<div class="featured-title-section relative flex justify-center items-center">
    <div class="container {{ $containerClass }}">
        @if($hasButton)
            <div class="blue-dark-bg -z-10 absolute w-[calc(100%_-_2.5rem)] h-full left-[1.25rem] top-0 rounded-3xl">
            </div>
        @endif
        <x-title
            :variation="$titleVariation"
            highlight="light"
            size="large"
            class="{{ $titleClass }}"
        >
            {!! $attributes['title'] !!}
        </x-title>

        @if($hasButton)
            <div>
                <x-button
                    type="link"
                    variant="primary"
                    size="medium"
                    :href="$attributes['buttonLink'] ?? '#'">
                    {!! $attributes['buttonText'] !!}
                </x-button>
            </div>
        @endif
    </div>

    @if(!$hasButton)
        @if($size === 'small' && !empty($attributes['backgroundImage']))
            <x-floating-bg
                background="{{ $attributes['backgroundImage'] }}"
                backgroundSize="{{ $attributes['backgroundSize'] ?? 'cover' }}"
                backgroundPosition="{{ $attributes['backgroundPosition'] ?? 'center' }}"
            />
        @elseif($size === 'large')
            <x-floating-bg color="blue-dark" />
        @endif
    @endif
</div>
