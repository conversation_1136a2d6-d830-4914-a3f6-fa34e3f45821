@php
    $blockId = 'case-study-technologies-' . uniqid();
@endphp

<div id="{{ $blockId }}" class="case-studies-technologies relative flex justify-center items-center mb-8">
    <x-case-study-styles :accentColor="$attributes['accentColor']" blockClass="case-studies-technologies" :blockId="$blockId" />
    <div class="container px-5 py-10">
        <div class="relative max-w-3xl flex items-start gap-6 lg:gap-12">
            <div class="timeline after:w-1 after:h-full after:absolute after:left-[19px]">
                <div class="icon-container flex items-center justify-center w-[42px] h-[42px] rounded-full">
                    <img class="w-5 h-5 object-contain" src="{{ $attributes['icon'] }}" />
                </div>
            </div>
            <div>
                @if(!empty($attributes['title']))
                    <x-title class="mb-4" variation="medium">{!! $attributes['title'] !!}</x-title>
                @endif

                @if(!empty($attributes['sections']))
                    @php
                        $colors = [
                            'bg-gradient-to-r from-[#ECF0F6] to-[rgba(0,0,0,0)]',
                            'bg-gradient-to-r from-[#F4F6EC] to-[rgba(0,0,0,0)]',
                            'bg-gradient-to-r from-[#F5ECF6] to-[rgba(0,0,0,0)]'
                        ];
                    @endphp
                    <div class="flex flex-col gap-4">
                        @foreach($attributes['sections'] as $section)
                            <div class="technology-section flex flex-col items-start gap-6 py-4 pr-7 pl-5 rounded-xl overflow-hidden {{ $colors[$loop->index % 3] }} lg:flex-row lg:items-center">
                                @if(!empty($section['sectionTitle']))
                                    <span class="min-w-[114px] max-w-[114px] text-bodySmall uppercase font-medium pt-1 text-[#3F4A5A] lg:text-bodySmallDesktop">{!! $section['sectionTitle'] !!}</span>
                                @endif
                                @php
                                    $techIcons = [];
                                    // Get technology icons from the central repository
                                    if (!empty($section['techIcons'])) {
                                        $allTechIcons = get_option('technology_icons', []);
                                        // Debug output
                                        if (isset($_GET['debug_tech_icons'])) {
                                            echo '<pre>Section Tech Icons: ' . json_encode($section['techIcons']) . '</pre>';
                                            echo '<pre>All Tech Icons: ' . json_encode($allTechIcons) . '</pre>';
                                        }

                                        foreach ($section['techIcons'] as $iconId) {
                                            foreach ($allTechIcons as $icon) {
                                                if ($icon['id'] == $iconId) {
                                                    $techIcons[] = $icon['url'];
                                                    break;
                                                }
                                            }
                                        }
                                    }

                                    // Add legacy custom images if any
                                    if (!empty($section['images'])) {
                                        $techIcons = array_merge($techIcons, $section['images']);
                                    }

                                    // Debug output
                                    if (isset($_GET['debug_tech_icons'])) {
                                        echo '<pre>Final Tech Icons: ' . json_encode($techIcons) . '</pre>';
                                    }
                                @endphp

                                @if(!empty($techIcons))
                                    <div class="technology-images flex flex-wrap gap-[2.5px]">
                                        @foreach($techIcons as $img)
                                            <div class="technology-image flex items-center justify-center w-12 h-12 bg-white rounded-lg overflow-hidden border border-[#E8EBF3]">
                                                <img class="object-contain w-8 h-8" src="{!! $img !!}" />
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
