document.addEventListener('DOMContentLoaded', function() {
    const partnershipModelsSection = document.querySelector('.js-partnership-models');
    if (!partnershipModelsSection) return;
    const swiperElements = partnershipModelsSection.querySelectorAll('.swiper[class*="swiper-benefits-"]');
    swiperElements.forEach(initializeSmoothInfiniteScroll);
});

function initializeSmoothInfiniteScroll(swiperElement) {
    const swiperWrapper = swiperElement.querySelector('.swiper-wrapper');
    let originalSlides = Array.from(swiperElement.querySelectorAll('.swiper-slide'));
    if (!swiperWrapper || originalSlides.length === 0) return;
    if (swiperElement.swiper) {
        swiperElement.swiper.destroy();
    }
    Array.from(swiperWrapper.children).forEach((child, index) => {
        if (!originalSlides.includes(child)) {
            swiperWrapper.removeChild(child);
        }
    });
    originalSlides = Array.from(swiperElement.querySelectorAll('.swiper-slide'));
    const wrapperStyle = window.getComputedStyle(swiperWrapper);
    const gapSize = parseInt(wrapperStyle.gap) || 24;
    const totalSetWidth = swiperWrapper.scrollWidth;
    for (let i = 0; i < 2; i++) {
        originalSlides.forEach(slide => {
            const clone = slide.cloneNode(true);
            swiperWrapper.appendChild(clone);
        });
    }
    swiperWrapper.style.display = 'flex'; 
    swiperWrapper.style.flexWrap = 'nowrap';
    const scrollSpeed = 60;
    const animationDuration = totalSetWidth / scrollSpeed;
    const uniqueId = `carousel-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    swiperElement.id = uniqueId;
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        #${uniqueId} .swiper-wrapper {
            animation: ${uniqueId}-scroll ${animationDuration}s linear infinite;
            will-change: transform;
        }
        @keyframes ${uniqueId}-scroll {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-${totalSetWidth + 24}px);
            }
        }
    `;
    document.head.appendChild(styleElement);
    swiperElement.addEventListener('mouseenter', () => {
        swiperWrapper.style.animationPlayState = 'paused';
    });
    swiperElement.addEventListener('mouseleave', () => {
        swiperWrapper.style.animationPlayState = 'running';
    });
    const resizeHandler = debounce(() => {
        const newTotalWidth = swiperWrapper.scrollWidth / 3;
        const newDuration = newTotalWidth / scrollSpeed;
        styleElement.textContent = `
            #${uniqueId} .swiper-wrapper {
                animation: ${uniqueId}-scroll ${newDuration}s linear infinite;
                will-change: transform;
            }
            @keyframes ${uniqueId}-scroll {
                0% {
                    transform: translateX(0);
                }
                100% {
                    transform: translateX(-${newTotalWidth}px);
                }
            }
        `;
    }, 250);
    window.addEventListener('resize', resizeHandler);
}

function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

export default {};
