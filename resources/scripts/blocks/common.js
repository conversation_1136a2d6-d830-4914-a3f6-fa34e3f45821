import { useBlockProps } from '@wordpress/block-editor';

export const commonAttributes = {
    title: {
        type: 'string',
    },
    subtitle: {
        type: 'string',
    },
    tagline: {
        type: 'string',
    },
    description: {
        type: 'string',
    },
    backgroundImage: {
        type: 'string',
    },
    buttonText: {
        type: 'string',
    },
    buttonLink: {
        type: 'string',
    },
    startNumber: {
        type: 'string',
    },
    endNumber: {
        type: 'string',
    },
    unit: {
        type: 'string',
    },
    numberLabel: {
        type: 'string',
    },
    alignment: {
        type: 'string',
        default: 'center',
    },
};

export const commonSave = () => {
    const blockProps = useBlockProps.save();
    return <div {...blockProps}></div>;
};