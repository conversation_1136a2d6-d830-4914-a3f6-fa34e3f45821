<?php
/**
 * Image Optimization Functions
 *
 * Automatically converts uploaded images to WebP and AVIF formats
 * and serves them when the browser supports these formats.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class to handle image optimization
 */
class AtlantBH_Image_Optimizer {
    /**
     * Constructor
     */
    public function __construct() {
        // Add support for WebP and AVIF mime types
        add_filter('mime_types', [$this, 'add_webp_avif_mime_types']);

        // Convert images to WebP and AVIF on upload
        add_filter('wp_handle_upload', [$this, 'convert_uploaded_image'], 10, 2);

        // Convert image subsizes to WebP and AVIF
        add_filter('image_make_intermediate_size', [$this, 'convert_intermediate_image']);

        // Serve WebP/AVIF images when available
        add_filter('wp_get_attachment_image_src', [$this, 'serve_optimized_image'], 10, 4);
        add_filter('wp_calculate_image_srcset', [$this, 'serve_optimized_srcset'], 10, 5);

        // Filter content to replace image URLs
        add_filter('the_content', [$this, 'replace_image_urls_in_content']);
    }

    /**
     * Add WebP and AVIF mime types
     */
    public function add_webp_avif_mime_types($mime_types) {
        $mime_types['webp'] = 'image/webp';
        $mime_types['avif'] = 'image/avif';
        return $mime_types;
    }

    /**
     * Convert uploaded image to WebP and AVIF
     */
    public function convert_uploaded_image($file, $context) {
        // Only process on upload, not other contexts
        if ($context !== 'upload') {
            return $file;
        }

        // Only process image files
        if (!$this->is_convertible_image($file['file'])) {
            return $file;
        }

        // Get settings
        $settings = $this->get_settings();

        // Skip if optimization is disabled
        if (!$settings['enable_optimization']) {
            return $file;
        }

        // Convert to WebP if enabled
        if ($settings['enable_webp']) {
            $this->convert_to_webp($file['file']);
        }

        // Convert to AVIF if enabled
        if ($settings['enable_avif']) {
            $this->convert_to_avif($file['file']);
        }

        return $file;
    }

    /**
     * Convert intermediate image sizes to WebP and AVIF
     */
    public function convert_intermediate_image($file) {
        // Get settings
        $settings = $this->get_settings();

        // Skip if optimization is disabled
        if (!$settings['enable_optimization']) {
            return $file;
        }

        // Only process image files
        if (!$this->is_convertible_image($file)) {
            return $file;
        }

        // Convert to WebP if enabled
        if ($settings['enable_webp']) {
            $this->convert_to_webp($file);
        }

        // Convert to AVIF if enabled
        if ($settings['enable_avif']) {
            $this->convert_to_avif($file);
        }

        return $file;
    }

    /**
     * Serve optimized image when available
     */
    public function serve_optimized_image($image, $attachment_id, $size, $icon) {
        if (!$image) {
            return $image;
        }

        // Get settings
        $settings = $this->get_settings();

        // Skip if optimization is disabled or serving optimized images is disabled
        if (!$settings['enable_optimization'] || !$settings['serve_optimized_images']) {
            return $image;
        }

        // Check browser support
        $supports_webp = $this->browser_supports_webp();
        $supports_avif = $this->browser_supports_avif();

        // Original image URL
        $image_url = $image[0];
        $image_path = $this->get_file_path_from_url($image_url);

        if (!$image_path) {
            return $image;
        }

        // Try AVIF first if supported and enabled
        if ($supports_avif && $settings['enable_avif']) {
            $avif_path = $this->get_avif_path($image_path);
            if (file_exists($avif_path)) {
                $image[0] = $this->get_url_from_file_path($avif_path);
                return $image;
            }
        }

        // Then try WebP if supported and enabled
        if ($supports_webp && $settings['enable_webp']) {
            $webp_path = $this->get_webp_path($image_path);
            if (file_exists($webp_path)) {
                $image[0] = $this->get_url_from_file_path($webp_path);
                return $image;
            }
        }

        return $image;
    }

    /**
     * Serve optimized srcset when available
     */
    public function serve_optimized_srcset($sources, $size_array, $image_src, $image_meta, $attachment_id) {
        if (empty($sources)) {
            return $sources;
        }

        // Get settings
        $settings = $this->get_settings();

        // Skip if optimization is disabled or serving optimized images is disabled
        if (!$settings['enable_optimization'] || !$settings['serve_optimized_images']) {
            return $sources;
        }

        // Check browser support
        $supports_webp = $this->browser_supports_webp();
        $supports_avif = $this->browser_supports_avif();

        // If neither format is supported, return original sources
        if (!$supports_webp && !$supports_avif) {
            return $sources;
        }

        foreach ($sources as &$source) {
            $image_url = $source['url'];
            $image_path = $this->get_file_path_from_url($image_url);

            if (!$image_path) {
                continue;
            }

            // Try AVIF first if supported and enabled
            if ($supports_avif && $settings['enable_avif']) {
                $avif_path = $this->get_avif_path($image_path);
                if (file_exists($avif_path)) {
                    $source['url'] = $this->get_url_from_file_path($avif_path);
                    continue;
                }
            }

            // Then try WebP if supported and enabled
            if ($supports_webp && $settings['enable_webp']) {
                $webp_path = $this->get_webp_path($image_path);
                if (file_exists($webp_path)) {
                    $source['url'] = $this->get_url_from_file_path($webp_path);
                }
            }
        }

        return $sources;
    }

    /**
     * Replace image URLs in content
     */
    public function replace_image_urls_in_content($content) {
        // Skip if content is empty
        if (empty($content) || !is_string($content)) {
            return $content;
        }

        // Get settings
        $settings = $this->get_settings();

        // Skip if optimization is disabled or serving optimized images is disabled
        if (!$settings['enable_optimization'] || !$settings['serve_optimized_images']) {
            return $content;
        }

        // Check browser support
        $supports_webp = $this->browser_supports_webp();
        $supports_avif = $this->browser_supports_avif();

        // If neither format is supported, return original content
        if (!$supports_webp && !$supports_avif) {
            return $content;
        }

        // Use DOMDocument to parse HTML and find images
        if (!function_exists('mb_convert_encoding')) {
            return $content;
        }

        // Create a new DOMDocument
        $dom = new DOMDocument();

        // Load HTML with error suppression
        @$dom->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'));

        // Find all images
        $images = $dom->getElementsByTagName('img');

        // If no images, return original content
        if ($images->length === 0) {
            return $content;
        }

        // Process each image
        $modified = false;
        foreach ($images as $img) {
            $src = $img->getAttribute('src');
            $image_path = $this->get_file_path_from_url($src);

            if (!$image_path) {
                continue;
            }

            // Try AVIF first if supported and enabled
            if ($supports_avif && $settings['enable_avif']) {
                $avif_path = $this->get_avif_path($image_path);
                if (file_exists($avif_path)) {
                    $img->setAttribute('src', $this->get_url_from_file_path($avif_path));
                    $modified = true;
                    continue;
                }
            }

            // Then try WebP if supported and enabled
            if ($supports_webp && $settings['enable_webp']) {
                $webp_path = $this->get_webp_path($image_path);
                if (file_exists($webp_path)) {
                    $img->setAttribute('src', $this->get_url_from_file_path($webp_path));
                    $modified = true;
                }
            }
        }

        // If no modifications were made, return original content
        if (!$modified) {
            return $content;
        }

        // Save the modified HTML
        $body = $dom->getElementsByTagName('body')->item(0);
        $new_content = '';
        foreach ($body->childNodes as $child) {
            $new_content .= $dom->saveHTML($child);
        }

        return $new_content;
    }

    /**
     * Check if browser supports WebP
     */
    public function browser_supports_webp() {
        // Check Accept header for image/webp
        if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false) {
            return true;
        }

        // Check User-Agent for Chrome, Edge, Firefox, etc.
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $ua = $_SERVER['HTTP_USER_AGENT'];

            // Chrome 32+, Edge 18+, Firefox 65+, Opera 19+, Safari 14+
            if (
                (strpos($ua, 'Chrome/') !== false && preg_match('/Chrome\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 32) ||
                (strpos($ua, 'Edg/') !== false && preg_match('/Edg\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 18) ||
                (strpos($ua, 'Firefox/') !== false && preg_match('/Firefox\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 65) ||
                (strpos($ua, 'OPR/') !== false && preg_match('/OPR\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 19) ||
                (strpos($ua, 'Safari/') !== false && strpos($ua, 'Version/') !== false && preg_match('/Version\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 14)
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if browser supports AVIF
     */
    public function browser_supports_avif() {
        // Check Accept header for image/avif
        if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/avif') !== false) {
            return true;
        }

        // Check User-Agent for Chrome, Firefox, etc.
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $ua = $_SERVER['HTTP_USER_AGENT'];

            // Chrome 85+, Firefox 86+, Safari 16+
            if (
                (strpos($ua, 'Chrome/') !== false && preg_match('/Chrome\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 85) ||
                (strpos($ua, 'Firefox/') !== false && preg_match('/Firefox\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 86) ||
                (strpos($ua, 'Safari/') !== false && strpos($ua, 'Version/') !== false && preg_match('/Version\/(\d+)/', $ua, $matches) && (int)$matches[1] >= 16)
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if image is convertible
     */
    public function is_convertible_image($file_path) {
        // Get file extension
        $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        // Only convert JPEG, PNG, and GIF (skip SVG and other formats)
        return in_array($ext, ['jpg', 'jpeg', 'png', 'gif']);
    }

    /**
     * Convert image to WebP
     */
    public function convert_to_webp($file_path, $force_regenerate = false) {
        try {
            // Get file extension
            $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

            // Skip if already WebP or AVIF
            if (in_array($ext, ['webp', 'avif', 'svg'])) {
                return false;
            }

            // Get WebP path
            $webp_path = $this->get_webp_path($file_path);

            // Get current quality setting
            $settings = $this->get_settings();
            $current_quality = $settings['webp_quality'];

            // Get stored quality information
            $quality_info = get_option('atlantbh_webp_quality_info', []);
            $file_key = md5($file_path); // Use MD5 hash as key to avoid path issues
            $stored_quality = isset($quality_info[$file_key]) ? $quality_info[$file_key] : null;

            // Skip if WebP already exists, is newer than the source, and quality hasn't changed
            if (!$force_regenerate &&
                file_exists($webp_path) &&
                filemtime($webp_path) >= filemtime($file_path) &&
                $stored_quality === $current_quality) {
                return true;
            }

            // Check if GD is available with WebP support
            if (!function_exists('imagewebp')) {
                return false;
            }

            // Load image based on type
            switch ($ext) {
                case 'jpg':
                case 'jpeg':
                    $image = imagecreatefromjpeg($file_path);
                    break;
                case 'png':
                    $image = imagecreatefrompng($file_path);
                    if ($image) {
                        // Ensure alpha channel is preserved
                        imagealphablending($image, false);
                        imagesavealpha($image, true);
                    }
                    break;
                case 'gif':
                    $image = imagecreatefromgif($file_path);
                    break;
                default:
                    return false;
            }

            if (!$image) {
                return false;
            }

            // Convert palette image to true color (crucial for GIFs)
            if (function_exists('imagepalettetotruecolor') && !imagepalettetotruecolor($image)) {
                // If conversion fails, create a new true color image
                $width = imagesx($image);
                $height = imagesy($image);

                $truecolor = imagecreatetruecolor($width, $height);

                // Set transparent background for the new image
                imagealphablending($truecolor, false);
                imagesavealpha($truecolor, true);
                $transparent = imagecolorallocatealpha($truecolor, 255, 255, 255, 127);
                imagefilledrectangle($truecolor, 0, 0, $width, $height, $transparent);

                // Copy the original image onto the true color image
                imagecopyresampled($truecolor, $image, 0, 0, 0, 0, $width, $height, $width, $height);

                // Free the original image
                imagedestroy($image);

                $image = $truecolor;
            }

            // Get quality setting
            $settings = $this->get_settings();
            $quality = $settings['webp_quality'];

            // Create a temporary file first to avoid issues
            $temp_file = $webp_path . '.tmp';

            // Save as WebP to temporary file
            $result = imagewebp($image, $temp_file, $quality);

            // Free memory
            imagedestroy($image);

            // If successful, rename the temporary file to the final file
            if ($result && file_exists($temp_file)) {
                if (file_exists($webp_path)) {
                    unlink($webp_path);
                }
                rename($temp_file, $webp_path);

                // Store quality information
                $quality_info = get_option('atlantbh_webp_quality_info', []);
                $file_key = md5($file_path);
                $quality_info[$file_key] = $quality;
                update_option('atlantbh_webp_quality_info', $quality_info);

                return true;
            } else if (file_exists($temp_file)) {
                unlink($temp_file);
            }

            return false;
        } catch (Exception $e) {
            error_log('Error converting to WebP: ' . $e->getMessage() . ' - File: ' . $file_path);
            return false;
        }
    }

    /**
     * Convert image to AVIF
     */
    public function convert_to_avif($file_path, $force_regenerate = false) {
        try {
            // Get file extension
            $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

            // Skip if already AVIF
            if (in_array($ext, ['avif', 'svg'])) {
                return false;
            }

            // Get AVIF path
            $avif_path = $this->get_avif_path($file_path);

            // Get current quality setting
            $settings = $this->get_settings();
            $current_quality = $settings['avif_quality'];

            // Get stored quality information
            $quality_info = get_option('atlantbh_avif_quality_info', []);
            $file_key = md5($file_path); // Use MD5 hash as key to avoid path issues
            $stored_quality = isset($quality_info[$file_key]) ? $quality_info[$file_key] : null;

            // Skip if AVIF already exists, is newer than the source, and quality hasn't changed
            if (!$force_regenerate &&
                file_exists($avif_path) &&
                filemtime($avif_path) >= filemtime($file_path) &&
                $stored_quality === $current_quality) {
                return true;
            }

            // Check if GD is available with AVIF support
            if (!function_exists('imageavif')) {
                return false;
            }

            // Load image based on type
            switch ($ext) {
                case 'jpg':
                case 'jpeg':
                    $image = imagecreatefromjpeg($file_path);
                    break;
                case 'png':
                    $image = imagecreatefrompng($file_path);
                    if ($image) {
                        // Ensure alpha channel is preserved
                        imagealphablending($image, false);
                        imagesavealpha($image, true);
                    }
                    break;
                case 'gif':
                    $image = imagecreatefromgif($file_path);
                    break;
                default:
                    return false;
            }

            if (!$image) {
                return false;
            }

            // Convert palette image to true color (crucial for GIFs)
            if (function_exists('imagepalettetotruecolor') && !imagepalettetotruecolor($image)) {
                // If conversion fails, create a new true color image
                $width = imagesx($image);
                $height = imagesy($image);

                $truecolor = imagecreatetruecolor($width, $height);

                // Set transparent background for the new image
                imagealphablending($truecolor, false);
                imagesavealpha($truecolor, true);
                $transparent = imagecolorallocatealpha($truecolor, 255, 255, 255, 127);
                imagefilledrectangle($truecolor, 0, 0, $width, $height, $transparent);

                // Copy the original image onto the true color image
                imagecopyresampled($truecolor, $image, 0, 0, 0, 0, $width, $height, $width, $height);

                // Free the original image
                imagedestroy($image);

                $image = $truecolor;
            }

            // Get quality setting
            $settings = $this->get_settings();
            $quality = $settings['avif_quality'];

            // Create a temporary file first to avoid issues
            $temp_file = $avif_path . '.tmp';

            // Save as AVIF to temporary file
            $result = imageavif($image, $temp_file, $quality);

            // Free memory
            imagedestroy($image);

            // If successful, rename the temporary file to the final file
            if ($result && file_exists($temp_file)) {
                if (file_exists($avif_path)) {
                    unlink($avif_path);
                }
                rename($temp_file, $avif_path);

                // Store quality information
                $quality_info = get_option('atlantbh_avif_quality_info', []);
                $file_key = md5($file_path);
                $quality_info[$file_key] = $quality;
                update_option('atlantbh_avif_quality_info', $quality_info);

                return true;
            } else if (file_exists($temp_file)) {
                unlink($temp_file);
            }

            return false;
        } catch (Exception $e) {
            error_log('Error converting to AVIF: ' . $e->getMessage() . ' - File: ' . $file_path);
            return false;
        }
    }

    /**
     * Load image from file
     */
    public function load_image($file_path) {
        // Get file extension
        $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

        // Load image based on type (only JPG and PNG)
        switch ($ext) {
            case 'jpg':
            case 'jpeg':
                $image = imagecreatefromjpeg($file_path);
                break;
            case 'png':
                $image = imagecreatefrompng($file_path);
                if ($image) {
                    // Ensure alpha channel is preserved
                    imagealphablending($image, false);
                    imagesavealpha($image, true);
                }
                break;
            default:
                return false;
        }

        return $image;
    }

    /**
     * Get WebP path for an image
     */
    public function get_webp_path($file_path) {
        return preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.webp', $file_path);
    }

    /**
     * Get AVIF path for an image
     */
    public function get_avif_path($file_path) {
        return preg_replace('/\.(jpg|jpeg|png|gif)$/i', '.avif', $file_path);
    }

    /**
     * Get file path from URL
     */
    public function get_file_path_from_url($url) {
        // Get upload directory info
        $upload_dir = wp_upload_dir();

        // Convert URL to path
        $url = set_url_scheme($url);

        // Check if URL is in uploads directory
        if (strpos($url, $upload_dir['baseurl']) === false) {
            return false;
        }

        // Replace upload URL with upload path
        $path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $url);

        // Check if file exists
        if (!file_exists($path)) {
            return false;
        }

        return $path;
    }

    /**
     * Get URL from file path
     */
    public function get_url_from_file_path($file_path) {
        // Get upload directory info
        $upload_dir = wp_upload_dir();

        // Check if path is in uploads directory
        if (strpos($file_path, $upload_dir['basedir']) === false) {
            return false;
        }

        // Replace upload path with upload URL
        $url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $file_path);

        return $url;
    }



    /**
     * Get default settings
     */
    public function get_default_settings() {
        return [
            'enable_optimization' => 1,
            'serve_optimized_images' => 1,
            'enable_webp' => 1,
            'webp_quality' => 85,
            'enable_avif' => 1,
            'avif_quality' => 75,
        ];
    }

    /**
     * Get settings
     */
    public function get_settings() {
        $defaults = $this->get_default_settings();
        $saved_settings = get_option('atlantbh_image_optimization', $defaults);

        // Make sure all keys exist
        $settings = wp_parse_args($saved_settings, $defaults);

        return $settings;
    }
}

// Initialize the image optimizer
$atlantbh_image_optimizer = new AtlantBH_Image_Optimizer();
