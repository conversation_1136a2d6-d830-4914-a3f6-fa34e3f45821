document.addEventListener("DOMContentLoaded", function () {
    function getButtonWidth(button, spinnerVisible) {
        const clone = button.cloneNode(true);
        clone.style.position = "absolute";
        clone.style.visibility = "hidden";
        clone.style.width = "auto";
        const spinner = clone.querySelector(".fa-spinner");
        if (spinner) {
            if (!spinnerVisible) {
                spinner.style.display = "none";
            } else {
                spinner.style.display = "inline-block";
                spinner.style.opacity = 1;
            }
        }
        document.body.appendChild(clone);
        const width = clone.offsetWidth;
        document.body.removeChild(clone);
        return width;
    }

    function enableSpinner(button) {
        const spinner = button.querySelector(".fa-spinner");
        const collapsedWidth = getButtonWidth(button, false);
        const expandedWidth = getButtonWidth(button, true);

        button.style.width = collapsedWidth + "px";
        if (spinner) {
            spinner.style.display = "none";
            spinner.style.opacity = 0;
        }

        void button.offsetWidth;
        button.style.transition = "width 300ms ease";
        button.style.width = expandedWidth + "px";

        button.addEventListener("transitionend", function widthHandler(e) {
            if (e.propertyName === "width") {
                button.removeEventListener("transitionend", widthHandler);
                if (spinner) {
                    spinner.style.display = "inline-block";
                    setTimeout(() => {
                        spinner.style.transition = "opacity 300ms ease";
                        spinner.style.opacity = 1;
                    }, 10);
                }
            }
        });
    }

    function disableSpinner(button) {
        const spinner = button.querySelector(".fa-spinner");
        const collapsedWidth = getButtonWidth(button, false);
        const expandedWidth = getButtonWidth(button, true);

        if (spinner) {
            spinner.style.transition = "opacity 300ms ease";
            spinner.style.opacity = 0;
            spinner.addEventListener("transitionend", function opacityHandler(e) {
                if (e.propertyName === "opacity") {
                    spinner.removeEventListener("transitionend", opacityHandler);
                    spinner.style.display = "none";
                    button.style.width = expandedWidth + "px";
                    void button.offsetWidth;
                    button.style.transition = "width 300ms ease";
                    button.style.width = collapsedWidth + "px";
                    button.addEventListener("transitionend", function widthHandler(e) {
                        if (e.propertyName === "width") {
                            button.removeEventListener("transitionend", widthHandler);
                            button.style.width = "";
                            button.style.transition = "";
                        }
                    });
                }
            });
        }
    }

    document.querySelectorAll("button").forEach(button => {
        const spinner = button.querySelector(".fa-spinner");
        if (spinner) {
            spinner.style.opacity = 0;
            spinner.style.display = "none";

            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    if (mutation.attributeName === "class") {
                        if (button.classList.contains("spinning")) {
                            enableSpinner(button);
                        } else {
                            disableSpinner(button);
                        }
                    }
                });
            });
            observer.observe(button, { attributes: true, attributeFilter: ["class"] });
        }
    });
});
