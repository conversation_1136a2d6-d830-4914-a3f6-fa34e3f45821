@props(['variation' => 'default', 'level' => null, 'highlight' => null])

@php
    $gradientMain = 'font-normal text-transparent bg-clip-text bg-gradient-to-r from-neutral-100 to-neutral-80';
    $gradientHighlightDark = 'font-normal text-transparent bg-clip-text bg-gradient-to-tr from-neutral-90 via-primary-80 via-75% to-gradient-turquoise bg-[length:200%]';
    $gradientHighlightLight = 'font-normal text-transparent bg-clip-text bg-gradient-to-tr from-[#003C57] via-primary-80 via-50% to-[#8ADDA7] bg-[length:200%]';

    $config = [
        'hero' => [
            'level' => 'h1',
            'classes' => "text-title leading-title tracking-title font-normal $gradientMain lg:text-titleDesktop lg:leading-titleDesktop lg:tracking-titleDesktop",
        ],
        'large' => [
            'level' => 'h2',
            'classes' => "text-large leading-large tracking-large font-normal $gradientMain lg:text-largeDesktop lg:leading-largeDesktop lg:tracking-largeDesktop",
        ],
        'medium' => [
            'level' => 'h3',
            'classes' => 'text-medium leading-medium tracking-medium font-normal text-neutral-100 lg:text-mediumDesktop lg:leading-mediumDesktop lg:tracking-mediumDesktop',
        ],
        'small' => [
            'level' => 'h4',
            'classes' => 'text-small leading-small tracking-small font-normal text-neutral-100 lg:text-smallDesktop lg:leading-smallDesktop lg:tracking-smallDesktop',
        ],
        'tagline' => [
            'level' => 'h5',
            'classes' => 'text-tagline leading-tagline tracking-tagline font-medium text-[#566276] lg:text-taglineDesktop lg:leading-taglineDesktop lg:tracking-taglineDesktop',
        ],
        'subtitle' => [
            'level' => 'h6',
            'classes' => 'text-subtitle leading-small tracking-subtitle font-medium text-neutral-100 lg:text-subtitleDesktop lg:leading-smallDesktop lg:tracking-subtitleDesktop',
        ],
        'default' => [
            'level' => 'p',
            'classes' => 'text-base font-normal text-neutral-100',
        ],
    ];

    $config = $config[$variation] ?? $config['default'];
    $level = $level ?? $config['level'];
    $classes = $config['classes'];

    if ($highlight) {
        $gradientHighlight = $highlight === 'light' ? $gradientHighlightLight : $gradientHighlightDark;
        $content = preg_replace('/<strong>(.*?)<\/strong>/i', '<span class="'.$gradientHighlight.'">$1</span>', $slot);
    } else {
        $content = $slot;
    }
@endphp

<{{ $level }} {{ $attributes->merge(['class' => $classes]) }}>
    {!! $content !!}
</{{ $level }}>
