import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    EditorContainer
} from './editor';

registerBlockType('sage/internship-benefits', {
    apiVersion: 2,
    title: 'Internship Benefits',
    icon: 'welcome-learn-more',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string',
            default: '',
        },
        description: {
            type: 'string',
            default: '',
        },
        benefits: {
            type: 'array',
            default: [],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, description, benefits } = attributes;
        const [activeBenefit, setActiveBenefit] = useState(0);

        // Add a new benefit item
        const addBenefit = () => {
            const newBenefit = {
                title: '',
                description: '',
                icon: '',
            };
            const newBenefits = [...benefits, newBenefit];
            setAttributes({ benefits: newBenefits });
            setActiveBenefit(newBenefits.length - 1);
        };

        // Initialize with one benefit if none exist
        if (benefits.length === 0) {
            addBenefit();
        }

        // Handle benefit item changes
        const handleBenefitChange = (index, field, value) => {
            const updatedBenefits = [...benefits];
            updatedBenefits[index] = { ...updatedBenefits[index], [field]: value };
            setAttributes({ benefits: updatedBenefits });
        };

        // Remove a benefit item
        const removeBenefit = (index) => {
            const updatedBenefits = [...benefits];
            updatedBenefits.splice(index, 1);
            setAttributes({ benefits: updatedBenefits });
            setActiveBenefit(Math.min(index, updatedBenefits.length - 1));
        };

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Internship Benefits">
                    <div className="mb-6">
                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter title"
                            tagName="h2"
                        />

                        <TextControl
                            label="Description"
                            value={description}
                            onChange={(value) => setAttributes({ description: value })}
                            placeholder="Enter description"
                            tagName="p"
                            multiline={true}
                        />
                    </div>

                    <div className="mt-6">
                        <TabNavigation
                            items={benefits}
                            activeTab={activeBenefit}
                            onTabChange={setActiveBenefit}
                            onAddItem={addBenefit}
                            onRemoveItem={() => removeBenefit(activeBenefit)}
                            getItemTitle={(item, index) => item.title || `Benefit ${index + 1}`}
                            itemName="Benefit"
                            addButtonTitle="Add New Benefit"
                        />

                        {benefits.length > 0 && (
                            <ItemEditor
                                itemIndex={activeBenefit}
                                onRemove={() => removeBenefit(activeBenefit)}
                                itemName="Benefit"
                                showRemoveButton={benefits.length > 1}
                            >
                                <TextControl
                                    label="Benefit Title"
                                    value={benefits[activeBenefit].title}
                                    onChange={(value) => handleBenefitChange(activeBenefit, 'title', value)}
                                    placeholder="Enter benefit title"
                                    tagName="h3"
                                />

                                <TextControl
                                    label="Benefit Description"
                                    value={benefits[activeBenefit].description}
                                    onChange={(value) => handleBenefitChange(activeBenefit, 'description', value)}
                                    placeholder="Enter benefit description"
                                    tagName="p"
                                    multiline={true}
                                />

                                <ImageUploader
                                    image={benefits[activeBenefit].icon}
                                    onSelect={(url) => handleBenefitChange(activeBenefit, 'icon', url)}
                                    onRemove={() => handleBenefitChange(activeBenefit, 'icon', '')}
                                    label="Benefit Icon"
                                    description="Upload an icon for this benefit (24x24px recommended)."
                                    objectFit="contain"
                                    altText="Benefit Icon"
                                />
                            </ItemEditor>
                        )}
                    </div>
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
