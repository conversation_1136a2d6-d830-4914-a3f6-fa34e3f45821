import JustValidate from 'just-validate';

document.addEventListener('DOMContentLoaded', () => {
    const form = document.querySelector('#download-form');
    if (!form) return;

    const fileUrl = form.dataset.fileUrl;

    const validation = new JustValidate('#download-form');
    validation.addField('input[name="email"]', [
        { rule: 'required', errorMessage: 'Email is required' },
        { rule: 'email', errorMessage: 'Invalid email format' },
    ])
    .onSuccess((e) => {
        e.preventDefault();
        const email = form.querySelector('input[name="email"]').value;

        fetch('/wp-json/sage/send-cta-file', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, fileUrl }),
        })
        .then(res => res.json())
        .then(data => {
            alert(data.message);
            form.reset();
        })
        .catch(err => {
            console.error(err);
            alert('Error sending file. Try again later.');
        });
    });
});
