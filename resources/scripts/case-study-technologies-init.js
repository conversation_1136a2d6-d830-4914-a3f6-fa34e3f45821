document.addEventListener('DOMContentLoaded', () => {
    // Log technology icons data for debugging
    if (window.technologyIconsData && window.technologyIconsData.icons) {
        console.log('Technology Icons Data (Frontend):', window.technologyIconsData.icons);
    } else {
        console.log('No technology icons data found in window object (Frontend)');
    }

    if (window.innerWidth < 1024) return;

    const sections = document.querySelectorAll('.technology-images');
    if (!sections.length) return;

    sections.forEach((container) => {
        const images = Array.from(container.querySelectorAll('.technology-image'));
        if (images.length > 10) {
            const extraContainer = document.createElement('div');
            extraContainer.className = 'transition-all duration-500 flex gap-[2.5px] overflow-hidden';
            extraContainer.style.maxHeight = '0';

            for (let i = 9; i < images.length; i++) {
                extraContainer.appendChild(images[i]);
            }

            const toggleButton = document.createElement('div');
            toggleButton.className =
                'technology-image flex items-center justify-center w-12 h-12 bg-white rounded-lg overflow-hidden border border-[#E8EBF3] cursor-pointer';

            const iconWrapper = document.createElement('div');
            iconWrapper.className = 'flex transition-transform duration-500';

            const icon = document.createElement('i');
            icon.className = 'fa-regular fa-chevron-down';
            iconWrapper.appendChild(icon);

            toggleButton.appendChild(iconWrapper);
            container.appendChild(toggleButton);
            container.appendChild(extraContainer);

            toggleButton.addEventListener('click', () => {
                if (extraContainer.style.maxHeight === '0px' || extraContainer.style.maxHeight === '0') {
                    extraContainer.style.maxHeight = `${extraContainer.scrollHeight}px`;
                    iconWrapper.classList.add('rotate-180');
                } else {
                    extraContainer.style.maxHeight = `${extraContainer.scrollHeight}px`;
                    setTimeout(() => {
                        extraContainer.style.maxHeight = '0';
                    }, 10);
                    iconWrapper.classList.remove('rotate-180');
                }
            });
        }
    });
});
