<svg width="1392" height="468" viewBox="0 0 1392 468" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3268_7763)">
<g filter="url(#filter0_f_3268_7763)">
<ellipse cx="322" cy="412" rx="298" ry="300" fill="#D300CF" fill-opacity="0.1"/>
</g>
<g filter="url(#filter1_f_3268_7763)">
<ellipse cx="645" cy="324" rx="298" ry="300" fill="#D300CF" fill-opacity="0.1"/>
</g>
<g filter="url(#filter2_f_3268_7763)">
<ellipse cx="958" cy="474" rx="298" ry="300" fill="#D300CF" fill-opacity="0.1"/>
</g>
<g filter="url(#filter3_f_3268_7763)">
<ellipse cx="1094" cy="247" rx="298" ry="300" fill="#D300CF" fill-opacity="0.1"/>
</g>
<g filter="url(#filter4_f_3268_7763)">
<ellipse cx="671" cy="601" rx="298" ry="300" fill="#D300CF" fill-opacity="0.1"/>
</g>
</g>
<defs>
<filter id="filter0_f_3268_7763" x="-476" y="-388" width="1596" height="1600" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_3268_7763"/>
</filter>
<filter id="filter1_f_3268_7763" x="-153" y="-476" width="1596" height="1600" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_3268_7763"/>
</filter>
<filter id="filter2_f_3268_7763" x="160" y="-326" width="1596" height="1600" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_3268_7763"/>
</filter>
<filter id="filter3_f_3268_7763" x="296" y="-553" width="1596" height="1600" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_3268_7763"/>
</filter>
<filter id="filter4_f_3268_7763" x="-127" y="-199" width="1596" height="1600" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_3268_7763"/>
</filter>
<clipPath id="clip0_3268_7763">
<rect width="1392" height="468" rx="24" fill="white"/>
</clipPath>
</defs>
</svg>
