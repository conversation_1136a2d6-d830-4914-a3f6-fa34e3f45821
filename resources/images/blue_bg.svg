<svg width="1392" height="1004" viewBox="0 0 1392 1004" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1939_16720)">
<rect width="1392" height="1004" fill="#EDF2F9"/>
<g opacity="0.2" filter="url(#filter0_f_1939_16720)">
<circle cx="1280" cy="731" r="239" fill="url(#paint0_linear_1939_16720)"/>
</g>
<g opacity="0.2" filter="url(#filter1_f_1939_16720)">
<ellipse cx="812" cy="978" rx="343" ry="301" fill="url(#paint1_linear_1939_16720)"/>
</g>
</g>
<defs>
<filter id="filter0_f_1939_16720" x="541" y="-8" width="1478" height="1478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1939_16720"/>
</filter>
<filter id="filter1_f_1939_16720" x="-31" y="177" width="1686" height="1602" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1939_16720"/>
</filter>
<linearGradient id="paint0_linear_1939_16720" x1="1301" y1="592" x2="1213" y2="1014" gradientUnits="userSpaceOnUse">
<stop stop-color="#39D1D9"/>
<stop offset="1" stop-color="#175255"/>
</linearGradient>
<linearGradient id="paint1_linear_1939_16720" x1="812" y1="677" x2="812" y2="1279" gradientUnits="userSpaceOnUse">
<stop stop-color="#12BEE2"/>
<stop offset="1" stop-color="#1C3BAA"/>
</linearGradient>
<clipPath id="clip0_1939_16720">
<rect width="1392" height="1004" fill="white"/>
</clipPath>
</defs>
</svg>
