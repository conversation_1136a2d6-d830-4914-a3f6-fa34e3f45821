import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    MediaUpload,
    URLInputButton,
    useBlockProps,
} from '@wordpress/block-editor';
import { Button, SelectControl, Panel, PanelBody } from '@wordpress/components';
import {
    EditorContainer,
    SectionHeader,
    ButtonEditor,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    TextControl
} from './editor';
import { Fragment, useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';

registerBlockType('sage/hero', {
    apiVersion: 2,
    title: 'Hero',
    icon: 'welcome-add-page',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        heroType: {
            type: 'string',
            default: 'default',
        },
        topImages: {
            type: 'array',
            default: [{ image: '' }],
        },
        logos: {
            type: 'array',
            default: [{ image: '' }],
        },
        sliderItems: {
            type: 'array',
            default: [{ background: '', description: '', logo: '', companyName: '' }],
        },
        tagline: {
            type: 'string',
            default: '',
        },
        backgroundColor: {
            type: 'string',
            default: '',
        },
        smallImage: {
            type: 'string',
            default: '',
        },
        imagePlacement: {
            type: 'string',
            default: 'normal',
        },
        buttonCaption: {
            type: 'string',
            default: '',
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const {
            heroType,
            title,
            description,
            buttonText,
            buttonLink,
            buttonCaption,
            topImages,
            logos,
            sliderItems,
            tagline,
            backgroundColor,
            smallImage,
            imagePlacement,
        } = attributes;

        const [activeTopImage, setActiveTopImage] = useState(0);
        const [activeLogo, setActiveLogo] = useState(0);
        const [activeSliderItem, setActiveSliderItem] = useState(0);

        const handleRepeaterChange = (items, index, field, value, attrName) => {
            const newItems = [...items];
            newItems[index][field] = value;
            setAttributes({ [attrName]: newItems });
        };

        const addRepeaterItem = (items, attrName, newItem, setActive) => {
            const newItems = [...items, newItem];
            setAttributes({ [attrName]: newItems });
            setActive(newItems.length - 1);
        };

        const removeRepeaterItem = (items, attrName, index, activeState, setActive) => {
            const newItems = [...items];
            newItems.splice(index, 1);
            setAttributes({ [attrName]: newItems });
            setActive(Math.max(activeState - 1, 0));
        };

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Hero</h2>
                        </div>
                    <SectionHeader
                        title="Hero Settings"
                        description="Select the type of hero section you want to display."
                    />
                    <SelectControl
                        label="Hero Type"
                        value={heroType}
                        options={[
                            { label: 'Default', value: 'default' },
                            { label: 'Small', value: 'small' },
                        ]}
                        onChange={(val) => setAttributes({ heroType: val })}
                        className="mb-6"
                    />

                    <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                        <Panel className="border-0 shadow-none">
                            <PanelBody
                                title="Hero Content"
                                initialOpen={true}
                            >
                                <TextControl
                                    label="Hero Title"
                                    value={title}
                                    onChange={(val) => setAttributes({ title: val })}
                                    placeholder="Enter title"
                                    tagName="h2"
                                />

                                <TextControl
                                    label="Hero Description"
                                    value={description}
                                    onChange={(val) => setAttributes({ description: val })}
                                    placeholder="Enter description"
                                    tagName="p"
                                    multiline={true}
                                    description="Enter a description for the hero section."
                                />

                                <ButtonEditor
                                    buttonText={buttonText}
                                    buttonLink={buttonLink}
                                    onChangeText={(val) => setAttributes({ buttonText: val })}
                                    onChangeLink={(val) => setAttributes({ buttonLink: val })}
                                    label="Hero Button"
                                />

                                {buttonText && (
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">Button Caption</label>
                                        <RichText
                                            tagName="p"
                                            placeholder="Enter button caption"
                                            value={buttonCaption}
                                            onChange={(val) => setAttributes({ buttonCaption: val })}
                                            className="p-3 bg-white rounded-md focus:border-primary-70 focus:ring-1 focus:ring-primary-70 outline-none text-base"
                                            style={{ border: '1px solid #E2E8F0' }}
                                        />
                                        <p className="mt-2 text-xs text-neutral-60">Enter a caption to display next to the button.</p>
                                    </div>
                                )}
                            </PanelBody>
                        </Panel>
                    </div>

                    {heroType === 'small' && (
                        <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                            <Panel className="border-0 shadow-none">
                                <PanelBody
                                    title="Small Hero Settings"
                                    initialOpen={true}
                                >
                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">Tagline</label>
                                        <RichText
                                            tagName="p"
                                            placeholder="Enter tagline"
                                            value={tagline}
                                            onChange={(val) => setAttributes({ tagline: val })}
                                            className="p-3 bg-white rounded-md focus:border-primary-70 focus:ring-1 focus:ring-primary-70 outline-none text-base" style={{ border: '1px solid #E2E8F0' }}
                                        />
                                        <p className="mt-2 text-xs text-neutral-60">Enter a short tagline to display above the title.</p>
                                    </div>

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">Background Color</label>
                                        <SelectControl
                                            value={backgroundColor}
                                            options={[
                                                { label: 'Blue', value: 'blue' },
                                                { label: 'Green', value: 'green' },
                                                { label: 'Peach', value: 'peach' },
                                                { label: 'Pink', value: 'pink' },
                                            ]}
                                            onChange={(val) => setAttributes({ backgroundColor: val.toLowerCase() })}
                                            className="mb-2"
                                        />
                                        <p className="text-xs text-neutral-60 mb-2">Select the background color for the hero section.</p>
                                    </div>

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">Image Placement</label>
                                        <SelectControl
                                            value={imagePlacement}
                                            options={[
                                                { label: 'Normal', value: 'normal' },
                                                { label: 'Edge', value: 'edge' },
                                            ]}
                                            onChange={(val) => setAttributes({ imagePlacement: val })}
                                            className="mb-2"
                                        />
                                        <p className="text-xs text-neutral-60 mb-2">Choose how the image should be positioned in the hero section.</p>
                                    </div>

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">Hero Image</label>
                                        <div className="relative group">
                                            <MediaUpload
                                                onSelect={(media) => setAttributes({ smallImage: media.url })}
                                                allowedTypes={['image']}
                                                value={smallImage}
                                                render={({ open }) => (
                                                    <div
                                                        onClick={open}
                                                        className={`flex items-center justify-center w-full h-48 rounded-lg overflow-hidden cursor-pointer ${smallImage ? 'bg-neutral-10' : 'bg-neutral-20 border-2 border-dashed border-neutral-50'}`}
                                                    >
                                                        {smallImage ? (
                                                            <>
                                                                <img
                                                                    className="w-full h-full object-cover"
                                                                    src={smallImage}
                                                                    alt="Hero Image"
                                                                />
                                                                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                                                    <span className="text-white text-sm font-medium px-3 py-1.5 bg-black bg-opacity-70 rounded">Change Image</span>
                                                                </div>
                                                            </>
                                                        ) : (
                                                            <div className="text-center">
                                                                <div className="text-neutral-60 text-3xl mb-2">+</div>
                                                                <span className="text-neutral-60 text-sm">Upload Image</span>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            />
                                            {smallImage && (
                                                <Button
                                                    isDestructive
                                                    className="absolute top-2 right-2 w-8 h-8 rounded-full flex items-center justify-center text-sm bg-red-500 text-white shadow-md"
                                                    onClick={() => setAttributes({ smallImage: '' })}
                                                    icon="no-alt"
                                                />
                                            )}
                                        </div>
                                        <p className="mt-2 text-xs text-neutral-60">Upload an image for the small hero section.</p>
                                    </div>
                                </PanelBody>
                            </Panel>
                        </div>
                    )}

                    {heroType === 'default' && (
                        <div>
                            <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                                <Panel className="border-0 shadow-none">
                                    <PanelBody
                                        title="Top Images"
                                        initialOpen={true}
                                    >
                                        <p className="text-sm text-neutral-60 mb-4">Add images to display at the top of the hero section.</p>

                                        <div className="mb-6">
                                            <h3 className="text-sm font-medium text-neutral-70 mb-3">Images</h3>
                                            <div className="flex flex-wrap gap-2 mb-3">
                                                {topImages.map((item, index) => (
                                                    <button
                                                        key={index}
                                                        className={`border-0 py-2 px-4 inline-flex items-center text-sm font-medium rounded-lg transition-all ${
                                                            index === activeTopImage
                                                                ? 'text-white bg-primary-80 shadow-sm'
                                                                : 'cursor-pointer text-neutral-70 hover:text-primary-80 bg-neutral-20 hover:bg-neutral-30'
                                                        }`}
                                                        onClick={() => setActiveTopImage(index)}
                                                    >
                                                        <span>{`Image ${index + 1}`}</span>
                                                    </button>
                                                ))}
                                                <button
                                                    className="border-0 bg-neutral-20 hover:bg-neutral-30 py-2 px-4 inline-flex items-center justify-center text-sm font-medium text-neutral-70 hover:text-primary-80 rounded-lg cursor-pointer transition-all w-10 h-10"
                                                    onClick={() =>
                                                        addRepeaterItem(topImages, 'topImages', { image: '' }, setActiveTopImage)
                                                    }
                                                    title="Add New Image"
                                                >
                                                    +
                                                </button>
                                            </div>
                                            {topImages.length > 1 && (
                                                <button
                                                    className="bg-transparent border-0 p-0 inline-flex items-center text-xs font-medium text-red-600 hover:text-red-700 rounded-md cursor-pointer transition-all"
                                                    onClick={() =>
                                                        removeRepeaterItem(topImages, 'topImages', activeTopImage, activeTopImage, setActiveTopImage)
                                                    }
                                                >
                                                    <span className="dashicons dashicons-trash text-xs mr-1" style={{ fontSize: '14px' }}></span>
                                                    Remove Current Image
                                                </button>
                                            )}
                                        </div>

                                        {topImages.length > 0 && (
                                            <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30">
                                                <div className="mb-0 pb-3 border-b border-neutral-20">
                                                    <h4 className="text-base font-medium mt-0 mb-3">Image {activeTopImage + 1} Details</h4>
                                                </div>

                                                <div className="mb-4 mt-4">
                                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Image</label>
                                                    <div className="relative group">
                                                        <MediaUpload
                                                            onSelect={(media) =>
                                                                handleRepeaterChange(topImages, activeTopImage, 'image', media.url, 'topImages')
                                                            }
                                                            allowedTypes={['image']}
                                                            value={topImages[activeTopImage].image}
                                                            render={({ open }) => (
                                                                <div
                                                                    onClick={open}
                                                                    className={`flex items-center justify-center w-full h-48 rounded-lg overflow-hidden cursor-pointer ${topImages[activeTopImage].image ? 'bg-neutral-10' : 'bg-neutral-20 border-2 border-dashed border-neutral-50'}`}
                                                                >
                                                                    {topImages[activeTopImage].image ? (
                                                                        <>
                                                                            <img
                                                                                className="w-full h-full object-cover"
                                                                                src={topImages[activeTopImage].image}
                                                                                alt="Top Image"
                                                                            />
                                                                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                                                                <span className="text-white text-sm font-medium px-3 py-1.5 bg-black bg-opacity-70 rounded">Change Image</span>
                                                                            </div>
                                                                        </>
                                                                    ) : (
                                                                        <div className="text-center">
                                                                            <div className="text-neutral-60 text-3xl mb-2">+</div>
                                                                            <span className="text-neutral-60 text-sm">Upload Image</span>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        />
                                                        {topImages[activeTopImage].image && (
                                                            <Button
                                                                isDestructive
                                                                className="absolute top-2 right-2 w-8 h-8 rounded-full flex items-center justify-center text-sm bg-red-500 text-white shadow-md"
                                                                onClick={() => handleRepeaterChange(topImages, activeTopImage, 'image', '', 'topImages')}
                                                                icon="no-alt"
                                                            />
                                                        )}
                                                    </div>
                                                    <p className="mt-2 text-xs text-neutral-60">Upload an image for the top section of the hero.</p>
                                                </div>

                                                {topImages.length > 1 && (
                                                    <div className="mt-6">
                                                        <Button
                                                            isDestructive
                                                            className="border border-red-300 bg-white hover:bg-red-50 text-red-600 hover:text-red-700 py-1.5 px-3 rounded-md transition-all text-sm flex items-center"
                                                            onClick={() =>
                                                                removeRepeaterItem(topImages, 'topImages', activeTopImage, activeTopImage, setActiveTopImage)
                                                            }
                                                            icon="trash"
                                                        >
                                                            Remove this image
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </PanelBody>
                                </Panel>
                            </div>

                            <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                                <Panel className="border-0 shadow-none">
                                    <PanelBody
                                        title="Logos"
                                        initialOpen={true}
                                    >
                                        <p className="text-sm text-neutral-60 mb-4">Add logos to display in the hero section.</p>

                                        <div className="mb-6">
                                            <h3 className="text-sm font-medium text-neutral-70 mb-3">Logos</h3>
                                            <div className="flex flex-wrap gap-2 mb-3">
                                                {logos.map((item, index) => (
                                                    <button
                                                        key={index}
                                                        className={`border-0 py-2 px-4 inline-flex items-center text-sm font-medium rounded-lg transition-all ${
                                                            index === activeLogo
                                                                ? 'text-white bg-primary-80 shadow-sm'
                                                                : 'cursor-pointer text-neutral-70 hover:text-primary-80 bg-neutral-20 hover:bg-neutral-30'
                                                        }`}
                                                        onClick={() => setActiveLogo(index)}
                                                    >
                                                        <span>{`Logo ${index + 1}`}</span>
                                                    </button>
                                                ))}
                                                <button
                                                    className="border-0 bg-neutral-20 hover:bg-neutral-30 py-2 px-4 inline-flex items-center justify-center text-sm font-medium text-neutral-70 hover:text-primary-80 rounded-lg cursor-pointer transition-all w-10 h-10"
                                                    onClick={() =>
                                                        addRepeaterItem(logos, 'logos', { image: '' }, setActiveLogo)
                                                    }
                                                    title="Add New Logo"
                                                >
                                                    +
                                                </button>
                                            </div>
                                            {logos.length > 1 && (
                                                <button
                                                    className="bg-transparent border-0 p-0 inline-flex items-center text-xs font-medium text-red-600 hover:text-red-700 rounded-md cursor-pointer transition-all"
                                                    onClick={() =>
                                                        removeRepeaterItem(logos, 'logos', activeLogo, activeLogo, setActiveLogo)
                                                    }
                                                >
                                                    <span className="dashicons dashicons-trash text-xs mr-1" style={{ fontSize: '14px' }}></span>
                                                    Remove Current Logo
                                                </button>
                                            )}
                                        </div>

                                        {logos.length > 0 && (
                                            <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30">
                                                <div className="mb-0 pb-3 border-b border-neutral-20">
                                                    <h4 className="text-base font-medium mt-0 mb-3">Logo {activeLogo + 1} Details</h4>
                                                </div>

                                                <div className="mb-4 mt-4">
                                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Logo Image</label>
                                                    <div className="relative group">
                                                        <MediaUpload
                                                            onSelect={(media) =>
                                                                handleRepeaterChange(logos, activeLogo, 'image', media.url, 'logos')
                                                            }
                                                            allowedTypes={['image']}
                                                            value={logos[activeLogo].image}
                                                            render={({ open }) => (
                                                                <div
                                                                    onClick={open}
                                                                    className={`flex items-center justify-center w-full h-32 rounded-lg overflow-hidden cursor-pointer ${logos[activeLogo].image ? 'bg-neutral-10' : 'bg-neutral-20 border-2 border-dashed border-neutral-50'}`}
                                                                >
                                                                    {logos[activeLogo].image ? (
                                                                        <>
                                                                            <img
                                                                                className="w-full h-full object-contain p-4"
                                                                                src={logos[activeLogo].image}
                                                                                alt="Logo Image"
                                                                            />
                                                                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                                                                <span className="text-white text-sm font-medium px-3 py-1.5 bg-black bg-opacity-70 rounded">Change Logo</span>
                                                                            </div>
                                                                        </>
                                                                    ) : (
                                                                        <div className="text-center">
                                                                            <div className="text-neutral-60 text-3xl mb-2">+</div>
                                                                            <span className="text-neutral-60 text-sm">Upload Logo</span>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        />
                                                        {logos[activeLogo].image && (
                                                            <Button
                                                                isDestructive
                                                                className="absolute top-2 right-2 w-8 h-8 rounded-full flex items-center justify-center text-sm bg-red-500 text-white shadow-md"
                                                                onClick={() => handleRepeaterChange(logos, activeLogo, 'image', '', 'logos')}
                                                                icon="no-alt"
                                                            />
                                                        )}
                                                    </div>
                                                    <p className="mt-2 text-xs text-neutral-60">Upload a logo to display in the hero section.</p>
                                                </div>

                                                {logos.length > 1 && (
                                                    <div className="mt-6">
                                                        <Button
                                                            isDestructive
                                                            className="border border-red-300 bg-white hover:bg-red-50 text-red-600 hover:text-red-700 py-1.5 px-3 rounded-md transition-all text-sm flex items-center"
                                                            onClick={() =>
                                                                removeRepeaterItem(logos, 'logos', activeLogo, activeLogo, setActiveLogo)
                                                            }
                                                            icon="trash"
                                                        >
                                                            Remove this logo
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </PanelBody>
                                </Panel>
                            </div>

                            <div className="mb-6 bg-neutral-20 rounded-xl p-6">
                                <Panel className="border-0 shadow-none">
                                    <PanelBody
                                        title="Slider Items"
                                        initialOpen={true}
                                    >
                                        <p className="text-sm text-neutral-60 mb-4">Add slides to display in the hero slider.</p>

                                        <div className="mb-6">
                                            <h3 className="text-sm font-medium text-neutral-70 mb-3">Slides</h3>
                                            <div className="flex flex-wrap gap-2 mb-3">
                                                {sliderItems.map((item, index) => (
                                                    <button
                                                        key={index}
                                                        className={`border-0 py-2 px-4 inline-flex items-center text-sm font-medium rounded-lg transition-all ${
                                                            index === activeSliderItem
                                                                ? 'text-white bg-primary-80 shadow-sm'
                                                                : 'cursor-pointer text-neutral-70 hover:text-primary-80 bg-neutral-20 hover:bg-neutral-30'
                                                        }`}
                                                        onClick={() => setActiveSliderItem(index)}
                                                    >
                                                        <span>{`Slide ${index + 1}`}</span>
                                                    </button>
                                                ))}
                                                <button
                                                    className="border-0 bg-neutral-20 hover:bg-neutral-30 py-2 px-4 inline-flex items-center justify-center text-sm font-medium text-neutral-70 hover:text-primary-80 rounded-lg cursor-pointer transition-all w-10 h-10"
                                                    onClick={() =>
                                                        addRepeaterItem(
                                                            sliderItems,
                                                            'sliderItems',
                                                            { background: '', description: '', logo: '', companyName: '' },
                                                            setActiveSliderItem
                                                        )
                                                    }
                                                    title="Add New Slide"
                                                >
                                                    +
                                                </button>
                                            </div>
                                            {sliderItems.length > 1 && (
                                                <button
                                                    className="bg-transparent border-0 p-0 inline-flex items-center text-xs font-medium text-red-600 hover:text-red-700 rounded-md cursor-pointer transition-all"
                                                    onClick={() =>
                                                        removeRepeaterItem(
                                                            sliderItems,
                                                            'sliderItems',
                                                            activeSliderItem,
                                                            activeSliderItem,
                                                            setActiveSliderItem
                                                        )
                                                    }
                                                >
                                                    <span className="dashicons dashicons-trash text-xs mr-1" style={{ fontSize: '14px' }}></span>
                                                    Remove Current Slide
                                                </button>
                                            )}
                                        </div>

                                        {sliderItems.length > 0 && (
                                            <div className="p-4 pt-0 bg-white rounded-lg border border-neutral-30">
                                                <div className="mb-0 pb-3 border-b border-neutral-20">
                                                    <h4 className="text-base font-medium mt-0 mb-3">Slide {activeSliderItem + 1} Details</h4>
                                                </div>

                                                <div className="mb-4 mt-4">
                                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Background Image</label>
                                                    <div className="relative group">
                                                        <MediaUpload
                                                            onSelect={(media) =>
                                                                handleRepeaterChange(
                                                                    sliderItems,
                                                                    activeSliderItem,
                                                                    'background',
                                                                    media.url,
                                                                    'sliderItems'
                                                                )
                                                            }
                                                            allowedTypes={['image']}
                                                            value={sliderItems[activeSliderItem].background}
                                                            render={({ open }) => (
                                                                <div
                                                                    onClick={open}
                                                                    className={`flex items-center justify-center w-full h-48 rounded-lg overflow-hidden cursor-pointer ${sliderItems[activeSliderItem].background ? 'bg-neutral-10' : 'bg-neutral-20 border-2 border-dashed border-neutral-50'}`}
                                                                >
                                                                    {sliderItems[activeSliderItem].background ? (
                                                                        <>
                                                                            <img
                                                                                className="w-full h-full object-cover"
                                                                                src={sliderItems[activeSliderItem].background}
                                                                                alt="Slide Background"
                                                                            />
                                                                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                                                                <span className="text-white text-sm font-medium px-3 py-1.5 bg-black bg-opacity-70 rounded">Change Background</span>
                                                                            </div>
                                                                        </>
                                                                    ) : (
                                                                        <div className="text-center">
                                                                            <div className="text-neutral-60 text-3xl mb-2">+</div>
                                                                            <span className="text-neutral-60 text-sm">Upload Background</span>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        />
                                                        {sliderItems[activeSliderItem].background && (
                                                            <Button
                                                                isDestructive
                                                                className="absolute top-2 right-2 w-8 h-8 rounded-full flex items-center justify-center text-sm bg-red-500 text-white shadow-md"
                                                                onClick={() => handleRepeaterChange(sliderItems, activeSliderItem, 'background', '', 'sliderItems')}
                                                                icon="no-alt"
                                                            />
                                                        )}
                                                    </div>
                                                    <p className="mt-2 text-xs text-neutral-60">Upload a background image for this slide.</p>
                                                </div>

                                                <div className="mb-4">
                                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Slide Description</label>
                                                    <RichText
                                                        tagName="p"
                                                        placeholder="Enter slide description"
                                                        value={sliderItems[activeSliderItem].description}
                                                        onChange={(val) =>
                                                            handleRepeaterChange(
                                                                sliderItems,
                                                                activeSliderItem,
                                                                'description',
                                                                val,
                                                                'sliderItems'
                                                            )
                                                        }
                                                        className="p-3 bg-white rounded-md focus:ring-1 focus:ring-primary-70 outline-none min-h-[100px] text-base" style={{ border: '1px solid #E2E8F0' }}
                                                    />
                                                    <p className="mt-2 text-xs text-neutral-60">Enter a description for this slide.</p>
                                                </div>

                                                <div className="mb-4">
                                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Company Logo</label>
                                                    <div className="relative group">
                                                        <MediaUpload
                                                            onSelect={(media) =>
                                                                handleRepeaterChange(
                                                                    sliderItems,
                                                                    activeSliderItem,
                                                                    'logo',
                                                                    media.url,
                                                                    'sliderItems'
                                                                )
                                                            }
                                                            allowedTypes={['image']}
                                                            value={sliderItems[activeSliderItem].logo}
                                                            render={({ open }) => (
                                                                <div
                                                                    onClick={open}
                                                                    className={`flex items-center justify-center w-full h-32 rounded-lg overflow-hidden cursor-pointer ${sliderItems[activeSliderItem].logo ? 'bg-neutral-10' : 'bg-neutral-20 border-2 border-dashed border-neutral-50'}`}
                                                                >
                                                                    {sliderItems[activeSliderItem].logo ? (
                                                                        <>
                                                                            <img
                                                                                className="w-full h-full object-contain p-4"
                                                                                src={sliderItems[activeSliderItem].logo}
                                                                                alt="Company Logo"
                                                                            />
                                                                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                                                                                <span className="text-white text-sm font-medium px-3 py-1.5 bg-black bg-opacity-70 rounded">Change Logo</span>
                                                                            </div>
                                                                        </>
                                                                    ) : (
                                                                        <div className="text-center">
                                                                            <div className="text-neutral-60 text-3xl mb-2">+</div>
                                                                            <span className="text-neutral-60 text-sm">Upload Logo</span>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        />
                                                        {sliderItems[activeSliderItem].logo && (
                                                            <Button
                                                                isDestructive
                                                                className="absolute top-2 right-2 w-8 h-8 rounded-full flex items-center justify-center text-sm bg-red-500 text-white shadow-md"
                                                                onClick={() => handleRepeaterChange(sliderItems, activeSliderItem, 'logo', '', 'sliderItems')}
                                                                icon="no-alt"
                                                            />
                                                        )}
                                                    </div>
                                                    <p className="mt-2 text-xs text-neutral-60">Upload a company logo for this slide.</p>
                                                </div>

                                                <div className="mb-4">
                                                    <label className="block text-sm font-medium text-neutral-70 mb-2">Company Name</label>
                                                    <RichText
                                                        tagName="p"
                                                        placeholder="Enter company name"
                                                        value={sliderItems[activeSliderItem].companyName}
                                                        onChange={(val) =>
                                                            handleRepeaterChange(
                                                                sliderItems,
                                                                activeSliderItem,
                                                                'companyName',
                                                                val,
                                                                'sliderItems'
                                                            )
                                                        }
                                                        className="p-3 bg-white rounded-md focus:border-primary-70 focus:ring-1 focus:ring-primary-70 outline-none text-base" style={{ border: '1px solid #E2E8F0' }}
                                                    />
                                                    <p className="mt-2 text-xs text-neutral-60">Enter the company name for this slide.</p>
                                                </div>

                                                {sliderItems.length > 1 && (
                                                    <div className="mt-6">
                                                        <Button
                                                            isDestructive
                                                            className="border border-red-300 bg-white hover:bg-red-50 text-red-600 hover:text-red-700 py-1.5 px-3 rounded-md transition-all text-sm flex items-center"
                                                            onClick={() =>
                                                                removeRepeaterItem(
                                                                    sliderItems,
                                                                    'sliderItems',
                                                                    activeSliderItem,
                                                                    activeSliderItem,
                                                                    setActiveSliderItem
                                                                )
                                                            }
                                                            icon="trash"
                                                        >
                                                            Remove this slide
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </PanelBody>
                                </Panel>
                            </div>
                        </div>
                    )}
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" value={attributes.description} />
                    <RichText.Content tagName="span" value={attributes.buttonText} />
                    <RichText.Content tagName="span" value={attributes.buttonCaption} />
                    <RichText.Content tagName="p" value={attributes.tagline} />

                    {attributes.heroType === 'small' && attributes.smallImage && (
                        <img
                            src={attributes.smallImage}
                            alt="Small Hero Image"
                        />
                    )}

                    {attributes.heroType === 'default' &&
                        attributes.topImages?.map((item, index) => (
                            item.image && (
                                <img
                                    key={`hero-top-image-${index}`}
                                    src={item.image}
                                    alt={`Hero Top Image ${index + 1}`}
                                />
                            )
                        ))}

                    {attributes.heroType === 'default' &&
                        attributes.logos?.map((item, index) => (
                            item.image && (
                                <img
                                    key={`hero-logo-${index}`}
                                    src={item.image}
                                    alt={`Hero Logo ${index + 1}`}
                                />
                            )
                        ))}

                    {attributes.heroType === 'default' &&
                        attributes.sliderItems?.map((item, index) => (
                            <div key={`hero-slide-${index}`}>
                                {item.background && (
                                    <img
                                        src={item.background}
                                        alt={`Slide Background ${index + 1}`}
                                    />
                                )}
                                <RichText.Content tagName="p" value={item.description} />
                                {item.logo && (
                                    <img
                                        src={item.logo}
                                        alt={item.companyName ? `${item.companyName} Logo` : `Slide Logo ${index + 1}`}
                                    />
                                )}
                                <RichText.Content tagName="p" value={item.companyName} />
                            </div>
                        ))}
                </div>
                <div data-dynamic="sage/hero"></div>
            </div>
        );
    }
});