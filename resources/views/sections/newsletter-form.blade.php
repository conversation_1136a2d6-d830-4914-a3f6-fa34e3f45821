@php
    $title = $attributes['title'] ?? '';
    $description = $attributes['description'] ?? '';
    $placeholder = $attributes['placeholder'] ?? '<EMAIL>';
    $buttonText = $attributes['buttonText'] ?? 'Submit';
@endphp

<div class="newsletter-form-section relative flex justify-center items-center">
    <div class="container lg:max-w-[856px] lg:m-auto">
        <div class="w-full p-12 flex flex-col justify-between items-start relative overflow-hidden lg:rounded-3xl" style="background: linear-gradient(to bottom right, #daeffb, #acdbf2);">
            {{-- Noise texture overlay --}}
            <div class="absolute inset-0 z-0 opacity-20 bg-repeat" style="background-image: url('{{ asset('images/noise.svg') }}');"></div>

            <div class="relative z-10 w-full flex flex-col items-center">
                @if(!empty($title))
                    <x-title variation="medium">{!! $title !!}</x-title>
                @endif

                @if(!empty($description))
                    <p class="text-bodySmall text-[#3F4A5A] font-normal lg:text-bodySmallDesktop mb-8">{!! $description !!}</p>
                @endif

                <div class="relative">
                    <form action="#" method="POST" class="flex gap-1">
                        <input type="email" placeholder="{{ $placeholder }}" class="px-4 pt-2.5 py-2 border border-[#CFD5E3] rounded-2xl min-w-72 appearance-none placeholder:text-[#656B74]">
                        <x-button
                            type="submit"
                            size="small"
                            variant="primary">
                            {{ $buttonText }}
                        </x-button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
