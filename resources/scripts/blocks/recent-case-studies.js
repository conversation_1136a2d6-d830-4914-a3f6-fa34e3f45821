import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { Fragment } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ButtonEditor,
    EditorContainer
} from './editor';

registerBlockType('sage/recent-case-studies', {
    apiVersion: 2,
    title: 'Recent Case Studies',
    icon: 'portfolio',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string'
        },
        description: {
            type: 'string'
        },
        buttonText: {
            type: 'string'
        },
        buttonLink: {
            type: 'string'
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, description, buttonText, buttonLink } = attributes;

        return (
            <Fragment>
                <EditorContainer blockTitle="Recent Case Studies" {...blockProps}>
                    <TextControl
                        label="Title"
                        value={title}
                        onChange={(value) => setAttributes({ title: value })}
                        placeholder="Enter title"
                        tagName="h2"
                    />

                    <TextControl
                        label="Description"
                        value={description}
                        onChange={(value) => setAttributes({ description: value })}
                        placeholder="Enter description"
                        tagName="p"
                        multiline={true}
                    />

                    <ButtonEditor
                        buttonText={buttonText}
                        buttonLink={buttonLink}
                        onChangeText={(value) => setAttributes({ buttonText: value })}
                        onChangeLink={(value) => setAttributes({ buttonLink: value })}
                        label="Button"
                    />
                </EditorContainer>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        const { title, description, buttonText, buttonLink } = attributes;

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <h2>{title}</h2>
                    <p>{description}</p>
                    {buttonText && buttonLink && (
                        <a href={buttonLink}>{buttonText}</a>
                    )}
                </div>
                <div data-dynamic="sage/recent-case-studies"></div>
            </div>
        );
    },
});
