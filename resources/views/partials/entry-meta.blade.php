@php
    $categories = get_the_category($post->ID);
    $author_name = get_the_author();
    $name_parts = explode(' ', $author_name);
    $initials = strtoupper(substr($name_parts[0], 0, 1) . (isset($name_parts[1]) ? substr($name_parts[1], 0, 1) : ''));
    $color_seed = md5($author_name);
    $bg_color = '#' . substr($color_seed, 0, 6);
@endphp

<div class="flex flex-wrap justify-between items-start flex-col-reverse lg:mt-11 lg:items-center lg:gap-y-4 lg:flex-row">
    <div class="flex items-center justify-between w-full lg:w-auto">
        <div class="flex items-center">
            <div class="w-10 h-10 rounded-full flex items-center justify-center text-white text-bodySmall mr-2 pt-0.5" style="background-color: {{ $bg_color }};">
                {{ $initials }}
            </div>
            <div class="flex flex-col pt-0.5 mr-1 lg:mr-0">
                <span class="text-[#222831] font-normal text-bodySmall lg:text-bodySmallDesktop">{!! $author_name !!}</span>
            </div>
        </div>
        <div class="flex items-center gap-1 lg:gap-0">
            <span class="text-[#222831] font-normal text-bodySmall pt-0.5 lg:hidden">•</span>
            <span class="text-[#222831] font-normal text-bodySmall pt-1 lg:text-bodyExtraSmall lg:hidden">{{ get_the_date() }}</span>
        </div>
    </div>

    <div class="post-tags flex flex-wrap gap-3 items-center mb-4 lg:mb-0">
        @foreach($categories as $category)
            <span class="tag block text-[#303945] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#FBFBFD] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                {{ $category->name }}
            </span>
        @endforeach
        <span class="text-[#222831] text-bodyExtraSmall block pt-0.5 hidden lg:block lg:text-bodyExtraSmallDesktop">•</span>
        <span class="text-neutral-100 text-bodyExtraSmall pt-0.5 hidden lg:block lg:text-bodyExtraSmallDesktop">{{ get_the_date() }}</span>
    </div>
</div>
