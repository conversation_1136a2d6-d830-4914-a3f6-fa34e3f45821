document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('.case-studies-collection');
    if (!container) return;

    const serviceFilter = container.querySelector('#service-filter');
    const loadButton = container.querySelector('#load');
    const grid = container.querySelector('.other-case-studies');
    if (!grid || !loadButton) return;

    const featuredOriginal = container.querySelector('#featured-original');
    let visibleCount = 6;
    const increment = 4;
    const duration = 300;

    function updateVisibleItems() {
        const filterValue = serviceFilter ? serviceFilter.value.trim() : '';

        if (filterValue !== '') {
            if (featuredOriginal) {
                featuredOriginal.style.display = 'none';
            }
            const featuredDuplicate = grid.querySelector('#featured-duplicate');
            if (featuredDuplicate) {
                const dupData = (featuredDuplicate.getAttribute('data-services') || '').trim();
                const dupArray = dupData.split(/\s+/);
                featuredDuplicate.style.display = dupArray.includes(filterValue) ? 'block' : 'none';
            }
        } else {
            if (featuredOriginal) featuredOriginal.style.display = 'flex';
            const featuredDuplicate = grid.querySelector('#featured-duplicate');
            if (featuredDuplicate) featuredDuplicate.style.display = 'none';
        }

        const allItems = Array.from(grid.querySelectorAll('.case-study-item'))
            .filter(item => item.id !== 'featured-duplicate');
        let filteredItems = allItems;
        if (filterValue !== '') {
            filteredItems = allItems.filter(item => {
                const dataServices = (item.getAttribute('data-services') || '').trim();
                return dataServices.split(/\s+/).includes(filterValue);
            });
        }
        allItems.forEach(item => item.style.display = 'none');
        filteredItems.slice(0, visibleCount).forEach(item => item.style.display = 'block');

        loadButton.style.display = (filteredItems.length > visibleCount) ? 'flex' : 'none';
    }

    function fadeOutElements(elements, callback) {
        elements.forEach(el => {
            if (getComputedStyle(el).display !== 'none') {
                el.style.transition = `opacity ${duration}ms ease, transform ${duration}ms ease`;
                el.style.opacity = 0;
                el.style.transform = 'translateY(20px)';
            }
        });
        setTimeout(callback, duration);
    }

    function fadeInElements(elements) {
        elements.forEach(el => {
            if (getComputedStyle(el).display !== 'none') {
                el.style.opacity = 0;
                el.style.transform = 'translateY(20px)';
                void el.offsetWidth;
                el.style.transition = `opacity ${duration}ms ease, transform ${duration}ms ease`;
                el.style.opacity = 1;
                el.style.transform = 'translateY(0)';
            }
        });
    }

    function updateVisibleItemsWithAnimation() {
        const currentVisible = [];
        if (featuredOriginal && getComputedStyle(featuredOriginal).display !== 'none') {
            currentVisible.push(featuredOriginal);
        }
        grid.querySelectorAll('.case-study-item').forEach(item => {
            if (getComputedStyle(item).display !== 'none') {
                currentVisible.push(item);
            }
        });

        fadeOutElements(currentVisible, function() {
            updateVisibleItems();
            const newVisible = [];
            if (featuredOriginal && getComputedStyle(featuredOriginal).display !== 'none') {
                newVisible.push(featuredOriginal);
            }
            grid.querySelectorAll('.case-study-item').forEach(item => {
                if (getComputedStyle(item).display !== 'none') {
                    newVisible.push(item);
                }
            });
            fadeInElements(newVisible);
        });
    }

    function updateNewItemsWithAnimation() {
        const oldVisible = new Set();
        if (featuredOriginal && getComputedStyle(featuredOriginal).display !== 'none') {
            oldVisible.add(featuredOriginal);
        }
        grid.querySelectorAll('.case-study-item').forEach(item => {
            if (getComputedStyle(item).display !== 'none') {
                oldVisible.add(item);
            }
        });

        updateVisibleItems();

        const newVisible = [];
        if (featuredOriginal && getComputedStyle(featuredOriginal).display !== 'none' && !oldVisible.has(featuredOriginal)) {
            newVisible.push(featuredOriginal);
        }
        grid.querySelectorAll('.case-study-item').forEach(item => {
            if (getComputedStyle(item).display !== 'none' && !oldVisible.has(item)) {
                newVisible.push(item);
            }
        });

        fadeInElements(newVisible);
    }

    if (serviceFilter) {
        serviceFilter.addEventListener('change', function() {
            visibleCount = 6;
            updateVisibleItemsWithAnimation();
        });
    }

    loadButton.addEventListener('click', function(e) {
        e.preventDefault();
        visibleCount += increment;
        updateNewItemsWithAnimation();
    });

    updateVisibleItems();
});
