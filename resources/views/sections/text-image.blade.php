<div class="text-image-section flex justify-center items-start relative bg-no-repeat bg-cover bg-[30%_50%] lg:bg-center lg:bg-contain" 
    style="{{ isset($attributes['blockBackground']) && $attributes['blockBackground'] ? "background-image:url('{$attributes['blockBackground']}');" : '' }}">

    <div class="container pt-10 px-5 lg:py-10">
        @if (!empty($attributes['items']))
            @foreach ($attributes['items'] as $index => $item)
                <div class="text-image-item mb-16 flex flex-col gap-12 {{ $index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse' }} lg:gap-16">
                <div class="content-container w-full md:w-1/2 flex flex-col justify-center {{ (isset($item['type']) && $item['type'] === 'advanced') ? 
                    ((isset($item['advancedFields']['background']) && $item['advancedFields']['background'] === 'light') 
                        ? 'bg-white' 
                        : 'bg-[#F4F6FA]') . ' p-8 rounded-3xl' 
                    : '' }}">

                        @if (!empty($item['title']))
                            @if ($item['type'] === 'advanced')
                                <x-title variation="medium" class="mb-3">{!! $item['title'] !!}</x-title>
                            @else
                                <x-title variation="large" class="mb-3 max-w-xs">{!! $item['title'] !!}</x-title>
                            @endif
                        @endif

                        @if (!empty($item['description']))
                            @if ($item['type'] === 'advanced')
                                <p class="text-bodySmall lg:text-bodySmallDesktop mb-8">{!! $item['description'] !!}</p>
                            @else
                                <p>{!! $item['description'] !!}</p>
                            @endif
                        @endif

                        @if ($item['type'] === 'advanced')
                            @if (!empty($item['advancedFields']['subRepeater']))
                                <ul class="flex gap-7 mb-6">
                                    @foreach ($item['advancedFields']['subRepeater'] as $subItem)
                                        <li class="flex flex-col items-start mb-2 pr-6 vertical relative after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:w-px after:h-20 lg:max-w-[27%]">
                                            @if (!empty($subItem['count']))
                                                <x-title variation="small">{!! $subItem['count'] !!}</x-title>
                                            @endif
                                            @if (!empty($subItem['label']))
                                                <span class="text-bodySmall lg:text-bodySmallDesktop">{{ $subItem['label'] }}</span>
                                            @endif
                                        </li>
                                    @endforeach
                                </ul>
                                <div class="flex justify-start">
                                    <x-button 
                                        type="link"
                                        variant="primary"
                                        icon="link"
                                        size="small"
                                        :href="$item['advancedFields']['buttonLink'] ?? '#'">
                                        {!! $item['advancedFields']['buttonText'] !!}
                                    </x-button>
                                </div>
                            @endif
                        @endif
                    </div>
                    @if ($item['type'] === 'advanced')
                        <div class="p-5 w-full flex flex-col justify-end h-[400px] relative lg:p-6 lg:w-1/2">
                            @if(!empty($item['image']))
                                <img class="rounded-3xl overflow-hidden object-cover w-full h-[400px] absolute inset-0 -z-20" src="{{ $item['image'] }}" />
                            @endif
                            <div class="relative z-10">
                                <p class="text-white mb-4">{!! $item['advancedFields']['description'] ?? '' !!}</p>
                                @if(!empty($item['advancedFields']['logo']) || !empty($item['advancedFields']['companyName']))
                                    <div class="flex items-center gap-2">
                                        @if(!empty($item['advancedFields']['logo']))
                                            <img src="{{ $item['advancedFields']['logo'] }}" alt="Company Logo" />
                                        @endif
                                        @if(!empty($item['advancedFields']['companyName']))
                                            <span class="text-white mt-1">{!! $item['advancedFields']['companyName'] !!}</span>
                                        @endif
                                    </div>
                                @endif
                            </div>
                            <div class="rounded-3xl overflow-hidden absolute w-full h-full left-0 bottom-0 pointer-events-none -z-10 bg-[linear-gradient(180deg,rgba(1,101,159,0)_0%,#042031_100%)] lg:h-3/5"></div>
                        </div>
                    @else
                        <div class="image-container w-full md:w-1/2 mb-6 md:mb-0 flex justify-center">
                            <img src="{{ $item['image'] }}" class="max-w-full max-h-80 m-auto lg:max-w-md">
                        </div>
                    @endif
                </div>
            @endforeach
        @endif
    </div>
</div>
