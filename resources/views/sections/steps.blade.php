@php
    $steps = $attributes['steps'] ?? [];
    $taglineColors = [
        '!text-[#E66953]', // Step 1
        '!text-[#33A5DC]', // Step 2
        '!text-[#E65975]', // Step 3
        '!text-[#6EB186]', // Step 4
    ];
@endphp

<div class="steps-section bg-[#F5FCFC] relative py-16 overflow-hidden lg:bg-transparent">
    @if(!empty($attributes['backgroundImage']))
        <div class="absolute inset-0 -z-10">
            <img src="{{ $attributes['backgroundImage'] }}" alt="Background" class="w-full h-full object-cover">
        </div>
    @else
        <div class="absolute inset-0 bg-[#F9F9FB] -z-10"></div>
    @endif

    <div class="container px-5">
        <div class="text-center mb-16">
            @if(!empty($attributes['title']))
                <x-title variation="large" class="mb-4">{!! $attributes['title'] !!}</x-title>
            @endif

            @if(!empty($attributes['description']))
                <p class="max-w-lg mx-auto">{!! $attributes['description'] !!}</p>
            @endif
        </div>

        <div class="steps-container max-w-[680px] m-auto">
            @foreach($steps as $index => $step)
                @php
                    $isEven = $index % 2 === 1;
                    $taglineColor = $taglineColors[$index % count($taglineColors)];
                    $isLast = $index === count($steps) - 1;
                    $position = $isEven ? 'right' : 'left';
                @endphp

                <div class="step-item-wrapper mb-6 relative lg:mb-0">
                    <div class="step-item bg-white rounded-xl p-4 {{ $isEven ? 'lg:ml-auto' : '' }} lg:w-[calc(50%_-_24px)] {{ $index > 0 && !$isEven ? 'lg:mt-[-148px]' : '' }} {{ $index > 0 && $isEven && $index < 3 ? 'lg:mt-[-77px]' : '' }} {{ $index === 3 ? 'lg:mt-[-104px]' : '' }}">
                        @if(!empty($step['tagline']))
                            <x-title variation="subtitle" class="uppercase {!! $taglineColor !!}">{!! $step['tagline'] !!}</x-title>
                        @endif

                        @if(!empty($step['title']))
                            <span class="block text-bodyLarge lg:text-bodyLargeDesktop font-medium mb-2">{!! $step['title'] !!}</span>
                        @endif

                        @if(!empty($step['description']))
                            <p class="text-bodySmall lg:text-bodySmallDesktop">{!! $step['description'] !!}</p>
                        @endif
                    </div>

                    @if(!$isLast)
                        @if($position === 'left')
                            @if($index === 0)
                                <div class="arrow-container hidden lg:block absolute" style="bottom: 77px; left: calc(50% - 18px);">
                                    <img src="{{ asset('images/left_box_arrow_1.svg') }}" alt="Arrow" class="w-full h-auto">
                                </div>
                            @else
                                <div class="arrow-container hidden lg:block absolute" style="bottom: 104px; left: calc(50% - 18px);">
                                    <img src="{{ asset('images/left_box_arrow_2.svg') }}" alt="Arrow" class="w-full h-auto">
                                </div>
                            @endif
                        @else
                            <div class="arrow-container hidden lg:block absolute" style="bottom: 148px; right: calc(50% - 18px);">
                                <img src="{{ asset('images/right_box_arrow_1.svg') }}" alt="Arrow" class="w-full h-auto">
                            </div>
                        @endif
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</div>
