import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor,
    EditorContainer
} from './editor';

registerBlockType('sage/big-banner', {
    apiVersion: 2,
    title: 'Big Banner',
    icon: 'format-image',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string',
            default: '',
        },
        description: {
            type: 'string',
            default: '',
        },
        mainImage: {
            type: 'string',
            default: '',
        },
        pills: {
            type: 'array',
            default: [],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, description, mainImage, pills } = attributes;
        const [activePill, setActivePill] = useState(0);

        // Add a new pill item
        const addPill = () => {
            const newPill = {
                image: '',
                text: '',
            };
            const newPills = [...pills, newPill];
            setAttributes({ pills: newPills });
            setActivePill(newPills.length - 1);
        };

        // Initialize with one pill if none exist
        if (pills.length === 0) {
            addPill();
        }

        // Handle pill item changes
        const handlePillChange = (index, field, value) => {
            const updatedPills = [...pills];
            updatedPills[index] = { ...updatedPills[index], [field]: value };
            setAttributes({ pills: updatedPills });
        };

        // Remove a pill item
        const removePill = (index) => {
            const updatedPills = [...pills];
            updatedPills.splice(index, 1);
            setAttributes({ pills: updatedPills });
            setActivePill(Math.min(index, updatedPills.length - 1));
        };

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Big Banner">
                    <div className="flex flex-col lg:flex-row gap-6 mb-6">
                        <div className="lg:w-1/2">
                            <TextControl
                                label="Title"
                                value={title}
                                onChange={(value) => setAttributes({ title: value })}
                                placeholder="Enter title"
                                tagName="h2"
                            />
                        </div>
                        <div className="lg:w-1/2">
                            <TextControl
                                label="Description"
                                value={description}
                                onChange={(value) => setAttributes({ description: value })}
                                placeholder="Enter description"
                                tagName="p"
                                multiline={true}
                            />
                        </div>
                    </div>

                    <ImageUploader
                        image={mainImage}
                        onSelect={(url) => setAttributes({ mainImage: url })}
                        onRemove={() => setAttributes({ mainImage: '' })}
                        label="Main Image"
                        description="Upload the main banner image."
                        height={64}
                        objectFit="cover"
                        altText="Banner Image"
                    />

                    <div className="mt-6">
                        <TabNavigation
                            items={pills}
                            activeTab={activePill}
                            onTabChange={setActivePill}
                            onAddItem={addPill}
                            onRemoveItem={() => removePill(activePill)}
                            getItemTitle={(item, index) => item.text || `Pill ${index + 1}`}
                            itemName="Pill"
                            addButtonTitle="Add New Pill"
                        />

                        {pills.length > 0 && (
                            <ItemEditor
                                itemIndex={activePill}
                                onRemove={() => removePill(activePill)}
                                itemName="Pill"
                                showRemoveButton={pills.length > 1}
                            >
                                <ImageUploader
                                    image={pills[activePill].image}
                                    onSelect={(url) => handlePillChange(activePill, 'image', url)}
                                    onRemove={() => handlePillChange(activePill, 'image', '')}
                                    label="Pill Icon"
                                    description="Upload a small icon (20x20px recommended)."
                                    objectFit="contain"
                                    altText="Pill Icon"
                                />

                                <TextControl
                                    label="Pill Text"
                                    value={pills[activePill].text}
                                    onChange={(value) => handlePillChange(activePill, 'text', value)}
                                    placeholder="Enter pill text"
                                    tagName="p"
                                />
                            </ItemEditor>
                        )}
                    </div>
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
