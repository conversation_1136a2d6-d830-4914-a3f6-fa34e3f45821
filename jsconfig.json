{
  "extends": [
    "@roots/bud/config/jsconfig.json",
    "@roots/sage/config/jsconfig.json"
  ],
  "compilerOptions": {
    "baseUrl": "resources",
    /**
     * Resolve aliases
     */
    "paths": {
      "@fonts/*": ["fonts/*"],
      "@images/*": ["images/*"],
      "@scripts/*": ["scripts/*"],
      "@styles/*": ["styles/*"]
    },
    /**
     * Type definitions
     */
    "types": [
      "@roots/bud",
      "@roots/bud-react",
      "@roots/bud-postcss",
      "@roots/bud-preset-recommend",
      "@roots/bud-preset-wordpress",
      "@roots/bud-tailwindcss",
      "@roots/bud-wordpress-theme-json",
      "@roots/sage"
    ]
  },
  "files": ["bud.config.js"],
  "include": ["resources"],
  "exclude": ["node_modules", "public"]
}
