<div class="counter-section flex items-center justify-center">
    <div class="container px-5 py-8 flex flex-col lg:flex-row lg:py-16">
        <div class="flex flex-col w-60 animation-delay-300" data-animate="fade-in">
            <div class="flex">
                <h4 class="text-8xl text-primary-80 font-bold inline">{{ $attributes['endNumber'] }}</h4>
                <h4 class="text-8xl text-primary-80 font-bold inline">{{ $attributes['unit'] }}</h4>
            </div>
            <h5 class="font-semibold text-sm uppercase text-neutral-80 mb-2">{{ $attributes['numberLabel'] }}</h5>
        </div>
        <x-title level="h2" class="mb-0 !leading-[67px] -mt-[3px] text-[64px] animation-delay-600" data-animate="fade-in" variation="large">{!! $attributes['title'] !!}</x-title>
    </div>
</div>
