<div class="contact-form-section relative flex justify-center items-center">
    <div class="container px-5 py-16 lg:py-32">
        @if(($attributes['formType'] ?? 'slider') === 'slider')
            <div class="mb-12 max-w-screen-md lg:mb-16 lg:pr-8">
                <x-title variation="large" class="mb-2">{!! $attributes['mainTitle'] !!}</x-title>
                <p class="text-bodyLarge lg:text-bodyLargeDesktop">{{ $attributes['mainSubtitle'] }}</p>
            </div>
            <div class="flex flex-col-reverse items-stretch justify-between lg:flex-row">
                <div class="w-full relative bg-primary-10 rounded-3xl overflow-hidden lg:w-[584px]">
                    @if($attributes['items'])
                        <div class="swiper-container overflow-hidden">
                            <div class="swiper-wrapper">
                                @foreach ($attributes['items'] as $item)
                                    <div class="swiper-slide text-left w-full h-full pb-[76px]">
                                        <div class="flex justify-center items-center h-[410px] fade-bottom">
                                            <img class="w-full h-full object-cover cutout-fade" src="{{ $item['image'] }}" />
                                        </div>
                                        <div class="mb-1">
                                            <x-title variation="tagline" class="px-8 mb-2 font-semibold !text-[#566276]">{!! $item['tagline'] !!}</x-title>
                                            <x-title variation="medium" class="px-8 mb-2 !text-[#222831]">{!! $item['title'] !!}</x-title>
                                            <p class="px-8 text-[#3F4A5A]">{!! $item['description'] !!}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="swiper-pagination"></div>
                        </div>
                    @endif
                </div>
                <div class="mb-6 bg-white min-h-full lg:w-[416px] lg:mb-0 relative transition-opacity duration-300" data-success-img="{{ asset('images/success.svg') }}" data-error-img="{{ asset('images/failure.svg') }}">
                    <form id="contact-form" class="h-full flex flex-col items-start justify-center transition-opacity duration-300">
                        <div class="w-full mb-4">
                            <label class="text-bodyMedium lg:text-bodyMediumDesktop text-[#566276] mb-0.5">Full Name</label>
                            <input type="text" name="name" placeholder="Enter your name..." class="form-input w-full placeholder-[#6E7B91] outline-none bg-[#FBFBFDD9] border border-[#CFD5E3] px-5 pt-2 pb-1.5 lg:px-6 lg:pt-3 lg:pb-2 rounded-2xl">
                        </div>
                        <div class="w-full mb-4">
                            <label class="text-bodyMedium lg:text-bodyMediumDesktop text-[#566276] mb-0.5">Email</label>
                            <input type="email" name="email" placeholder="Enter your email address..." class="form-input w-full placeholder-[#6E7B91] outline-none bg-[#FBFBFDD9] border border-[#CFD5E3] px-5 pt-2 pb-1.5 lg:px-6 lg:pt-3 lg:pb-2 rounded-2xl">
                        </div>
                        <div class="w-full mb-2 flex flex-col">
                            <label class="text-bodyMedium lg:text-bodyMediumDesktop text-[#566276] mb-0.5">Message</label>
                            <textarea name="message" rows="4" maxlength="300" placeholder="Let us know your thoughts..." class="form-textarea w-full placeholder-[#6E7B91] outline-none bg-[#FBFBFDD9] border border-[#CFD5E3] px-5 pt-2 pb-1.5 lg:px-6 lg:pt-3 lg:pb-2 rounded-2xl"></textarea>
                        </div>
                        <div class="w-full mb-10">
                            <p class="mb-2">Services you're interested in <span class="text-bodySmall lg:text-bodySmallDesktop">(Optional)</span></p>
                            <div class="categories flex flex-wrap gap-2 mt-2">
                                @foreach ($attributes['categories'] as $category)
                                    <button class="pill flex items-center pt-1 pb-0.5 pl-3 pr-3 rounded-full border border-[#CFD5E3] text-bodySmall transition-all overflow-hidden lg:text-bodySmallDesktop" data-category="{{ $category }}">
                                        <span class="pill-text text-[#303945]">{{ $category }}</span>
                                        <span class="close-icon text-[#00567F] ml-1 w-0 opacity-0 transition-all duration-300 overflow-hidden">&times;</span>
                                    </button>
                                @endforeach
                            </div>
                        </div>
                        <input type="hidden" name="submissionEmail" value="{{ $attributes['submissionEmail'] ?? get_option('contact_form_email') }}" />
                        <x-button type="submit" variant="primary" icon="loading" class="w-fit">Schedule a Call</x-button>
                    </form>
                    <div id="form-overlay" class="absolute w-full p-6 z-10 h-full flex flex-col items-center justify-center top-0 right-0 hidden transition-opacity duration-300">
                        <img id="overlay-img" class="mb-6" src="{{ asset('images/success.svg') }}" />
                        <x-title id="overlay-title" class="mb-2 text-center" variation="small">Thank you for reaching out to us!</x-title>
                        <p id="overlay-subtitle" class="mb-6 text-center">
                            We’ll get back to you soon. This window will close automatically in <span id="countdown">5 seconds</span>.
                        </p>
                        <x-button id="overlay-close" type="button" variant="secondary" size="small">Close</x-button>
                    </div>
                </div>
            </div>
        @elseif(($attributes['formType'] ?? 'slider') === 'static')
            <div class="flex bg-[#F0F9FC] rounded-3xl">
                <div class="lg:w-1/2 lg:p-12 relative transition-opacity duration-300" data-success-img="{{ asset('images/success.svg') }}" data-error-img="{{ asset('images/failure.svg') }}">
                    <x-title id="static-title" variation="medium" class="mb-4 transition-opacity duration-300">{!! $attributes['mainTitle'] !!}</x-title>
                    <form id="contact-form" class="flex flex-col items-start justify-center transition-opacity duration-300">
                        <div class="flex flex-col lg:flex-row lg:gap-4">
                            <div class="w-full mb-4">
                                <label class="text-bodyMedium lg:text-bodyMediumDesktop text-[#566276] mb-0.5">Full Name</label>
                                <input type="text" name="name" placeholder="Enter your name..." class="form-input w-full placeholder-[#6E7B91] outline-none bg-[#FBFBFDD9] border border-[#CFD5E3] px-5 pt-2 pb-1.5 lg:px-6 lg:pt-3 lg:pb-2 rounded-2xl">
                            </div>
                            <div class="w-full mb-4">
                                <label class="text-bodyMedium lg:text-bodyMediumDesktop text-[#566276] mb-0.5">Email</label>
                                <input type="email" name="email" placeholder="Your email address..." class="form-input w-full placeholder-[#6E7B91] outline-none bg-[#FBFBFDD9] border border-[#CFD5E3] px-5 pt-2 pb-1.5 lg:px-6 lg:pt-3 lg:pb-2 rounded-2xl">
                            </div>
                        </div>
                        <div class="w-full mb-2 flex flex-col">
                            <label class="text-bodyMedium lg:text-bodyMediumDesktop text-[#566276] mb-0.5">Message</label>
                            <textarea name="message" rows="4" maxlength="300" placeholder="Let us know your thoughts..." class="form-textarea w-full placeholder-[#6E7B91] outline-none bg-[#FBFBFDD9] border border-[#CFD5E3] px-5 pt-2 pb-1.5 lg:px-6 lg:pt-3 lg:pb-2 rounded-2xl"></textarea>
                        </div>
                        <div class="w-full mb-10">
                            <p class="mb-2">Services you're interested in <span class="text-bodySmall lg:text-bodySmallDesktop">(Optional)</span></p>
                            <div class="categories flex flex-wrap gap-2 mt-2">
                                @foreach ($attributes['categories'] as $category)
                                    <button class="pill flex items-center pt-1 pb-0.5 pl-3 pr-3 bg-[#F8FAFD] rounded-full border border-[#CFD5E3] text-bodySmall transition-all overflow-hidden lg:text-bodySmallDesktop" data-category="{{ $category }}">
                                        <span class="pill-text text-[#303945]">{{ $category }}</span>
                                        <span class="close-icon text-[#00567F] ml-1 w-0 opacity-0 transition-all duration-300 overflow-hidden">&times;</span>
                                    </button>
                                @endforeach
                            </div>
                        </div>
                        <input type="hidden" name="submissionEmail" value="{{ $attributes['submissionEmail'] ?? get_option('contact_form_email') }}" />
                        <x-button type="submit" variant="primary" icon="loading" class="w-fit">Schedule a Call</x-button>
                    </form>
                    <div id="form-overlay" class="absolute w-full p-6 z-10 h-full flex flex-col items-center justify-center top-0 left-0 hidden transition-opacity duration-300">
                        <img id="overlay-img" class="mb-6" src="{{ asset('images/success.svg') }}" />
                        <x-title id="overlay-title" class="mb-2 text-center" variation="small">Thank you for reaching out to us!</x-title>
                        <p id="overlay-subtitle" class="mb-6 text-center">
                            We’ll get back to you soon. This window will close automatically in <span id="countdown">5 seconds</span>.
                        </p>
                        <x-button id="overlay-close" type="button" variant="secondary" size="small">Close</x-button>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <img class="h-full object-cover cutout-fade" src="{!! $attributes['items'][0]['image'] !!}" style="mask-size:785px;mask-position:0 50%;" />
                </div>
            </div>
        @endif
    </div>
</div>
