@php
    $items = $attributes['items'] ?? [];
    $totalItems = count($items);
    $midPoint = (int)ceil($totalItems / 2);
    $topItems = array_slice($items, 0, $midPoint);
    $bottomItems = array_slice($items, $midPoint);
    $largeItems = $attributes['largeItems'] ?? [];
    $smallItems = $attributes['smallItems'] ?? [];
    $slideCount = count($largeItems);
@endphp

@if(($attributes['variant'] ?? 'default') === 'default')
    <div class="relative flex flex-col items-center justify-center w-full overflow-hidden">
        @if(!empty($attributes['title']) || !empty($attributes['description']) || !empty($attributes['buttonText']))
            <div class="container flex flex-col items-start justify-between px-5 py-8 lg:py-16 lg:flex-row">
                <div class="mb-4 lg:w-3/5 lg:pr-6 lg:mb-0">
                    @if(!empty($attributes['title']))
                        <x-title class="mb-2" variation="large">{!! $attributes['title'] !!}</x-title>
                    @endif
                    @if(!empty($attributes['description']))
                        <p class="text-bodyLarge lg:text-bodyLargeDesktop">{!! $attributes['description'] !!}</p>
                    @endif
                </div>
                @if(!empty($attributes['buttonText']))
                    <div>
                        <x-button 
                            type="link"
                            variant="secondary"
                            icon="link"
                            :href="$attributes['buttonLink'] ?? '#'">
                            {!! $attributes['buttonText'] !!}
                        </x-button>
                    </div>
                @endif
            </div>
        @endif

        <div class="pb-16 flex flex-col justify-between items-center {{ empty($attributes['title']) && empty($attributes['description']) && empty($attributes['buttonText']) ? 'pt-16' : '' }}">
            <div class="carousel-wrapper flex flex-col relative w-full overflow-hidden gap-y-6">
                <div class="carousel-top relative w-full overflow-hidden flex gap-x-6">
                    <div class="carousel-top-slider flex whitespace-nowrap gap-x-6">
                        @foreach($topItems as $index => $item)
                            <div class="carousel-slide flex-shrink-0 p-3 rounded-xl bg-neutral-10 border border-neutral-20 flex items-center lg:min-w-[292px] box-border">
                                <img src="{{ $item['image'] }}" alt="Item {{ $index + 1 }}" class="w-14 h-14 bg-white rounded-lg p-3 object-contain max-h-16">
                                <span class="carousel-item-title mt-1.5 text-2xl ml-3">{{ $item['title'] ?? '' }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="carousel-bottom relative w-full overflow-hidden flex gap-x-6">
                    <div class="carousel-bottom-slider flex whitespace-nowrap gap-x-6">
                        @foreach($bottomItems as $index => $item)
                            <div class="carousel-slide flex-shrink-0 p-3 rounded-xl bg-neutral-10 border border-neutral-20 flex items-center lg:min-w-[292px] box-border">
                                <img src="{{ $item['image'] }}" alt="Item {{ $index + 1 }}" class="w-14 h-14 bg-white rounded-lg p-3 object-contain max-h-16">
                                <span class="carousel-item-title mt-1.5 text-2xl ml-3">{{ $item['title'] ?? '' }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@elseif(($attributes['variant'] ?? 'default') === 'large')
    <div class="carousel-large-wrapper relative lg:h-[calc(100vh*var(--num-slides))]" style="--num-slides: {{ $slideCount }}">
        <div class="carousel-large-section overflow-hidden flex flex-col justify-center items-center lg:sticky lg:top-0 lg:h-screen">
            <div class="container flex flex-col items-start justify-between px-5 py-8 lg:py-16 lg:flex-row">
                <div class="mb-4 lg:w-8/12 lg:mb-0">
                    @if(!empty($attributes['title']))
                        <x-title class="mb-2" variation="large">{!! $attributes['title'] !!}</x-title>
                    @endif
                    @if(!empty($attributes['description']))
                        <p class="text-bodyLarge lg:text-bodyLargeDesktop">{!! $attributes['description'] !!}</p>
                    @endif
                </div>
            </div>
            <div class="container px-5 pb-8 lg:pr-0 lg:pl-5 lg:pb-16">
                <div class="swiper overflow-visible">
                    <div class="swiper-wrapper">
                        @foreach($largeItems as $item)
                            <div class="swiper-slide relative bg-[#F4F6FA] flex flex-col justify-between w-full h-auto overflow-hidden rounded-max">
                                <div class="flex flex-col justify-center lg:p-12 lg:w-3/5 lg:h-full">
                                    @if(!empty($item['tagline']))
                                        <x-title variation="tagline" class="p-5 uppercase mb-auto lg:p-0">
                                            {!! $item['tagline'] !!}
                                        </x-title>
                                    @endif
                                    <div class="px-5 flex flex-col mb-auto lg:mt-6 lg:p-0">
                                        @if(!empty($item['title']))
                                            <x-title variation="medium" class="mb-1">
                                                {!! $item['title'] !!}
                                            </x-title>
                                        @endif
                                        @if(!empty($item['subtitle']))
                                            <p class="mb-6">{!! $item['subtitle'] !!}</p>
                                        @endif
                                        @if(!empty($item['listItems']))
                                            <ul class="flex flex-col">
                                                @foreach($item['listItems'] as $listItem)
                                                    <li class="text-bodySmall flex gap-3 items-start lg:text-bodySmallDesktop mb-5">
                                                        <i class="p-1 text-bodyExtraSmall bg-[#CCE9F6] rounded-full fa-solid fa-check"></i>
                                                        <span class="pt-0.5">{!! $listItem !!}</span>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        @endif
                                    </div>
                                </div>
                                @if(!empty($item['image']))
                                    <div class="h-full lg:absolute lg:top-1/2 lg:-translate-y-1/2 lg:w-2/5 lg:right-0">
                                        <img class="w-full h-full object-cover cutout-fade rounded-3xl overflow-hidden"
                                             src="{{ $item['image'] }}" />
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@elseif(($attributes['variant'] ?? 'default') === 'small')
    <div class="flex flex-col items-center justify-center">
        <div class="w-full px-5 py-[62px] overflow-hidden lg:container">
            <div class="swiper-container small-carousel w-full">
                <div class="swiper-wrapper !ease-linear lg:justify-between">
                    @foreach($smallItems as $item)
                        @if(!empty($item['image']))
                            <div class="swiper-slide h-12 flex items-center justify-center lg:max-w-fit">
                                <img src="{{ $item['image'] }}" alt="Small Item" class="w-full h-full max-w-24 object-contain rounded-lg">
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@endif
