@php
    $models = $attributes['models'] ?? [];
    $backgrounds = [
        'images/model_bg_blue.svg',
        'images/model_bg_green.svg',
        'images/model_bg_peach.svg'
    ];
    $bgColors = [
        '#EDF2F9',
        '#EEF9ED',
        '#F9F3ED'
    ];
    $taglineColors = [
        'text-[#008FD3]',
        'text-[#6EB186]',
        'text-[#C48D52]'
    ];
@endphp

<div class="partnership-models-section js-partnership-models">
    <div class="max-w-[1072px] mx-auto py-[62px] px-5">
        <div class="flex flex-col gap-16">
            @foreach($models as $index => $model)
                @php
                    $bgIndex = $index % count($backgrounds);
                    $bgImage = $backgrounds[$bgIndex];
                    $bgColor = $bgColors[$bgIndex];
                    $taglineColor = $taglineColors[$bgIndex];
                @endphp
                
                <div class="partnership-model rounded-3xl bg-cover bg-no-repeat bg-right-top" style="background-image: url('{{ asset($bgImage) }}'); background-color: {{ $bgColor }};">
                    <div class="px-6 py-16 lg:px-[88px] lg:py-[124px]">
                        <div class="text-center mb-8">
                            @if(!empty($model['tagline']))
                                <x-subtitle class="{{ $taglineColor }}" variation="tagline">{!! $model['tagline'] !!}</x-subtitle>
                            @endif
                            
                            @if(!empty($model['title']))
                                <x-title variation="large">{!! $model['title'] !!}</x-title>
                            @endif
                        </div>
                        
                        @if(!empty($model['description']))
                            <p class="mb-8 max-w-4xl">
                                {!! $model['description'] !!}
                            </p>
                        @endif
                        
                        @if(!empty($model['benefits']))
                            <div class="benefits-section">
                                <h3 class="text-bodyMedium mb-4 lg:text-bodyMediumDesktop">Why choose this model:</h3>
                                <div class="benefits-swiper-container relative overflow-hidden" style="mask:linear-gradient(to right,rgba(0,0,0,0) 1%,rgba(0,0,0,1) 10%,rgba(0,0,0,1) 90%,rgba(0,0,0,0) 99%)">
                                    <div class="swiper swiper-benefits-{{ $index }}">
                                        <div class="swiper-wrapper flex gap-6" style="transition-timing-function: linear; will-change: transform;">
                                            @foreach($model['benefits'] as $benefit)
                                                <div class="swiper-slide w-auto" style="flex-shrink: 0;">
                                                    <div class="benefit-card bg-white bg-opacity-80 backdrop-blur-sm rounded-2xl p-4 flex items-center h-12">
                                                        @if(!empty($benefit['image']))
                                                            <div class="benefit-icon w-8 h-8 flex-shrink-0 flex items-center justify-center mr-2">
                                                                <img src="{{ $benefit['image'] }}" alt="" class="w-5 h-5">
                                                            </div>
                                                        @endif
                                                        <span class="benefit-text text-bodySmall whitespace-nowrap overflow-visible pt-0.5 lg:text-bodySmallDesktop">
                                                            {!! $benefit['text'] !!}
                                                        </span>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</div>