import Swiper from 'swiper';
import { Autoplay, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';

document.addEventListener('DOMContentLoaded', function () {
    const mainElement = document.getElementById('main');
    const caseStudiesSlider = new Swiper('.case-studies-slider', {
        modules: [Autoplay, Pagination],
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.case-studies-slider .swiper-pagination',
            clickable: true,
        },
        slidesPerView: 1,
        spaceBetween: 24,
    });
    const menuIcon = document.querySelectorAll('.menu-icon');

    menuIcon.forEach(icon => {
        icon.addEventListener('click', () => {
            const lines = icon.querySelectorAll('.line');
            const menuContainer = document.querySelector('.menu-container');
            const isActive = icon.classList.toggle('is-active');
        
            if (isActive) {
                mainElement.style.filter = 'blur(5px)';

                lines[0].classList.add('translate-y-[8px]', 'rotate-45');
                lines[1].classList.add('opacity-0');
                lines[2].classList.add('-translate-y-[8px]', '-rotate-45');
            
                menuContainer.classList.remove('hidden');
                menuContainer.style.height = 'auto'; 

                const fullHeight = menuContainer.scrollHeight + 'px';
            
                menuContainer.style.height = '0';
                menuContainer.style.overflowY = 'hidden';
                menuContainer.offsetHeight;
                menuContainer.style.height = fullHeight;
            
                const openTransition = () => {
                    menuContainer.style.height = 'auto';
                    menuContainer.style.overflowY = 'scroll';
                    menuContainer.removeEventListener('transitionend', openTransition);
                };

                menuContainer.addEventListener('transitionend', openTransition);
            } else {
                mainElement.style.filter = 'none';

                lines[0].classList.remove('translate-y-[8px]', 'rotate-45');
                lines[1].classList.remove('opacity-0');
                lines[2].classList.remove('-translate-y-[8px]', '-rotate-45');
            
                menuContainer.style.overflowY = 'hidden';
                menuContainer.style.height = menuContainer.scrollHeight + 'px';
                menuContainer.offsetHeight;
                menuContainer.style.height = '0';
            
                const closeTransition = () => {
                    menuContainer.classList.add('hidden');
                    menuContainer.removeEventListener('transitionend', closeTransition);
                };

                menuContainer.addEventListener('transitionend', closeTransition);
            }
        });
    });

    const menuItemsWithSubmenu = document.querySelectorAll('.menu-item-has-children');

    if (mainElement) {
        menuItemsWithSubmenu.forEach(menuItem => {
            menuItem.addEventListener('mouseenter', () => {
                if (window.innerWidth >= 1024) {
                    mainElement.style.filter = 'blur(5px)';
                }
            });
        
            menuItem.addEventListener('mouseleave', () => {
                if (window.innerWidth >= 1024) {
                    mainElement.style.filter = 'none';
                }
            });
        });
    }
      
    document.querySelectorAll('.menu-expandable > a, .menu-expandable > svg').forEach(trigger => {
        trigger.addEventListener('click', e => {
            e.stopPropagation();
            e.preventDefault();

            if (window.innerWidth < 1024) {
                const menu = trigger.closest('.menu-expandable');
                const submenu = menu.querySelector('.submenu-container');
                const chevron = menu.querySelector('.fa-chevron-down');
                const isOpen = submenu.getAttribute('data-open') === 'true';

                if (!isOpen) {
                    submenu.setAttribute('data-open', 'true');
                    if (chevron) chevron.style.transform = 'rotate(180deg)';
                    submenu.style.maxHeight = '0vh';
                    requestAnimationFrame(() => {
                        caseStudiesSlider.update();
                        requestAnimationFrame(() => {
                            submenu.style.maxHeight = '200vh';
                        });
                    });
                } else {
                    if (chevron) chevron.style.transform = 'rotate(0deg)';
                    submenu.setAttribute('data-open', 'false');
                    submenu.style.maxHeight = '200vh';
                    requestAnimationFrame(() => {
                        submenu.style.maxHeight = '0vh';
                    });
                }
            }
        });
    });
});
