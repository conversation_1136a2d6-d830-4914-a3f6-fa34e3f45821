@php
    $variant = $attributes['variant'] ?? 'default';
    $items = $attributes['items'] ?? [];
    $count = count($items);
    $half = (int)ceil($count / 2);
    $splitItems = [
        'left' => array_slice($items, 0, $half),
        'right' => array_slice($items, $half),
    ];
    $gridClasses = [
        'left' => ['grid-item-blue', 'grid-item-pink'],
        'right' => ['grid-item-orange', 'grid-item-green'],
    ];
    $backgrounds = [
        'left' => ['blue_bg_small.svg', 'pink_bg_small.svg'],
        'right' => ['orange_bg_small.svg', 'green_bg_small.svg'],
    ];
    $simpleItems = $attributes['simpleItems'] ?? [];
@endphp

<div class="grid-section flex justify-center items-center">
    @if($variant === 'default')
        <div class="max-w-full py-16 lg:max-w-[904px] lg:px-5">
            <div class="mobile-swiper lg:hidden">
                <x-title variation="large" class="px-5 mb-10">{!! $attributes['title'] ?? '' !!}</x-title>
                <div class="swiper relative mb-8 px-5">
                    <div class="swiper-wrapper items-stretch"></div>
                </div>
                @if(!empty($attributes['buttonText']))
                    <div class="flex justify-start mx-5">
                        <x-button 
                            type="link"
                            variant="secondary"
                            icon="link"
                            :href="$attributes['buttonLink'] ?? '#'">
                            {!! $attributes['buttonText'] !!}
                        </x-button>
                    </div>
                @endif
            </div>

            <div class="desktop-grid hidden lg:block">
                <div class="grid grid-cols-1 mb-12 lg:grid-cols-2 lg:gap-6 all-items-container">
                    @foreach(['left', 'right'] as $side)
                        <div class="flex flex-col gap-6 {{ $side }}-items">
                            @if($side === 'right')
                                <x-title variation="large" class="hidden lg:block">{!! $attributes['title'] ?? '' !!}</x-title>
                                @if(!empty($attributes['buttonText']))
                                    <div class="flex justify-start -mt-2 mb-2 hidden lg:block">
                                        <x-button 
                                            type="link"
                                            variant="secondary"
                                            icon="link"
                                            :href="$attributes['buttonLink'] ?? '#'">
                                            {!! $attributes['buttonText'] !!}
                                        </x-button>
                                    </div>
                                @endif
                            @endif
                            @foreach($splitItems[$side] as $item)
                                @php
                                    $classIndex = ($loop->iteration - 1) % count($gridClasses[$side]);
                                    $gridClass = $gridClasses[$side][$classIndex];
                                    $background = $backgrounds[$side][$classIndex];
                                @endphp
                                <div class="swiper-slide w-[264px] h-auto p-6 pt-8 rounded-3xl bg-[#F4F6FA] bg-cover {{ $gridClass }} lg:h-fit lg:p-8 lg:pt-12 lg:w-fit" style="background-image:url('{{ asset('images/' . $background) }}')">
                                    <span class="flex items-center text-titleDesktop h-16 pt-[26px] font-normal block mb-4 lg:text-[102px]">{!! $item['title'] ?? '' !!}</span>
                                    <span class="subtitle relative flex flex-col text-bodySmall text-[#3F4A5A] font-light block lg:text-bodySmallDesktop after:w-full after:h-px after:my-8">{!! $item['subtitle'] ?? '' !!}</span>
                                    <x-title variation="medium" class="mb-4 max-w-72 pr-1.5">{!! $item['tagline'] ?? '' !!}</x-title>
                                    <p class="text-pretty">{!! $item['description'] ?? '' !!}</p>
                                </div>
                            @endforeach
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @elseif($variant === 'simple' && !empty($simpleItems))
        <div class="max-w-full py-8 w-full lg:max-w-[1072px] lg:pb-[62px] lg:px-5">
            @php
                $totalItems = count($simpleItems);
                $backgroundImages = [
                    'grid_bg_blue.svg',
                    'grid_bg_sand.svg',
                    'grid_bg_yellow.svg',
                    'grid_bg_purple.svg',
                    'grid_bg_pink.svg'
                ];
                $iconBackgrounds = [
                    '#C2DFE3',
                    '#E3D7C2',
                    '#DEE3C2',
                    '#D0C2E3',
                    '#E3C2CE'
                ];

                if ($totalItems === 1) {
                    $firstRowClass = 'lg:grid-cols-1';
                    $firstRowItems = 1;
                } elseif ($totalItems === 2) {
                    $firstRowClass = 'lg:grid-cols-2';
                    $firstRowItems = 2;
                } elseif ($totalItems === 3) {
                    $firstRowClass = 'lg:grid-cols-3';
                    $firstRowItems = 3;
                } elseif ($totalItems === 4) {
                    $firstRowClass = 'lg:grid-cols-2';
                    $firstRowItems = 2;
                } else {
                    $firstRowClass = 'lg:grid-cols-2';
                    $firstRowItems = 2;
                }

                $hasSecondRow = $totalItems > $firstRowItems;
                if ($hasSecondRow) {
                    $secondRowItems = min(3, $totalItems - $firstRowItems);
                    if ($secondRowItems === 1) {
                        $secondRowClass = 'lg:grid-cols-1';
                    } elseif ($secondRowItems === 2) {
                        $secondRowClass = 'lg:grid-cols-2';
                    } else {
                        $secondRowClass = 'lg:grid-cols-3';
                    }
                }

                $hasThirdRow = $totalItems > ($firstRowItems + ($hasSecondRow ? $secondRowItems : 0));
                if ($hasThirdRow) {
                    $thirdRowItems = min(3, $totalItems - $firstRowItems - $secondRowItems);
                    if ($thirdRowItems === 1) {
                        $thirdRowClass = 'lg:grid-cols-1';
                    } elseif ($thirdRowItems === 2) {
                        $thirdRowClass = 'lg:grid-cols-2';
                    } else {
                        $thirdRowClass = 'lg:grid-cols-3';
                    }
                }
            @endphp

            <div class="grid grid-cols-1 {{ $firstRowClass }} gap-6 px-5 mb-6 last:mb-0">
                @foreach($simpleItems as $index => $item)
                    @if($index < $firstRowItems)
                        @php
                            $bgIndex = $index % count($backgroundImages);
                            $iconBgColor = $iconBackgrounds[$bgIndex];
                            $bgImage = $backgroundImages[$bgIndex];
                        @endphp
                        <div class="border border-solid border-[#E8EBF3] rounded-3xl p-6 bg-cover" style="background-image: url('{{ asset('images/' . $bgImage) }}')">
                            <div class="flex flex-col">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center mb-4" style="background-color: {{ $iconBgColor }}">
                                    @if(!empty($item['image']))
                                        <img src="{{ $item['image'] }}" alt="" class="w-6 h-6">
                                    @endif
                                </div>
                                <span class="text-bodyLarge lg:text-bodyLargeDesktop mb-2">{!! $item['title'] ?? '' !!}</span>
                                <p class="text-bodyMedium lg:text-bodyMediumDesktop">{!! $item['description'] ?? '' !!}</p>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>

            @if($hasSecondRow)
                <div class="grid grid-cols-1 {{ $secondRowClass }} gap-6 px-5 mb-6 last:mb-0">
                    @foreach($simpleItems as $index => $item)
                        @if($index >= $firstRowItems && $index < ($firstRowItems + $secondRowItems))
                            @php
                                $bgIndex = $index % count($backgroundImages);
                                $iconBgColor = $iconBackgrounds[$bgIndex];
                                $bgImage = $backgroundImages[$bgIndex];
                            @endphp
                            <div class="border border-solid border-[#E8EBF3] rounded-3xl p-6 bg-cover" style="background-image: url('{{ asset('images/' . $bgImage) }}')">
                                <div class="flex flex-col">
                                    <div class="w-12 h-12 rounded-full flex items-center justify-center mb-4" style="background-color: {{ $iconBgColor }}">
                                        @if(!empty($item['image']))
                                            <img src="{{ $item['image'] }}" alt="" class="w-6 h-6">
                                        @endif
                                    </div>
                                    <span class="text-bodyLarge lg:text-bodyLargeDesktop mb-2">{!! $item['title'] ?? '' !!}</span>
                                    <p>{!! $item['description'] ?? '' !!}</p>
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>
            @endif

            @if($hasThirdRow)
                <div class="grid grid-cols-1 {{ $thirdRowClass }} gap-6 px-5">
                    @foreach($simpleItems as $index => $item)
                        @if($index >= ($firstRowItems + $secondRowItems))
                            @php
                                $bgIndex = $index % count($backgroundImages);
                                $iconBgColor = $iconBackgrounds[$bgIndex];
                                $bgImage = $backgroundImages[$bgIndex];
                            @endphp
                            <div class="border border-solid border-[#E8EBF3] rounded-3xl p-6 bg-cover" style="background-image: url('{{ asset('images/' . $bgImage) }}')">
                                <div class="flex flex-col">
                                    <div class="w-12 h-12 rounded-full flex items-center justify-center mb-4" style="background-color: {{ $iconBgColor }}">
                                        @if(!empty($item['image']))
                                            <img src="{{ $item['image'] }}" alt="" class="w-6 h-6">
                                        @endif
                                    </div>
                                    <span class="text-bodyLarge lg:text-bodyLargeDesktop mb-2">{!! $item['title'] ?? '' !!}</span>
                                    <p>{!! $item['description'] ?? '' !!}</p>
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>
            @endif
        </div>
    @endif
</div>