<svg width="1392" height="1004" viewBox="0 0 1392 1004" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1939_16785)">
<rect width="1392" height="1004" fill="#F4FAF8"/>
<g opacity="0.35" filter="url(#filter0_f_1939_16785)">
<circle cx="815.5" cy="745.5" r="396.5" fill="#6EB186"/>
</g>
<g opacity="0.5" filter="url(#filter1_f_1939_16785)">
<circle cx="537" cy="488" r="239" fill="#A6CE39"/>
</g>
</g>
<defs>
<filter id="filter0_f_1939_16785" x="-81" y="-151" width="1793" height="1793" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1939_16785"/>
</filter>
<filter id="filter1_f_1939_16785" x="-202" y="-251" width="1478" height="1478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1939_16785"/>
</filter>
<clipPath id="clip0_1939_16785">
<rect width="1392" height="1004" fill="white"/>
</clipPath>
</defs>
</svg>
