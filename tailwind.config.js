/** @type {import('tailwindcss').Config} */
import plugin from 'tailwindcss/plugin.js';

// MFS - Mobile font size (rem)
// MLH - Mobile line height (rem)
// MLS - Mobile letter spacing (em)
// DFS - Desktop font size (rem)
// DLH - Desktop line height (rem)
// DLS - Desktop letter spacing (em)
// ================================================================================================
//                              MFS     MLH     MLS                   DFS     DLH     DLS
// ================================================================================================
const fontSizes = {
    title:            { mobile: [3,     3.5,    '-0.01em'],  desktop: [4,     4,      '-0.01em'] },
    large:            { mobile: [2.25,  2.75,   '-0.005em'], desktop: [3.25,  3.5,    '-0.005em'] },
    medium:           { mobile: [1.75,  2.25,   '-0.005em'], desktop: [2.5,   2.75,   '-0.005em'] },
    small:            { mobile: [1.5,   2,      '-0.005em'], desktop: [2,     2.25,   '-0.005em'] },
    tagline:          { mobile: [0.875, 1,      '0.06em'],   desktop: [1,     1.25,   '0.06em'] },
    subtitle:         { mobile: [0.625, 0.875,  '0.06em'],   desktop: [0.75,  1,      '0.06em'] },
    bodyLarge:        { mobile: [1.25,  1.75,   '0'],        desktop: [1.5,   2,      '0'] },
    bodyMedium:       { mobile: [1,     1.5,    '0.005em'],  desktop: [1.25,  1.75,   '0.005em'] },
    bodySmall:        { mobile: [0.875, 1.25,   '0.005em'],  desktop: [1,     1.25,   '0.005em'] },
    bodyExtraSmall:   { mobile: [0.75,  1,      '0.005em'],  desktop: [0.875, 1.25,   '0.005em'] },
    buttonLarge:      { mobile: [1.25,  0.9375, '0'],        desktop: [1.5,   0.9375, '0'] },
    buttonMedium:     { mobile: [1,     0.8125, '0.005em'],  desktop: [1.25,  0.8125, '0.005em'] },
    buttonSmall:      { mobile: [0.875, 0.625,  '0.005em'],  desktop: [1,     0.625,  '0.005em'] },
    buttonExtraSmall: { mobile: [0.75,  0.5625, '0.005em'],  desktop: [0.875, 0.5625, '0.005em'] },
};
// Example usage: <p class='bodyLarge md:bodyMediumDesktop'>
// The separate desktop class is autogenerated by the generateFontSizes function.

const generateFontSizes = () => {
    const sizes = {};
    for (const [key, { mobile, desktop }] of Object.entries(fontSizes)) {
        sizes[key] = [`${mobile[0]}rem`, { lineHeight: `${mobile[1]}rem`, letterSpacing: mobile[2] }];
        sizes[`${key}Desktop`] = [`${desktop[0]}rem`, { lineHeight: `${desktop[1]}rem`, letterSpacing: desktop[2] }];
    }
    return sizes;
};

export default {
    content: ['./app/**/*.php', './resources/**/*.{php,vue,js}'],
    safelist: [
        'text-left',
        'text-center',
        'text-right',
        'opacity-0',
        'opacity-100',
        'translate-y-0',
        'flex',
        {
            pattern: /^animate-[a-zA-Z]+$/,
        },
        ...[...Array(10).keys()].flatMap(i => [`mt-[${i * 10}px]`]),
        {
            pattern: /animation-delay-\d{3,4}/,
        },
    ],
    theme: {
        container: {
            center: true,
            screens: {
                sm: '640px',
                md: '768px',
                lg: '1072px',
                xl: '1072px',
                '2xl': '1072px',
            },
        },
        extend: {
            colors: {
                neutral: {
                    10: '#F4F8FF',
                    20: '#E8F1FF',
                    30: '#D7E4F5',
                    40: '#C2D3E7',
                    50: '#9AB4CC',
                    60: '#688CA6',
                    70: '#436B85',
                    80: '#1C526E',
                    90: '#003954',
                    100: '#001D2A',
                },
                primary: {
                    10: '#ECF9FF',
                    20: '#D5EFFC',
                    30: '#B3E1F8',
                    40: '#91D1F1',
                    50: '#73C3EA',
                    60: '#51B3E3',
                    70: '#2FA3DC',
                    80: '#008FD3',
                    90: '#01659F',
                    100: '#00567F',
                },
                gradient: {
                    turquoise: '#39D1D9',
                    warmBlue: '#12BEE2',
                },
                body: '#3F4A5A',
            },
            spacing: {
                submenu: '72px',
                submenuAdmin: '72px',
            },
            fontFamily: {
                sans: ['TT Commons', 'sans-serif'],
            },
            fontSize: generateFontSizes(),
            borderRadius: {
                'max': '32px',
            },
            boxShadow: {
                'fade': '0px 24px 48px -20px rgba(34, 40, 49, 0.24)',
            },
            transitionProperty: {
                background: 'background',
            },
            blur: {
                'xs': '3px',
            },
            padding: {
                '18': '4.5rem',
            },
            minWidth: {
                '66': '16.5rem',
            },
            keyframes: {
                fadeIn: {
                    '0%': {
                        opacity: '0',
                    },
                    '100%': {
                        opacity: '1',
                    },
                },
                fadeInUp: {
                    '0%': {
                        opacity: '0',
                        transform: 'translateY(10px)',
                    },
                    '100%': {
                        opacity: '1',
                        transform: 'translateY(0)',
                    },
                },
                fadeInPop: {
                    '0%': {
                        opacity: '0',
                        transform: 'scale(0.8)',
                    },
                    '100%': {
                        opacity: '1',
                        transform: 'scale(1)',
                    },
                },
            },
            animation: {
                fadeIn: 'fadeIn 0.5s ease-in-out forwards',
                fadeInUp: 'fadeInUp 0.5s ease-in-out forwards',
                fadeInPop: 'fadeInPop 0.5s ease-in-out forwards',
            },
            willChange: {
                animation: 'opacity, transform',
            },
        },
    },
    plugins: [
        plugin(function ({ addBase, theme }) {
            addBase({
                'p, li': {
                    fontSize: theme('fontSize.bodyMedium[0]'),
                    lineHeight: theme('fontSize.bodyMedium[1].lineHeight'),
                    letterSpacing: theme('fontSize.bodyMedium[1].letterSpacing'),
                    color: theme('colors.body'),
                },
                'p, li, label, button, span': {
                    fontWeight: 300,
                },
                '@screen md': {
                    'p, li': {
                        fontSize: theme('fontSize.bodyMediumDesktop[0]'),
                        lineHeight: theme('fontSize.bodyMediumDesktop[1].lineHeight'),
                        letterSpacing: theme('fontSize.bodyMediumDesktop[1].letterSpacing'),
                    },
                },
            });
        }),
        plugin(function ({ addVariant }) {
            addVariant('admin-bar', 'body.admin-bar &');
        }),
        plugin(function ({ addUtilities }) {
            const animationDelay = {};
            for (let i = 1; i <= 30; i++) {
                const delay = i * 100;
                animationDelay[`.animation-delay-${delay}`] = { 'animation-delay': `${delay}ms` };
            }

            addUtilities(animationDelay, ['responsive', 'hover']);
        }),
        plugin(function ({ addUtilities }) {
            const scrollbarHide = {
                '.scrollbar-hide': {
                    '-ms-overflow-style': 'none',
                    'scrollbar-width': 'none',
                    '&::-webkit-scrollbar': {
                        display: 'none',
                    },
                },
            };

            addUtilities(scrollbarHide, ['responsive']);
        }),
        plugin(function ({ addComponents }) {
            addComponents({
                '.components-form-token-field__remove-token': {
                    padding: '0 2px !important',
                },
            });
        }),
        function ({ addUtilities }) {
            const newUtilities = {
                '@media (max-width: 1023px)': {
                    '.bg-gradient-override': {
                        background: 'linear-gradient(180deg, #CFF6D3 0%, #E4FBE6 100%) !important',
                    },
                },
            };
            addUtilities(newUtilities, ['responsive']);
        },
        function ({ addUtilities }) {
            addUtilities({
                '.cutout-br-large': {
                    clipPath: 'path("M328 0H0V280H125Q157 280 157 248 157 216 189 216H296Q328 216 328 184Z")',
                },
                '.cutout-br-small': {
                    clipPath: 'path("M208 0H0V156H132Q152 156 156 136 160 108 188 104 208 100 208 80Z");',
                }
            });
        },
    ],
};