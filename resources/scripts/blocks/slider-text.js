import { registerBlockType } from '@wordpress/blocks';
import {
    RichText,
    useBlockProps
} from '@wordpress/block-editor';
import { Button } from '@wordpress/components';
import { Fragment, useState } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader,
    TabNavigation,
    ItemEditor
} from './editor';

registerBlockType('sage/slider-text', {
    apiVersion: 2,
    title: 'Slider & Text',
    icon: 'images-alt2',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        sliders: {
            type: 'array',
            default: [{ image: '' }],
        },
        boxes: {
            type: 'array',
            default: [{ image: '', content: '' }],
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { title, description, sliders, boxes } = attributes;
        const [activeSlider, setActiveSlider] = useState(0);
        const [activeBox, setActiveBox] = useState(0);

        const updateSlider = (index, field, value) => {
            const newSliders = [...sliders];
            newSliders[index][field] = value;
            setAttributes({ sliders: newSliders });
        };
        const addSlider = () => {
            const newSliders = [...sliders, { image: '' }];
            setAttributes({ sliders: newSliders });
            setActiveSlider(newSliders.length - 1);
        };
        const removeSlider = (index) => {
            const newSliders = [...sliders];
            newSliders.splice(index, 1);
            setAttributes({ sliders: newSliders });
            setActiveSlider(Math.max(activeSlider - 1, 0));
        };

        const updateBox = (index, field, value) => {
            const newBoxes = [...boxes];
            newBoxes[index][field] = value;
            setAttributes({ boxes: newBoxes });
        };
        const addBox = () => {
            const newBoxes = [...boxes, { image: '', content: '' }];
            setAttributes({ boxes: newBoxes });
            setActiveBox(newBoxes.length - 1);
        };
        const removeBox = (index) => {
            const newBoxes = [...boxes];
            newBoxes.splice(index, 1);
            setAttributes({ boxes: newBoxes });
            setActiveBox(Math.max(activeBox - 1, 0));
        };

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Slider & Text</h2>
                        </div>

                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter title"
                            tagName="h2"
                        />

                        <TextControl
                            label="Description"
                            value={description}
                            onChange={(value) => setAttributes({ description: value })}
                            placeholder="Enter description"
                            tagName="p"
                            multiline={true}
                        />

                        <div className="mb-6 mt-6">
                            <div className="mb-4 border-b border-neutral-30 pb-2">
                                <h3 className="text-base font-medium text-neutral-90">Sliders</h3>
                            </div>

                            <TabNavigation
                                items={sliders}
                                activeTab={activeSlider}
                                onTabChange={setActiveSlider}
                                onAddItem={addSlider}
                                onRemoveItem={() => removeSlider(activeSlider)}
                                getItemTitle={(_, index) => `Slide ${index + 1}`}
                                itemName="Slide"
                                addButtonTitle="Add New Slide"
                            />

                            {sliders[activeSlider] && (
                                <ItemEditor
                                    itemIndex={activeSlider}
                                    onRemove={() => removeSlider(activeSlider)}
                                    itemName="Slide"
                                    showRemoveButton={sliders.length > 1}
                                >
                                    <ImageUploader
                                        image={sliders[activeSlider].image}
                                        onSelect={(url) => updateSlider(activeSlider, 'image', url)}
                                        onRemove={() => updateSlider(activeSlider, 'image', '')}
                                        label="Slide Image"
                                        description="Upload an image for this slide."
                                        height={48}
                                        objectFit="cover"
                                        altText="Slide Image"
                                    />
                                </ItemEditor>
                            )}
                        </div>

                        <div className="mb-6 mt-6">
                            <div className="mb-4 border-b border-neutral-30 pb-2">
                                <h3 className="text-base font-medium text-neutral-90">Boxes</h3>
                            </div>

                            <TabNavigation
                                items={boxes}
                                activeTab={activeBox}
                                onTabChange={setActiveBox}
                                onAddItem={addBox}
                                onRemoveItem={() => removeBox(activeBox)}
                                getItemTitle={(_, index) => `Box ${index + 1}`}
                                itemName="Box"
                                addButtonTitle="Add New Box"
                            />

                            {boxes[activeBox] && (
                                <ItemEditor
                                    itemIndex={activeBox}
                                    onRemove={() => removeBox(activeBox)}
                                    itemName="Box"
                                    showRemoveButton={boxes.length > 1}
                                >
                                    <ImageUploader
                                        image={boxes[activeBox].image}
                                        onSelect={(url) => updateBox(activeBox, 'image', url)}
                                        onRemove={() => updateBox(activeBox, 'image', '')}
                                        label="Box Image"
                                        description="Upload an image for this box."
                                        height={48}
                                        objectFit="cover"
                                        altText="Box Image"
                                    />

                                    <div className="mb-4">
                                        <label className="block text-sm font-medium text-neutral-70 mb-2">Box Content</label>
                                        <div className="p-3 bg-white rounded-md border border-neutral-30">
                                            <RichText
                                                tagName="div"
                                                placeholder="Enter box content (formatting enabled)"
                                                value={boxes[activeBox].content}
                                                onChange={(value) => updateBox(activeBox, 'content', value)}
                                                className="text-base"
                                                allowedFormats={['core/bold', 'core/italic', 'core/link']}
                                                style={{ border: '1px solid #E2E8F0' }}
                                            />
                                        </div>
                                        <p className="mt-1 text-xs text-neutral-60">Add formatted content for this box. Bold, italic, and links are supported.</p>
                                    </div>
                                </ItemEditor>
                            )}
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" value={attributes.description} />

                    {attributes.sliders?.map((slider, index) => (
                        slider.image && (
                            <img
                                key={`slider-${index}`}
                                src={slider.image}
                                alt={`Slider Image ${index + 1}`}
                            />
                        )
                    ))}

                    {attributes.boxes?.map((box, index) => (
                        <div key={`box-${index}`}>
                            {box.image && (
                                <img
                                    src={box.image}
                                    alt={`Box Image ${index + 1}`}
                                />
                            )}
                            <RichText.Content tagName="div" value={box.content} />
                        </div>
                    ))}
                </div>
                <div data-dynamic="sage/slider-text"></div>
            </div>
        );
    }
});
