document.addEventListener('DOMContentLoaded', function() {
    const internsSections = document.querySelectorAll('.interns-section');

    // Handle window resize events to recalculate heights for open items
    window.addEventListener('resize', debounce(() => {
        document.querySelectorAll('.intern-content:not(.closed)').forEach(content => {
            // Only update if height is not already auto
            if (content.style.height !== 'auto') {
                content.style.height = 'auto';
            }
        });
    }, 250));

    // Simple debounce function to limit resize event handling
    function debounce(func, wait) {
        let timeout;
        return function() {
            clearTimeout(timeout);
            timeout = setTimeout(func, wait);
        };
    }

    internsSections.forEach(section => {
        const internHeaders = section.querySelectorAll('.intern-header');
        const internContents = section.querySelectorAll('.intern-content');
        const profileImageContainer = section.querySelector('.profile-image-container');

        if (!internHeaders.length || !profileImageContainer) return;

        // Set up all intern contents initially
        internContents.forEach((content) => {
            // Set initial properties
            content.style.overflow = 'hidden';

            // Initially set all items to closed state
            content.style.height = '0';
            content.style.opacity = '0';
            content.classList.add('closed');

            // Add a class to track initialization state
            content.classList.add('not-animated');
        });

        // Open the first item with animation after a short delay
        if (internContents.length > 0) {
            // Use a slightly longer delay to ensure everything is fully rendered
            setTimeout(() => {
                const firstContent = internContents[0];
                const firstHeader = internHeaders[0];

                // Remove the not-animated class before opening
                firstContent.classList.remove('not-animated');

                // Open the first item
                openItem(firstHeader, firstContent);

                // Update profile image
                if (firstHeader) {
                    updateProfileImage(
                        firstHeader.getAttribute('data-image'),
                        firstHeader.getAttribute('data-name')
                    );
                }
            }, 100); // Slightly longer delay to ensure DOM is fully ready
        }

        // Remove the initial-closed class now that we've set up the items
        section.querySelectorAll('.initial-closed').forEach(item => {
            item.classList.remove('initial-closed');
        });

        // Get the first header to update the profile image
        if (internHeaders.length > 0) {
            const firstHeader = internHeaders[0];
            // Update profile image with the first intern's image
            updateProfileImage(
                firstHeader.getAttribute('data-image'),
                firstHeader.getAttribute('data-name')
            );
        }

        // Add click event to each intern header
        internHeaders.forEach(header => {
            header.addEventListener('click', function() {
                // Get the content element for this header
                const content = this.nextElementSibling;

                // Check if content is currently animating (has inline height style but not 0 or auto)
                const isAnimating = content.style.height &&
                                   content.style.height !== '0px' &&
                                   content.style.height !== 'auto';

                // Skip if animation is in progress
                if (isAnimating) return;

                const isActive = !content.classList.contains('closed');

                // If already active, close it
                if (isActive) {
                    // Close this item
                    closeItem(this, content);
                    return;
                }

                // Make sure all not-animated classes are removed
                internContents.forEach(c => {
                    c.classList.remove('not-animated');
                });

                // Close all items first
                internHeaders.forEach((h, i) => {
                    const c = internContents[i];
                    if (!c.classList.contains('closed')) {
                        closeItem(h, c);
                    }
                });

                // Then open the clicked item after a small delay to allow closing animation to start
                setTimeout(() => {
                    openItem(this, content);

                    // Update profile image
                    const userImage = this.getAttribute('data-image');
                    const userName = this.getAttribute('data-name');
                    updateProfileImage(userImage, userName);
                }, 50);
            });
        });

        // Helper function to accurately calculate content height
        function calculateContentHeight(content) {
            // Get the content inner element
            const contentInner = content.querySelector('.content-inner');

            if (!contentInner) {
                return content.scrollHeight;
            }

            // Get computed styles for padding
            const style = window.getComputedStyle(content);
            const paddingTop = parseFloat(style.paddingTop) || 0;
            const paddingBottom = parseFloat(style.paddingBottom) || 0;

            // Calculate total height including padding
            return contentInner.offsetHeight + paddingTop + paddingBottom;
        }

        // Function to open an item with animation
        function openItem(header, content) {
            // Set active styles
            header.classList.add('bg-[#F4F6FA]');

            // Ensure content is visible but still has 0 height for animation
            content.classList.remove('closed');
            content.style.opacity = '1';

            // Make sure we're starting from 0 height
            content.style.height = '0';
            content.style.overflow = 'hidden';

            // Force a reflow to ensure the browser registers the initial state
            content.offsetHeight;

            // Calculate the target height
            const targetHeight = calculateContentHeight(content);

            // Set the height explicitly to trigger the transition
            requestAnimationFrame(() => {
                content.style.height = targetHeight + 'px';

                // After animation completes, set height to auto to handle content changes
                setTimeout(() => {
                    content.style.height = 'auto';
                }, 300); // Match the transition duration
            });
        }

        // Function to close an item with animation
        function closeItem(header, content) {
            // Remove active styles
            header.classList.remove('bg-[#F4F6FA]');

            // If height is auto, we need to set it to a specific value first
            if (content.style.height === 'auto' || content.style.height === '') {
                // Calculate the current height
                const currentHeight = calculateContentHeight(content);
                content.style.height = currentHeight + 'px';

                // Force a reflow to ensure the browser registers the initial state
                content.offsetHeight;
            }

            // Use requestAnimationFrame to ensure the browser has processed the previous style
            requestAnimationFrame(() => {
                // Animate height to 0
                content.style.height = '0';
                content.style.opacity = '0';

                // Mark as closed after animation completes
                setTimeout(() => {
                    content.classList.add('closed');
                }, 300); // Match the transition duration
            });
        }

        // Function to update profile image with fade animation
        function updateProfileImage(imageUrl, altText) {
            if (!imageUrl) return;

            // Find the existing image element
            let imgElement = profileImageContainer.querySelector('img');
            let containerDiv = profileImageContainer.querySelector('div');

            // If image element exists, animate and update its attributes
            if (imgElement) {
                // Add fade-out class to start the animation
                imgElement.classList.add('opacity-0');
                imgElement.style.transition = 'opacity 0.3s ease-in-out';

                // Wait for fade-out to complete before updating the image
                setTimeout(() => {
                    // Update image source and alt text
                    imgElement.src = imageUrl;
                    imgElement.alt = altText;

                    // When the new image is loaded, fade it back in
                    imgElement.onload = function() {
                        // Trigger reflow to ensure transition applies
                        imgElement.offsetHeight;
                        // Remove the fade-out class to fade back in
                        imgElement.classList.remove('opacity-0');
                    };

                    // Fallback in case onload doesn't trigger
                    setTimeout(() => {
                        imgElement.classList.remove('opacity-0');
                    }, 50);
                }, 300); // Match the transition duration
            } else {
                // Get data from data attributes
                const containerClasses = profileImageContainer.getAttribute('data-container-classes') ||
                                        'rounded-max overflow-hidden p-11 pb-0 h-full w-full relative';
                const imgClasses = profileImageContainer.getAttribute('data-img-classes') ||
                                  'w-full h-full object-cover rounded-3xl rounded-bl-none rounded-br-none relative z-10 opacity-0';
                const bgGradient = profileImageContainer.getAttribute('data-bg-gradient') ||
                                  'bg-[radial-gradient(94.53%_94.53%_at_5.46%_5.47%,_#D5F6F3_0%,_#B2D6E6_100%)]';

                // Get overlay URLs from existing elements if they exist
                let cloudsUrl = '';
                let noiseUrl = '';

                // Find existing overlays to extract the correct URLs with cache-busting hashes
                const existingCloudsOverlay = section.querySelector('.clouds-overlay');
                const existingNoiseOverlay = section.querySelector('.noise-overlay');

                if (existingCloudsOverlay) {
                    // Extract URL from style attribute, removing url(' and ') parts
                    const cloudsStyle = existingCloudsOverlay.style.backgroundImage;
                    cloudsUrl = cloudsStyle.replace(/^url\(['"](.+)['"]\)$/, '$1');
                }

                if (existingNoiseOverlay) {
                    // Extract URL from style attribute, removing url(' and ') parts
                    const noiseStyle = existingNoiseOverlay.style.backgroundImage;
                    noiseUrl = noiseStyle.replace(/^url\(['"](.+)['"]\)$/, '$1');
                }

                // Create container div if it doesn't exist
                if (!containerDiv) {
                    containerDiv = document.createElement('div');
                    containerDiv.className = containerClasses + ' ' + bgGradient;
                    profileImageContainer.appendChild(containerDiv);

                    // Only create overlays if we have the URLs
                    if (cloudsUrl) {
                        // Create clouds overlay
                        const cloudsOverlay = document.createElement('div');
                        cloudsOverlay.className = 'clouds-overlay absolute inset-0 z-0 opacity-50 bg-no-repeat bg-cover';
                        cloudsOverlay.style.backgroundImage = `url('${cloudsUrl}')`;
                        containerDiv.appendChild(cloudsOverlay);
                    }

                    if (noiseUrl) {
                        // Create noise texture overlay
                        const noiseOverlay = document.createElement('div');
                        noiseOverlay.className = 'noise-overlay absolute inset-0 z-0 opacity-20 bg-repeat';
                        noiseOverlay.style.backgroundImage = `url('${noiseUrl}')`;
                        containerDiv.appendChild(noiseOverlay);
                    }
                }

                // Create image element (start with opacity 0)
                imgElement = document.createElement('img');
                imgElement.src = imageUrl;
                imgElement.alt = altText;
                imgElement.className = imgClasses;
                imgElement.style.transition = 'opacity 0.3s ease-in-out';

                // Add image to container
                containerDiv.appendChild(imgElement);

                // Fade in the image after it's added to the DOM
                setTimeout(() => {
                    imgElement.classList.remove('opacity-0');
                }, 50);
            }
        }
    });
});
