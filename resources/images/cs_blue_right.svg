<svg width="328" height="280" viewBox="0 0 328 280" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2703_4398)">
<rect width="328" height="280" rx="32" fill="url(#paint0_linear_2703_4398)"/>
<g opacity="0.5" filter="url(#filter0_f_2703_4398)">
<circle cx="276" cy="35" r="107" fill="#9ECCEB"/>
</g>
</g>
<defs>
<filter id="filter0_f_2703_4398" x="-45" y="-286" width="642" height="642" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="107" result="effect1_foregroundBlur_2703_4398"/>
</filter>
<linearGradient id="paint0_linear_2703_4398" x1="328" y1="-16.4138" x2="-12.154" y2="349.861" gradientUnits="userSpaceOnUse">
<stop stop-color="#F1F4F7"/>
<stop offset="1" stop-color="#F1F6FA"/>
</linearGradient>
<clipPath id="clip0_2703_4398">
<rect width="328" height="280" rx="32" fill="white"/>
</clipPath>
</defs>
</svg>
