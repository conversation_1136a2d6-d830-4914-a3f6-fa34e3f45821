<svg width="884" height="644" viewBox="0 0 884 644" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1939_17053)">
<rect width="884" height="644" fill="#F1F3F7"/>
<rect width="884" height="644" fill="#EDF2F9"/>
<g opacity="0.2" filter="url(#filter0_f_1939_17053)">
<circle cx="1026" cy="551" r="239" fill="url(#paint0_linear_1939_17053)"/>
</g>
<g opacity="0.2" filter="url(#filter1_f_1939_17053)">
<ellipse cx="558" cy="798" rx="343" ry="301" fill="url(#paint1_linear_1939_17053)"/>
</g>
</g>
<defs>
<filter id="filter0_f_1939_17053" x="287" y="-188" width="1478" height="1478" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1939_17053"/>
</filter>
<filter id="filter1_f_1939_17053" x="-285" y="-3" width="1686" height="1602" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_1939_17053"/>
</filter>
<linearGradient id="paint0_linear_1939_17053" x1="1047" y1="412" x2="959" y2="834" gradientUnits="userSpaceOnUse">
<stop stop-color="#39D1D9"/>
<stop offset="1" stop-color="#175255"/>
</linearGradient>
<linearGradient id="paint1_linear_1939_17053" x1="558" y1="497" x2="558" y2="1099" gradientUnits="userSpaceOnUse">
<stop stop-color="#12BEE2"/>
<stop offset="1" stop-color="#1C3BAA"/>
</linearGradient>
<clipPath id="clip0_1939_17053">
<rect width="884" height="644" fill="white"/>
</clipPath>
</defs>
</svg>
