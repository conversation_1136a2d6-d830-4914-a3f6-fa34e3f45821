@php
    $title = $attributes['title'] ?? '';
    $selectedUsers = $attributes['selectedUsers'] ?? [];
    $activeUser = $attributes['activeUser'] ?? null;

    // Get the first user as the active user if not specified
    if (empty($activeUser) && !empty($selectedUsers)) {
        $activeUser = $selectedUsers[0];
    }

    // Find the active user in the selected users array
    $activeUserIndex = 0;
    if (!empty($activeUser) && !empty($selectedUsers)) {
        foreach ($selectedUsers as $index => $user) {
            if ($user['id'] === $activeUser['id']) {
                $activeUserIndex = $index;
                break;
            }
        }
    }
@endphp

<div class="interns-section py-16">
    <div class="container px-5 mx-auto">
        <div class="mb-12">
            <div class="title-container">
                <x-title variation="large" class="max-w-sm">
                    {!! $title !!}
                    @if(!empty($selectedUsers))
                        <span class="profile-images-container">
                            @foreach($selectedUsers as $index => $user)
                                <span class="profile-thumbnail" style="margin-left: {{ $index > 0 ? '-20px' : '0' }}; z-index: {{ count($selectedUsers) - $index }};">
                                    <img
                                        src="{{ $user['profileImage'] }}"
                                        alt="{{ $user['name'] }}"
                                        class="w-12 h-12 rounded-full object-cover border-[3px] border-white"
                                    />
                                </span>
                            @endforeach
                        </span>
                    @endif
                </x-title>
            </div>
        </div>

        @if(!empty($selectedUsers))
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column: User List and Testimony -->
                <div class="flex flex-col">
                    <!-- User List -->
                    <div class="interns-accordion space-y-3">
                        @foreach($selectedUsers as $index => $user)
                            <div class="intern-item border border-[#E8EBF3] rounded-max overflow-hidden{{ $index !== 0 ? ' initial-closed' : '' }} lg:rounded-2xl">
                                <div
                                    class="intern-header flex justify-between items-center p-6 cursor-pointer {{ $index === 0 ? 'bg-[#F4F6FA]' : '' }}"
                                    data-name="{{ $user['name'] }}"
                                    data-position="{{ $user['position'] }}"
                                    data-testimony="{{ $user['testimony'] }}"
                                    data-image="{{ $user['profileImage'] }}"
                                    data-index="{{ $index }}"
                                >
                                    <div class="text-bodySmall lg:text-bodySmallDesktop">{{ $user['name'] }}</div>
                                    <div class="text-bodyExtraSmall lg:text-bodyExtraSmallDesktop text-[#566276]">{{ $user['position'] }}</div>
                                </div>

                                <div class="intern-content bg-white">
                                    <div class="content-inner p-6">
                                        <p class="text-bodySmall lg:text-bodySmallDesktop text-[#3F4A5A]">
                                            {!! $user['testimony'] !!}
                                        </p>
                                        @if(!empty($activeUser) && !empty($activeUser['profileImage']))
                                            <div class="rounded-[20px] block mt-5 overflow-hidden bg-[radial-gradient(94.53%_94.53%_at_5.46%_5.47%,_#D5F6F3_0%,_#B2D6E6_100%)] p-11 pb-0 h-full w-full relative lg:hidden">
                                                <!-- Clouds overlay - with proper asset URL -->
                                                <div class="clouds-overlay absolute inset-0 z-0 bg-no-repeat bg-cover"
                                                    style="background-image: url('{{ asset('images/clouds.png') }}');"></div>

                                                <!-- Noise texture overlay - with proper asset URL -->
                                                <div class="noise-overlay absolute inset-0 z-0 opacity-20 bg-repeat"
                                                    style="background-image: url('{{ asset('images/noise.svg') }}');"></div>

                                                <!-- Profile image -->
                                                <img
                                                    src="{{ $activeUser['profileImage'] }}"
                                                    alt="{{ $activeUser['name'] }}"
                                                    class="w-full h-full object-cover rounded-3xl rounded-bl-none rounded-br-none relative z-10"
                                                />
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Right Column: Profile Image -->
                <div class="profile-image-container hidden h-[548px] lg:block"
                     data-container-classes="rounded-max overflow-hidden p-11 pb-0 h-full w-full relative"
                     data-img-classes="w-full h-full object-cover rounded-3xl rounded-bl-none rounded-br-none relative z-10"
                     data-bg-gradient="bg-[radial-gradient(94.53%_94.53%_at_5.46%_5.47%,_#D5F6F3_0%,_#B2D6E6_100%)]">
                    @if(!empty($activeUser) && !empty($activeUser['profileImage']))
                        <div class="rounded-max hidden overflow-hidden bg-[radial-gradient(94.53%_94.53%_at_5.46%_5.47%,_#D5F6F3_0%,_#B2D6E6_100%)] p-11 pb-0 h-full w-full relative lg:block">
                            <!-- Clouds overlay - with proper asset URL -->
                            <div class="clouds-overlay absolute inset-0 z-0 bg-no-repeat bg-cover"
                                 style="background-image: url('{{ asset('images/clouds.png') }}');"></div>

                            <!-- Noise texture overlay - with proper asset URL -->
                            <div class="noise-overlay absolute inset-0 z-0 opacity-20 bg-repeat"
                                 style="background-image: url('{{ asset('images/noise.svg') }}');"></div>

                            <!-- Profile image -->
                            <img
                                src="{{ $activeUser['profileImage'] }}"
                                alt="{{ $activeUser['name'] }}"
                                class="w-full h-full object-cover rounded-3xl rounded-bl-none rounded-br-none relative z-10"
                            />
                        </div>
                    @endif
                </div>
            </div>
        @else
            <div class="text-center py-8 text-neutral-60">
                No interns selected. Please edit this block to add interns.
            </div>
        @endif
    </div>
</div>
