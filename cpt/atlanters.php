<?php
function register_atlanters_cpt() {
    $labels = array(
        'name'               => 'Atlanters',
        'singular_name'      => 'Atlanter',
        'menu_name'          => 'Atlanters',
        'name_admin_bar'     => 'Atlanter',
        'add_new'            => 'Add New',
        'add_new_item'       => 'Add New Atlanter',
        'new_item'           => 'New Atlanter',
        'edit_item'          => 'Edit Atlanter',
        'view_item'          => 'View Atlanter',
        'all_items'          => 'All Atlanters',
        'search_items'       => 'Search Atlanters',
        'parent_item_colon'  => 'Parent Atlanters:',
        'not_found'          => 'No atlanters found.',
        'not_found_in_trash' => 'No atlanters found in Trash.',
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'atlanters'),
        'capability_type'    => 'post',
        'map_meta_cap'       => true,
        'has_archive'        => true,
        'hierarchical'       => false,
        'supports'           => array(
            'title',
            'editor',
            'thumbnail',
            'page-attributes',
            'custom-fields',
        ),
        'show_in_rest'       => true,
    );

    register_post_type('atlanters', $args);
}
add_action('init', 'register_atlanters_cpt');

register_post_meta('atlanters', 'position', array(
    'type'       => 'string',
    'single'     => true,
    'auth_callback' => '__return_true',
    'show_in_rest' => array(
        'schema' => array(
            'type'    => 'string',
            'context' => array('view', 'edit')
        )
    ),
));

function atlanters_add_meta_box() {
    add_meta_box(
        'atlanters_position_box',
        'Position',
        'atlanters_position_callback',
        'atlanters',
        'side'
    );
}
add_action('add_meta_boxes', 'atlanters_add_meta_box');

function atlanters_position_callback($post) {
    wp_nonce_field('atlanters_save_position', 'atlanters_position_nonce');
    $value = get_post_meta($post->ID, 'position', true);
    echo '<label for="atlanters_position_field">Position: </label>';
    echo '<input type="text" id="atlanters_position_field" name="position" value="' . esc_attr($value) . '" style="width:100%;" />';
}

function atlanters_save_position($post_id) {
    if (!isset($_POST['atlanters_position_nonce']) || 
        !wp_verify_nonce($_POST['atlanters_position_nonce'], 'atlanters_save_position')) {
        return;
    }
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    if (isset($_POST['position'])) {
        update_post_meta($post_id, 'position', sanitize_text_field($_POST['position']));
    }
}
add_action('save_post', 'atlanters_save_position');
