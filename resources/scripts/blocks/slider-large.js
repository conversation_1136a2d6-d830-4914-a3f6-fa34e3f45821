import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { useState, useEffect } from '@wordpress/element';
import { SearchControl, Spinner, Button } from '@wordpress/components';
import apiFetch from '@wordpress/api-fetch';
import { commonAttributes, commonSave } from './common';
import { TextControl, EditorContainer } from './editor';

registerBlockType('sage/slider-large', {
    apiVersion: 2,
    title: 'Slider Large',
    icon: 'slides',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string',
            default: '',
        },
        description: {
            type: 'string',
            default: '',
        },
        selectedUsers: {
            type: 'array',
            default: [],
        },
        activeUser: {
            type: 'object',
            default: null,
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, description, selectedUsers, activeUser } = attributes;
        const [searchTerm, setSearchTerm] = useState('');
        const [searchResults, setSearchResults] = useState([]);
        const [isSearching, setIsSearching] = useState(false);
        const [showResults, setShowResults] = useState(false);

        // Search for users
        const searchUsers = async (term) => {
            if (!term || term.length < 3) {
                setSearchResults([]);
                setShowResults(false);
                return;
            }

            setIsSearching(true);
            setShowResults(true);

            try {
                const results = await apiFetch({
                    path: `/wp/v2/users?search=${encodeURIComponent(term)}&per_page=10`,
                });

                // Filter out users that are already selected
                const filteredResults = results.filter(
                    (user) => !selectedUsers.some((selected) => selected.id === user.id)
                );

                // Fetch custom profile images for each user
                const resultsWithCustomImages = await Promise.all(
                    filteredResults.map(async (user) => {
                        try {
                            // Get user meta data to check for custom profile image
                            const userData = await apiFetch({
                                path: `/wp/v2/users/${user.id}`,
                            });

                            // If user has a custom profile image, fetch it
                            if (userData.meta?.custom_profile_image_id) {
                                try {
                                    const media = await apiFetch({
                                        path: `/wp/v2/media/${userData.meta.custom_profile_image_id}`,
                                    });

                                    return {
                                        ...user,
                                        customProfileImage: media.source_url,
                                        position: userData.meta?.position || '',
                                    };
                                } catch (error) {
                                    // If media fetch fails, return user with default avatar
                                    return {
                                        ...user,
                                        position: userData.meta?.position || '',
                                    };
                                }
                            }

                            // Return user with position but no custom image
                            return {
                                ...user,
                                position: userData.meta?.position || '',
                            };
                        } catch (error) {
                            // If user data fetch fails, return original user
                            return user;
                        }
                    })
                );

                setSearchResults(resultsWithCustomImages);
            } catch (error) {
                console.error('Error searching users:', error);
                setSearchResults([]);
            } finally {
                setIsSearching(false);
            }
        };

        // Debounce search
        useEffect(() => {
            const timer = setTimeout(() => {
                searchUsers(searchTerm);
            }, 500);

            return () => clearTimeout(timer);
        }, [searchTerm]);

        // Add user to selected users
        const addUser = (user) => {
            // If we already have custom profile image and position from search results, use them
            const newUser = {
                id: user.id,
                name: user.name,
                position: user.position || '',
                mentorFeedback: '',
                profileImage: user.customProfileImage || user.avatar_urls?.['96'] || '',
            };

            // Get user meta data (we still need to fetch mentor feedback)
            apiFetch({
                path: `/wp/v2/users/${user.id}`,
            }).then((userData) => {
                // Update user with meta data if available
                const updatedUser = {
                    ...newUser,
                    // Only update position if it wasn't already set from search results
                    position: newUser.position || userData.meta?.position || '',
                    mentorFeedback: userData.meta?.mentor_feedback || '',
                };

                // Check if user has a custom profile image and we don't already have it
                if (userData.meta?.custom_profile_image_id && !user.customProfileImage) {
                    apiFetch({
                        path: `/wp/v2/media/${userData.meta.custom_profile_image_id}`,
                    }).then((media) => {
                        const updatedUserWithImage = {
                            ...updatedUser,
                            profileImage: media.source_url || updatedUser.profileImage,
                        };

                        setAttributes({
                            selectedUsers: [...selectedUsers, updatedUserWithImage],
                            activeUser: updatedUserWithImage,
                        });
                    }).catch(() => {
                        setAttributes({
                            selectedUsers: [...selectedUsers, updatedUser],
                            activeUser: updatedUser,
                        });
                    });
                } else {
                    setAttributes({
                        selectedUsers: [...selectedUsers, updatedUser],
                        activeUser: updatedUser,
                    });
                }
            }).catch(() => {
                setAttributes({
                    selectedUsers: [...selectedUsers, newUser],
                    activeUser: newUser,
                });
            });

            setSearchTerm('');
            setSearchResults([]);
            setShowResults(false);
        };

        // Remove user from selected users
        const removeUser = (userId) => {
            const updatedUsers = selectedUsers.filter((user) => user.id !== userId);
            setAttributes({
                selectedUsers: updatedUsers,
                activeUser: updatedUsers.length > 0 ? updatedUsers[0] : null,
            });
        };

        // Set active user
        const setActiveUserById = (userId) => {
            const user = selectedUsers.find((user) => user.id === userId);
            if (user) {
                setAttributes({ activeUser: user });
            }
        };

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Slider Large">
                    <div className="mb-6">
                        <TextControl
                            label="Block Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter title"
                            tagName="h2"
                        />
                    </div>

                    <div className="mb-6">
                        <TextControl
                            label="Block Description"
                            value={description}
                            onChange={(value) => setAttributes({ description: value })}
                            placeholder="Enter description"
                            tagName="p"
                        />
                    </div>

                    <div className="mb-6">
                        <div className="mb-4 border-b border-neutral-30 pb-2">
                            <h3 className="text-base font-medium text-neutral-90">Select Users</h3>
                        </div>

                        <div className="mb-4 relative">
                            <SearchControl
                                value={searchTerm}
                                onChange={(value) => setSearchTerm(value)}
                                placeholder="Search for users..."
                                className="mb-2"
                            />

                            {showResults && (
                                <div className="absolute z-10 w-full bg-white border border-neutral-30 rounded-md shadow-sm mt-1">
                                    {isSearching ? (
                                        <div className="p-4 text-center">
                                            <Spinner />
                                        </div>
                                    ) : searchResults.length > 0 ? (
                                        <ul className="p-0">
                                            {searchResults.map((user) => (
                                                <li
                                                    key={user.id}
                                                    className="p-2 hover:bg-neutral-10 cursor-pointer flex items-center"
                                                    onClick={() => addUser(user)}
                                                >
                                                    <img
                                                        src={user.customProfileImage || user.avatar_urls?.['24'] || ''}
                                                        alt={user.name}
                                                        className="w-6 !h-6 rounded-full mr-2 object-cover"
                                                    />
                                                    <div className="flex flex-col">
                                                        <span>{user.name}</span>
                                                        {user.position && (
                                                            <span className="text-xs text-neutral-60">{user.position}</span>
                                                        )}
                                                    </div>
                                                </li>
                                            ))}
                                        </ul>
                                    ) : searchTerm.length >= 3 ? (
                                        <div className="p-4 text-center text-neutral-60">
                                            No users found
                                        </div>
                                    ) : (
                                        <div className="p-4 text-center text-neutral-60">
                                            Type at least 3 characters to search
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>

                        <div className="mb-2">
                            <div className="mb-2">
                                <h4 className="text-sm font-medium text-neutral-70">Selected Users</h4>
                            </div>

                            {selectedUsers.length > 0 ? (
                                <div className="flex flex-wrap gap-2">
                                    {selectedUsers.map((user) => (
                                        <div
                                            key={user.id}
                                            className={`
                                                p-2 border rounded-md flex items-center gap-2 cursor-pointer
                                                ${activeUser?.id === user.id ? 'border-primary-50 bg-primary-20' : 'border-neutral-30 bg-primary-10'}
                                            `}
                                            onClick={() => setActiveUserById(user.id)}
                                        >
                                            <img
                                                src={user.profileImage}
                                                alt={user.name}
                                                className="w-6 !h-6 rounded-full"
                                            />
                                            <span className="pt-1">{user.name}</span>
                                            <button
                                                type="button"
                                                className="bg-transparent relative top-1 border-0 cursor-pointer pt-px text-3xl text-red-500 ml-1"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    removeUser(user.id);
                                                }}
                                            >
                                                ×
                                            </button>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="p-4 text-center text-neutral-60 border border-dashed border-neutral-30 rounded-md">
                                    No users selected. Search and add users above.
                                </div>
                            )}
                        </div>
                    </div>
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
