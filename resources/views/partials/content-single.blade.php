@if($post->post_type === 'case_study')
    @php
        $post_id = get_the_ID();
        $content = apply_filters('the_content', get_the_content());
        $all_industries  = get_the_terms($post_id, 'industry');
        $all_services    = get_the_terms($post_id, 'service');
        $primary_industry = get_yoast_primary_term($post_id, 'industry');
        $primary_service  = get_yoast_primary_term($post_id, 'service');

        $hero_image = null;
        $hero_image_id = get_post_meta($post->ID, '_hero_image_id', true);
        if ($hero_image_id) {
            $hero_image = wp_get_attachment_image_src($hero_image_id, 'full');
        }

        $serviceColors = [
            'data-engineering' => [
                'tagline' => '!text-[#861C31]',
                'bg'      => 'pink',
            ],
            'data-science-analytics' => [
                'tagline' => '!text-[#00567F]',
                'bg'      => 'blue',
            ],
            'product-design' => [
                'tagline' => '!text-[#862818]',
                'bg'      => 'peach',
            ],
            'software-development' => [
                'tagline' => '!text-[#647C22]',
                'bg'      => 'green',
            ],
        ];

        $taglineClass = 'text-[#861C31]';
        $bgColor = 'blue';

        if ($primary_service && isset($primary_service->slug) && array_key_exists($primary_service->slug, $serviceColors)) {
            $taglineClass = $serviceColors[$primary_service->slug]['tagline'];
            $bgColor = $serviceColors[$primary_service->slug]['bg'];
        }
    @endphp

    <header class="single-header relative">
        <div class="container flex items-center px-8 pb-10 pt-20 mx-auto lg:px-5 lg:py-16 lg:min-h-[400px]">
            <div class="lg:w-3/5">
                <x-title variation="tagline" class="mb-8 {{ $taglineClass }}">CASE STUDY</x-title>
                <x-title variation="hero" class="mb-2">{!! $title !!}</x-title>
                @if (has_excerpt($post->ID))
                    <p>{!! get_the_excerpt($post->ID) !!}</p>
                @endif
                @if(!empty($all_services))
                    <div class="post-tags flex flex-wrap gap-3 items-center mt-8 lg:mb-0">
                        <span class="w-full p-0 block text-[#303945] text-bodyMedium font-light lg:w-auto lg:text-bodyMediumDesktop">
                            Services
                        </span>
                        @foreach ($all_services as $service)
                            <span class="tag block text-[#303945] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#FBFBFD] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                {!! $service->name !!}
                            </span>
                        @endforeach
                    </div>
                @endif
                @if(!empty($all_services))
                    <div class="post-tags flex flex-wrap gap-3 items-center mt-3 lg:mb-0">
                        <span class="w-full p-0 block text-[#303945] text-bodyMedium font-light lg:w-auto lg:text-bodyMediumDesktop">
                            Industry
                        </span>
                        @foreach ($all_industries as $industry)
                            <span class="tag block text-[#303945] text-bodyExtraSmall whitespace-nowrap capitalize bg-[#FBFBFD] font-normal py-1 pb-0.5 px-2 rounded-full lg:text-bodyExtraSmallDesktop">
                                {!! $industry->name !!}
                            </span>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
        @if (!empty($hero_image))
            <div class="blur absolute -z-10 object-cover h-full w-full top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 lg:blur-none single-hero-image lg:mt-0 lg:px-0 lg:translate-x-0 lg:-translate-y-1/2 lg:h-full lg:left-auto lg:right-6 lg:-top-3 lg:w-2/5 lg:top-1/2">
                <img class="w-full h-full object-cover rounded-3xl overflow-hidden cutout-fade-desktop" src="{{ $hero_image[0] }}" alt="Hero Image">
            </div>
        @endif
        <x-floating-bg color="{{ $bgColor }}" />
    </header>
    {!! $content !!}
@else
    <header class="single-header relative">
        <div class="container flex items-center px-8 pb-10 pt-20 mx-auto lg:px-5 lg:py-10 lg:min-h-[400px]">
            <div class="lg:w-3/5">
                <x-title variation="hero" class="mb-4">{!! $title !!}</x-title>
                @if(has_excerpt($post->ID))
                    <p>{!! get_the_excerpt($post->ID) !!}</p>
                @endif
                @include('partials.entry-meta')
            </div>
        </div>
        @if(has_post_thumbnail($post->ID))
            <div class="single-hero-image relative -mt-[15%] h-1/2 lg:h-64 lg:mt-0 lg:px-0 lg:-translate-y-1/2 lg:h-full lg:absolute lg:right-6 lg:-top-3 lg:w-2/5 lg:top-1/2">
                <img class="w-full h-full object-cover cutout-fade rounded-3xl overflow-hidden" src="{{ get_the_post_thumbnail_url($post->ID, 'full') }}" />
            </div>
        @endif
        <x-floating-bg background="{{ asset('images/blog_bg.png') }}" />
    </header>

    <div class="container px-5 py-10 mx-auto scroll-smooth">
        @php
            $content = apply_filters('the_content', get_the_content());
            $headings = [];

            if (preg_match_all('/<h([2-6])([^>]*)>(.*?)<\/h\1>/is', $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $index => $match) {
                    $full_tag = $match[0];
                    $level = $match[1];
                    $attributes = $match[2];
                    $inner_html = $match[3];
                    $heading_text = trim(strip_tags($inner_html));

                    if (preg_match('/id="([^"]+)"/', $attributes, $id_match)) {
                        $id = $id_match[1];
                    } else {
                        $id = 'section-' . $index;
                        $attributes .= ' id="' . $id . '"';
                        $new_tag = '<h' . $level . $attributes . '>' . $inner_html . '</h' . $level . '>';
                        $content = str_replace($full_tag, $new_tag, $content);
                    }

                    $headings[] = [
                        'id' => $id,
                        'text' => $heading_text,
                        'level' => (int) $level,
                    ];
                }
            }
        @endphp

        <article {!! post_class('h-entry') !!}>
            <div class="flex flex-col lg:flex-row gap-6">
                <div class="w-full post-content order-2 lg:order-1 lg:w-3/4">
                    <div class="e-content mb-8">
                        {!! $content !!}
                    </div>

                    @if ($pagination)
                        <footer>
                            <nav class="page-nav" aria-label="Page">
                                {!! $pagination !!}
                            </nav>
                        </footer>
                    @endif

                    {!! comments_template() !!}
                </div>

                @if (!empty($headings))
                <div class="w-full order-1 lg:order-2 lg:w-1/4">
                    <div class="sticky top-10">
                        <h2 class="text-tagline font-semibold uppercase pt-4 px-5 pb-3 mb-5 bg-primary-10 text-primary-70 rounded-lg lg:text-taglineDesktop">Table of Contents</h2>
                        <ul class="table-of-contents">
                            @foreach ($headings as $heading)
                                <li class="text-bodySmall font-regular flex items-center transition-all px-[18px] mb-3 min-h-10 lg:text-bodySmallDesktop">
                                    <a href="#{{ $heading['id'] }}" class="transition-all text-[#6E7B91]">
                                        {{ $heading['text'] }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                        <div class="flex flex-col items-start p-5 green-bg !bg-[length:300%] rounded-2xl overflow-hidden">
                            <span class="text-bodyMedium font-medium mb-2 lg:text-bodyMediumDesktop">See where our expertise fits in?</span>
                            <x-button type="link" variant="secondary" :href="'#'">Let's talk!</x-button>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </article>
    </div>
@endif


@php
    $contact_form_id   = get_option('blog_post_contact_form_id');
    $contact_form_post = get_post($contact_form_id);
    $attributes = [];
    if ($contact_form_post) {
        $blocks = parse_blocks($contact_form_post->post_content);
        foreach ($blocks as $block) {
            if ($block['blockName'] === 'sage/contact-form') {
                $attributes = $block['attrs'];
                break;
            }
        }
    }
@endphp

@if($attributes)
    @include('sections.contact-form', ['attributes' => $attributes])
@endif

@php
$related_query = new WP_Query([
    'posts_per_page' => 3,
    'post__not_in' => [$post->ID],
    'tag__in' => wp_get_post_tags($post->ID, ['fields' => 'ids']),
    'orderby' => 'rand',
]);

if ($related_query->have_posts()) :
@endphp
<div class="container px-5 py-10 mx-auto scroll-smooth">
    <x-title variation="medium">Read more about similar topics</x-title>

    <div class="related-posts posts-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-16">
        @foreach($related_query->posts as $related_post)
            @php
                setup_postdata($related_post);
                $post_categories = get_the_category($related_post->ID);
                $category_slugs = [];
                foreach ($post_categories as $cat) {
                    $category_slugs[] = $cat->slug;
                }
                $author_id = $related_post->post_author;
                $author_name = get_the_author_meta('display_name', $author_id);
                $author_avatar = get_avatar_url($author_id);
                $permalink = get_permalink($related_post->ID);
                $excerpt = has_excerpt($related_post->ID) 
                    ? wp_strip_all_tags(get_the_excerpt($related_post->ID)) 
                    : wp_strip_all_tags(get_post_field('post_content', $related_post->ID));
            @endphp
            <div class="post-item flex flex-col bg-[#F4F6FA] rounded-3xl overflow-hidden hover:scale-105 transition-all relative" data-categories="{{ implode(',', $category_slugs) }}">
                <a class="absolute left-0 top-0 w-full h-full z-10" href="{{ $permalink }}"></a>
                <div class="post-featured-image relative h-40">
                    <div class="top-5 left-5 rounded-full backdrop-blur-md bg-white/45 px-3 pt-[2px] absolute">{{ get_the_date('d.m.Y', $related_post->ID) }}</div>
                    @if (has_post_thumbnail($related_post->ID))
                        <img class="h-full w-full object-cover" src="{{ get_the_post_thumbnail_url($related_post->ID, 'full') }}" alt="{{ get_the_title($related_post->ID) }}">
                    @endif
                </div>
                <div class="p-5 px-5 h-full flex flex-col">
                    @if ($post_categories)
                        <div class="post-categories flex flex-wrap gap-2 mt-2">
                            @foreach($post_categories as $category)
                                <span class="tag bg-[#E8EBF3] pt-0.5 px-3 rounded-full text-bodyExtraSmall whitespace-nowrap capitalize lg:text-bodyExtraSmallDesktop">{{ $category->name }}</span>
                            @endforeach
                        </div>
                    @endif
                    <div class="py-2 mt-4">
                        <h2 class="text-bodyLarge mb-1 lg:text-bodyLargeDesktop">
                            <a href="{{ get_permalink($related_post->ID) }}">{!! get_the_title($related_post->ID) !!}</a>
                        </h2>
                        <div class="relative h-10 overflow-hidden text-bodyExtraSmall lg:text-bodyExtraSmallDesktop">
                            <p class="text-bodyExtraSmall h-14 text-[#3F4A5A] lg:text-bodyExtraSmallDesktop" style="-webkit-line-clamp:2;display:-webkit-box;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;">{!! $excerpt !!}</p>
                        </div>
                    </div>
                </div>
            </div>
            @php wp_reset_postdata(); @endphp
        @endforeach
    </div>
</div>
@php
endif;
@endphp