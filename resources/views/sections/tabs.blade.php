@php
    $title = $attributes['title'] ?? '';
    $description = $attributes['description'] ?? '';
    $tabs = $attributes['tabs'] ?? [];
@endphp

<div class="tabs-section relative flex justify-center items-center">
    <div class="container px-5 py-16">
        <div class="tabs-header text-center mb-4 lg:mb-16">
            @if($title)
                <x-title variation="hero" level="h2" class="mb-4">{!! $title !!}</x-title>
            @endif
            @if($description)
                <p class="text-bodyLarge lg:text-bodyLargeDesktop">{!! $description !!}</p>
            @endif
        </div>

        <div class="tabs-navigation flex justify-center mb-6 hidden lg:flex">
            <div class="flex flex-wrap bg-white/40 rounded-full p-4 gap-x-3">
                @foreach($tabs as $index => $tab)
                    <button
                        class="@if($index === 0) bg-white @else bg-transparent @endif rounded-full px-4 pt-2.5 pb-1.5 transition-all duration-500"
                        data-tab-index="{{ $index }}"
                    >
                        {{ $tab['tabButtonTitle'] ?? "Tab " . ($index + 1) }}
                    </button>
                @endforeach
            </div>
        </div>

        <div class="swiper-navigation flex justify-between items-center lg:hidden">
            <div class="swiper-pagination !bottom-6"></div>
        </div>

        <div class="tabs-content relative swiper">
            <div class="swiper-wrapper lg:min-h-96">
                @foreach($tabs as $index => $tab)
                    <div class="swiper-slide tab-content-item bg-white/40 p-8 rounded-3xl transition-all duration-500 lg:p-6" data-tab-content="{{ $index }}">
                        <div class="tab-content-inner h-full flex flex-col lg:flex-row items-center">
                            <div class="tab-text flex flex-col items-start w-full mb-4 lg:w-1/2 lg:mb-0">
                                @if(isset($tab['title']))
                                    <x-title variation="large" class="mb-4">{!! $tab['title'] !!}</x-title>
                                @endif
                                @if(isset($tab['description']))
                                    <p class="mb-4">{!! $tab['description'] !!}</p>
                                @endif
                                @if(isset($tab['buttonText']) && isset($tab['buttonLink']))
                                    <x-button type="link" variant="secondary" size="small" :href="$tab['buttonLink'] ?? '#'">{{ $tab['buttonText'] }}</x-button>
                                @endif
                            </div>
                            @if(isset($tab['image']))
                                <div class="tab-image h-full w-full lg:w-1/2">
                                    <img src="{{ $tab['image'] }}" alt="{{ $tab['title'] ?? '' }}" class="rounded-3xl w-full h-96 object-cover">
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <x-floating-bg color="blue" type="dynamic" />
</div>