import glob from 'glob';
import path from 'path';

/**
 * Compiler configuration
 *
 * @see {@link https://roots.io/sage/docs sage documentation}
 * @see {@link https://bud.js.org/learn/config bud.js configuration guide}
 *
 * @type {import('@roots/bud').Config}
 */
export default async (app) => {
  /**
   * Application assets & entrypoints
   *
   * @see {@link https://bud.js.org/reference/bud.entry}
   * @see {@link https://bud.js.org/reference/bud.assets}
   */

  const blockEntries = glob.sync('./resources/scripts/blocks/*.js').reduce((entries, file) => {
    const blockName = path.basename(file, '.js');
    entries[blockName] = `@scripts/blocks/${blockName}`;
    return entries;
  }, {});

  app
    .entry('app', ['@scripts/app', '@styles/app'])
    .entry('editor', ['@scripts/editor', '@styles/editor'])
    .entry('menu-editor', ['@scripts/menu-editor', '@styles/menu-editor', 'jquery'])
    .entry('custom', ['@styles/custom'])
    .entry('button', ['@scripts/button', 'jquery'])
    .entry('accordion-init', ['@scripts/accordion-init', 'jquery'])
    .entry('atlanters-init', ['@scripts/atlanters-init', 'jquery'])
    .entry('animation', ['@scripts/animation', 'jquery'])
    .entry('blog-init', ['@scripts/blog-init', 'jquery'])
    .entry('carousel-init', ['@scripts/carousel-init', 'jquery'])
    .entry('case-studies-collection-init', ['@scripts/case-studies-collection-init', 'jquery'])
    .entry('contact-form-init', ['@scripts/contact-form-init', 'jquery'])
    .entry('counter-init', ['@scripts/counter-init', 'jquery'])
    .entry('cta-init', ['@scripts/cta-init', 'jquery'])
    .entry('dynamic-clamp', ['@scripts/dynamic-clamp', 'jquery'])
    .entry('grid-init', ['@scripts/grid-init', 'jquery'])
    .entry('hero-init', ['@scripts/hero-init', 'jquery'])
    .entry('case-studies-init', ['@scripts/case-studies-init', 'jquery'])
    .entry('case-study-technologies-init', ['@scripts/case-study-technologies-init', 'jquery'])
    .entry('nav', ['@scripts/nav', 'jquery'])
    .entry('partnership-models-init', ['@scripts/partnership-models-init', 'jquery'])
    .entry('single', ['@scripts/single', 'jquery'])
    .entry('slider-text-init', ['@scripts/slider-text-init', 'jquery'])
    .entry('slider-large-init', ['@scripts/slider-large-init', 'jquery'])
    .entry('tabs-init', ['@scripts/tabs-init', 'jquery'])
    .entry('testimonials-init', ['@scripts/testimonials-init', 'jquery'])
    .entry('interns-init', ['@scripts/interns-init', 'jquery'])
    .entry('newsletter-form-init', ['@scripts/newsletter-form-init', 'jquery'])
    .assets(['images']);

  Object.keys(blockEntries).forEach(blockName => {
    app.entry(blockName, blockEntries[blockName]);
  });

  /**
   * Set public path
   *
   * @see {@link https://bud.js.org/reference/bud.setPublicPath}
   */
//   app.setPublicPath('/app/themes/sage/public/');
  app.setPublicPath('/wp-content/themes/atlantbh/public/');


  /**
   * Development server settings
   *
   * @see {@link https://bud.js.org/reference/bud.setUrl}
   * @see {@link https://bud.js.org/reference/bud.setProxyUrl}
   * @see {@link https://bud.js.org/reference/bud.watch}
   */
  app
    .setUrl('http://localhost:3000')
    .setProxyUrl('http://example.test')
    .watch(['resources/views', 'app']);

  /**
   * Generate WordPress `theme.json`
   *
   * @note This overwrites `theme.json` on every build.
   *
   * @see {@link https://bud.js.org/extensions/sage/theme.json}
   * @see {@link https://developer.wordpress.org/block-editor/how-to-guides/themes/theme-json}
   */
  app.wpjson
    .setSettings({
      background: {
        backgroundImage: true,
      },
      color: {
        custom: false,
        customDuotone: false,
        customGradient: false,
        defaultDuotone: false,
        defaultGradients: false,
        defaultPalette: false,
        duotone: [],
      },
      custom: {
        spacing: {},
        typography: {
          'font-size': {},
          'line-height': {},
        },
      },
      spacing: {
        padding: true,
        units: ['px', '%', 'em', 'rem', 'vw', 'vh'],
      },
      typography: {
        customFontSize: false,
      },
    })
    .useTailwindColors()
    .useTailwindFontFamily()
    .useTailwindFontSize();
};
