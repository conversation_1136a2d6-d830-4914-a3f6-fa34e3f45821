<svg width="2122" height="1983" viewBox="0 0 2122 1983" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_3028_3075)">
<circle cx="874" cy="1263" r="183" fill="#F1D8D8"/>
</g>
<g opacity="0.6" filter="url(#filter1_f_3028_3075)">
<circle cx="583" cy="1400" r="183" fill="#F1D8D8"/>
</g>
<g filter="url(#filter2_f_3028_3075)">
<circle cx="583" cy="909" r="183" fill="#F1E9D8"/>
</g>
<g filter="url(#filter3_f_3028_3075)">
<circle cx="1539" cy="937" r="183" fill="#D8E0F1"/>
</g>
<g filter="url(#filter4_f_3028_3075)">
<circle cx="1248" cy="583" r="183" fill="#D8F1E1"/>
</g>
<defs>
<filter id="filter0_f_3028_3075" x="291" y="680" width="1166" height="1166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_3028_3075"/>
</filter>
<filter id="filter1_f_3028_3075" x="0" y="817" width="1166" height="1166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_3028_3075"/>
</filter>
<filter id="filter2_f_3028_3075" x="0" y="326" width="1166" height="1166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_3028_3075"/>
</filter>
<filter id="filter3_f_3028_3075" x="956" y="354" width="1166" height="1166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_3028_3075"/>
</filter>
<filter id="filter4_f_3028_3075" x="665" y="0" width="1166" height="1166" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="200" result="effect1_foregroundBlur_3028_3075"/>
</filter>
</defs>
</svg>
