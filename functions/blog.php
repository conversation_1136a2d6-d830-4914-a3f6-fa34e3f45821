<?php
add_action('wp_ajax_load_more_posts', 'load_more_posts_ajax_handler');
add_action('wp_ajax_nopriv_load_more_posts', 'load_more_posts_ajax_handler');

function load_more_posts_ajax_handler() {
    $category       = isset($_POST['category']) ? sanitize_text_field($_POST['category']) : 'all';
    $page           = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $posts_per_page = isset($_POST['posts_per_page']) ? intval($_POST['posts_per_page']) : 6;

    $args = array(
        'post_status'    => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged'          => $page,
    );

    if ($category !== 'all') {
        $args['category_name'] = $category;
    }

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        ob_start();
        while ($query->have_posts()) {
            $query->the_post();
            $post_id         = get_the_ID();
            $post_categories = get_the_category($post_id);
            $category_slugs  = $post_categories ? wp_list_pluck($post_categories, 'slug') : [];
            $author_id       = get_the_author_meta('ID');
            $author_name     = get_the_author();
            $author_avatar   = get_avatar_url($author_id);
            $permalink       = get_permalink($post_id);
        
            echo \Roots\view('partials.blog-post-item', [
                'permalink'      => $permalink,
                'post_id'        => $post_id,
                'date'           => get_the_date('d.m.Y', $post_id),
                'has_thumbnail'  => has_post_thumbnail($post_id),
                'thumbnail_url'  => get_the_post_thumbnail_url($post_id, 'full'),
                'title'          => get_the_title($post_id),
                'excerpt'        => wp_trim_words(get_the_excerpt(), 20),
                'author_avatar'  => $author_avatar,
                'author_name'    => $author_name,
                'categories'     => $post_categories,
                'category_slugs' => $category_slugs,
            ])->render();
        }
        wp_reset_postdata();
        $posts_html     = ob_get_clean();
        $max_pages      = $query->max_num_pages;
        $found_posts    = $query->found_posts;
        $posts_returned = $query->post_count;

        wp_send_json_success(array(
            'posts_html'     => $posts_html,
            'max_pages'      => $max_pages,
            'found_posts'    => $found_posts,
            'posts_returned' => $posts_returned,
        ));
    } else {
        wp_send_json_error('No posts found');
    }
}
