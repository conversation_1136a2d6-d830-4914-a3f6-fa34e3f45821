document.addEventListener('DOMContentLoaded', () => {
    const newsletterForms = document.querySelectorAll('.newsletter-form-section form');
    
    if (newsletterForms.length === 0) return;
    
    newsletterForms.forEach(form => {
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            const emailInput = form.querySelector('input[type="email"]');
            const email = emailInput.value.trim();
            
            if (!email) {
                alert('Please enter your email address.');
                return;
            }
            
            // For now, just show a success message
            // This can be replaced with actual form submission logic later
            alert('Thank you for subscribing! This is a placeholder message.');
            form.reset();
        });
    });
});
