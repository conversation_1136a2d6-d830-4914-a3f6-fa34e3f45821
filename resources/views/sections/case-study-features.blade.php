@php
    $blockId = 'case-study-features-' . uniqid();
@endphp

<div id="{{ $blockId }}" class="case-studies-features relative flex justify-center items-center mb-8">
    <x-case-study-styles :accentColor="$attributes['accentColor']" blockClass="case-studies-features" :blockId="$blockId" />
    <div class="container px-5 py-10">
        <div class="relative max-w-3xl flex items-start gap-6 lg:gap-12">
            <div class="timeline after:w-1 after:h-full after:absolute after:left-[19px]">
                <div class="icon-container flex items-center justify-center w-[42px] h-[42px] rounded-full">
                    <img class="w-5 h-5 object-contain" src="{{ $attributes['icon'] }}" />
                </div>
            </div>
            <div>
                @if(!empty($attributes['title']))
                    <x-title class="mb-4" variation="medium">{!! $attributes['title'] !!}</x-title>
                @endif

                @if(!empty($attributes['items']))
                    <div class="flex flex-col gap-4">
                        @foreach($attributes['items'] as $item)
                            <div class="bg-gradient-to-r from-[#f2f6f6] to-[#f1f6f2] flex flex-col justify-between gap-6 p-8 rounded-3xl overflow-hidden lg:flex-row">
                                @if(!empty($item['featureTitle']))
                                    <span class="text-bodyLarge text-[#303945] font-normal lg:text-bodyLargeDesktop">
                                        {!! $item['featureTitle'] !!}
                                    </span>
                                @endif
                                <p class="text-bodySmall lg:text-bodySmallDesktop @if(!empty($item['featureTitle'])) lg:w-96 @endif">
                                    {!! $item['featureDescription'] !!}
                                </p>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
