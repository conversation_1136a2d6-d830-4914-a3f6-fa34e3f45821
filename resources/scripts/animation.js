document.addEventListener("DOMContentLoaded", function () {
    const elements = document.querySelectorAll('[data-animate]');

    const toCamelCase = (str) => {
        return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
    };

    elements.forEach((element) => {
        element.style.opacity = '0';

        const animationType = toCamelCase(element.getAttribute('data-animate'));
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    element.classList.add(`animate-${animationType}`);
                }
            });
        }, { threshold: 1.0 });

        observer.observe(element);
    });
});

document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();

        const targetId = this.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
            const offset = 50;
            const elementPosition = targetElement.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: "smooth"
            });
        }
    });
});
