<?php

// Register The Auto Loader
if (!file_exists($composer = __DIR__.'/vendor/autoload.php')) {
    wp_die(__('Error locating autoloader. Please run <code>composer install</code>.', 'sage'));
}
require $composer;

// Register The Bootloader
if (!function_exists('\Roots\bootloader')) {
    wp_die(
        __('You need to install Acorn to use this theme.', 'sage'),
        '',
        [
            'link_url' => 'https://roots.io/acorn/docs/installation/',
            'link_text' => __('Acorn Docs: Installation', 'sage'),
        ]
    );
}
\Roots\bootloader()->boot();

// Register Sage Theme Files
collect(['setup', 'filters'])
    ->each(function ($file) {
        if (!locate_template($file = "app/{$file}.php", true, true)) {
            wp_die(
                sprintf(__('Error locating <code>%s</code> for inclusion.', 'sage'), $file)
            );
        }
    });

require_once __DIR__ . '/functions/settings/index.php'; // WordPress dashboard theme settings

require_once __DIR__ . '/functions/upload.php'; // SVG uploads

require_once __DIR__ . '/functions/nav.php'; // Custom navigation and WP dashboard menu editor functionality

require_once __DIR__ . '/functions/blog.php'; // Custom functionality pertaining to the blog block

require_once __DIR__ . '/functions/clone.php'; // Post & page cloning functionality

require_once __DIR__ . '/functions/enlighter.php'; // Custom enqueue for EnlighterJS

require_once __DIR__ . '/functions/comments.php'; // Comment related functionalities

require_once __DIR__ . '/functions/form.php'; // Form related functionalities

require_once __DIR__ . '/functions/image-optimization.php'; // Image optimization and WebP/AVIF conversion

require_once __DIR__ . '/cpt/case_studies.php'; // Case Studies Custom Post Type

require_once __DIR__ . '/cpt/atlanters.php'; // Atlanters in Focus Custom Post Type

require_once __DIR__ . '/functions/user-profile.php'; // User profile extensions for interns

/*
 * To keep this functions.php file clean and maintainable, please follow these guidelines:
 *
 * 1. Before adding new functionality, check if WordPress or Sage already have it covered:
 *    - WordPress Documentation: https://wordpress.org/documentation/
 *    - Sage Documentation: https://roots.io/sage/docs/
 *
 * 2. If custom code is needed, be a hero and place it in the right folder:
 *    - functions/ for general custom functions
 *    - cpt/ for custom post types
 *    - Create a new file for each distinct functionality.
 *
 * 3. Remember that no one enjoys playing hide and seek with code, and that keeping
 *    things organized and documented will likely make someone's life easier.
 *
 *    Happy coding! :)
 */
