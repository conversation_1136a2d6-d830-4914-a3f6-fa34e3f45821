import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps, RichText } from '@wordpress/block-editor';
import { ComboboxControl, Button } from '@wordpress/components';
import { Fragment, useState, useEffect } from '@wordpress/element';
import apiFetch from '@wordpress/api-fetch';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader
} from './editor';

registerBlockType('sage/blog', {
    apiVersion: 2,
    title: 'Blog',
    icon: 'admin-post',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        featuredPostId: {
            type: 'number',
        },
        featuredPostTitle: {
            type: 'string',
        },
        featuredPostExcerpt: {
            type: 'string',
        },
        featuredPostImage: {
            type: 'string',
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const {
            title,
            description,
            backgroundImage,
            featuredPostId,
            featuredPostTitle,
            featuredPostExcerpt,
            featuredPostImage,
        } = attributes;
        const [inputValue, setInputValue] = useState(featuredPostTitle || '');
        const [searchResults, setSearchResults] = useState([]);
        const [isLoading, setIsLoading] = useState(false);

        useEffect(() => {
            if (inputValue) {
                setIsLoading(true);
                apiFetch({
                    path: `/wp/v2/posts?search=${encodeURIComponent(inputValue)}&per_page=20&_embed`,
                })
                    .then((posts) => {
                        const options = posts.map((post) => ({
                            label: post.title.rendered,
                            value: post.id,
                            excerpt: post.excerpt.rendered,
                            image:
                                post._embedded &&
                                post._embedded['wp:featuredmedia'] &&
                                post._embedded['wp:featuredmedia'][0] &&
                                post._embedded['wp:featuredmedia'][0].source_url
                                    ? post._embedded['wp:featuredmedia'][0].source_url
                                    : '',
                        }));
                        setSearchResults(options);
                        setIsLoading(false);
                    })
                    .catch(() => {
                        setSearchResults([]);
                        setIsLoading(false);
                    });
            } else {
                setSearchResults([]);
            }
        }, [inputValue]);

        useEffect(() => {
            setInputValue(featuredPostTitle || '');
        }, [featuredPostTitle]);

        return (
            <Fragment>
                <div {...useBlockProps()}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Blog</h2>
                        </div>

                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter blog section title"
                            tagName="h2"
                        />

                        <TextControl
                            label="Description"
                            value={description}
                            onChange={(value) => setAttributes({ description: value })}
                            placeholder="Enter blog section description"
                            tagName="p"
                            multiline={true}
                        />

                        <ImageUploader
                            image={backgroundImage}
                            onSelect={(url) => setAttributes({ backgroundImage: url })}
                            onRemove={() => setAttributes({ backgroundImage: '' })}
                            label="Background Image"
                            description="Upload a background image for the blog section."
                            height={48}
                            objectFit="cover"
                            altText="Blog Background"
                        />

                        <div className="mb-6 mt-6">
                            <div className="flex justify-between items-center mb-2">
                                <label className="block text-sm font-medium text-neutral-70">Featured Article</label>
                            </div>
                            <div className="p-4 bg-neutral-20 rounded-md">
                                {!featuredPostId && (
                                    <div className="p-3 bg-white rounded-md border border-neutral-30">
                                        <ComboboxControl
                                            label="Search and select a featured article"
                                            value={inputValue}
                                            onChange={(selectedValue) => {
                                                const selectedOption = searchResults.find(
                                                    (option) => option.value === selectedValue
                                                );

                                                if (selectedOption) {
                                                    setAttributes({
                                                        featuredPostId: selectedOption.value,
                                                        featuredPostTitle: selectedOption.label,
                                                        featuredPostExcerpt: selectedOption.excerpt,
                                                        featuredPostImage: selectedOption.image,
                                                    });
                                                    setInputValue(selectedOption.label);
                                                } else {
                                                    setAttributes({
                                                        featuredPostId: null,
                                                        featuredPostTitle: '',
                                                        featuredPostExcerpt: '',
                                                        featuredPostImage: '',
                                                    });
                                                    setInputValue('');
                                                }
                                            }}
                                            onFilterValueChange={(newValue) => {
                                                setInputValue(newValue);
                                            }}
                                            options={searchResults}
                                            isLoading={isLoading}
                                        />
                                        <p className="mt-1 text-xs text-neutral-60">Search for a post to feature in the blog section</p>
                                    </div>
                                )}
                                {featuredPostId && (
                                    <div className="p-4 bg-white rounded-md border border-neutral-30 relative">
                                        <div className="flex justify-between items-start mb-3">
                                            <h3 className="text-base font-medium m-0">Selected Article</h3>
                                            <Button
                                                className="p-1 text-xs bg-red-50 border border-red-300 text-red-600 rounded"
                                                onClick={() => {
                                                    setAttributes({
                                                        featuredPostId: null,
                                                        featuredPostTitle: '',
                                                        featuredPostExcerpt: '',
                                                        featuredPostImage: '',
                                                    });
                                                    setInputValue('');
                                                }}
                                                icon="no-alt"
                                                iconSize={16}
                                                aria-label="Remove featured article"
                                            />
                                        </div>
                                        <div className="flex flex-col md:flex-row gap-4">
                                            {featuredPostImage && (
                                                <div className="md:w-1/3">
                                                    <img
                                                        className="w-full h-auto object-cover rounded-md"
                                                        src={featuredPostImage}
                                                        alt={featuredPostTitle}
                                                    />
                                                </div>
                                            )}
                                            <div className="md:w-2/3">
                                                <h4 className="text-lg font-medium mb-2" dangerouslySetInnerHTML={{ __html: featuredPostTitle }} />
                                                <div
                                                    className="text-sm text-neutral-70"
                                                    dangerouslySetInnerHTML={{ __html: featuredPostExcerpt }}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <p className="mt-1 text-xs text-neutral-60">Select a featured article to display prominently in the blog section</p>
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();

        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" value={attributes.description} />

                    {attributes.backgroundImage && (
                        <img
                            src={attributes.backgroundImage}
                            alt={attributes.title ? `${attributes.title} - Background` : "Background Image"}
                        />
                    )}

                    <h3>Featured Article</h3>
                    <RichText.Content tagName="h4" value={attributes.featuredPostTitle} />
                    <div dangerouslySetInnerHTML={{ __html: attributes.featuredPostExcerpt }} />

                    {attributes.featuredPostImage && (
                        <img
                            src={attributes.featuredPostImage}
                            alt={attributes.featuredPostTitle ? `${attributes.featuredPostTitle} - Featured Image` : "Featured Article Image"}
                        />
                    )}
                </div>
                <div data-dynamic="sage/blog"></div>
            </div>
        );
    }
});
