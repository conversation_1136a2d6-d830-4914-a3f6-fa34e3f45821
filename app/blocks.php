<?php

namespace App;

use Roots\view;

/**
 * Extract accent color from block content if it's not directly available in the attributes
 *
 * @param array $attributes Block attributes
 * @param string $content Block content
 * @return array Updated attributes with accent color
 */
function extract_accent_color($attributes, $content) {
    // If accent color is already set, return the attributes as is
    if (!empty($attributes['accentColor'])) {
        return $attributes;
    }

    // Try to extract accent color from data-accent-color attribute
    if (preg_match('/data-accent-color="([^"]+)"/', $content, $matches)) {
        $attributes['accentColor'] = $matches[1];
    }

    return $attributes;
}

add_action('init', function () {
    $blocksDir = get_template_directory() . '/resources/scripts/blocks';
    $blocks = glob($blocksDir . '/*.js');

    foreach ($blocks as $blockFile) {
        $blockName = basename($blockFile, '.js');
        $templatePath = 'sections.' . $blockName;

        if (view()->exists($templatePath)) {
            register_block_type('sage/' . $blockName, [
                'render_callback' => function ($attributes, $content) use ($templatePath) {
                    // Extract accent color from content if needed
                    $attributes = extract_accent_color($attributes, $content);

                    return $content . view($templatePath, [
                        'attributes' => $attributes,
                    ])->render();
                }
            ]);
        }
    }
});
