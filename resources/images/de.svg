<svg width="52" height="52" viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="52" height="52" rx="12" fill="url(#paint0_linear_2421_9810)"/>
<rect x="4" y="4" width="44" height="44" rx="8" fill="white" fill-opacity="0.8"/>
<g filter="url(#filter0_i_2421_9810)">
<ellipse cx="26" cy="31.5" rx="10" ry="3.5" fill="#DEA5E1"/>
</g>
<g filter="url(#filter1_if_2421_9810)">
<ellipse cx="26" cy="29.5" rx="10" ry="3.5" fill="#DEA5E1"/>
</g>
<g filter="url(#filter2_i_2421_9810)">
<ellipse cx="26" cy="25.5" rx="10" ry="3.5" fill="#DEA5E1"/>
</g>
<g filter="url(#filter3_if_2421_9810)">
<ellipse cx="26" cy="24.5" rx="10" ry="3.5" fill="#DEA5E1"/>
</g>
<g filter="url(#filter4_i_2421_9810)">
<ellipse cx="26" cy="20.5" rx="10" ry="3.5" fill="#DEA5E1"/>
</g>
<defs>
<filter id="filter0_i_2421_9810" x="16" y="28" width="20" height="10.4185" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.41855"/>
<feGaussianBlur stdDeviation="3.41855"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2421_9810"/>
</filter>
<filter id="filter1_if_2421_9810" x="12" y="22" width="28" height="15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.41855"/>
<feGaussianBlur stdDeviation="3.41855"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2421_9810"/>
<feGaussianBlur stdDeviation="2" result="effect2_foregroundBlur_2421_9810"/>
</filter>
<filter id="filter2_i_2421_9810" x="16" y="22" width="20" height="10.4185" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.41855"/>
<feGaussianBlur stdDeviation="3.41855"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2421_9810"/>
</filter>
<filter id="filter3_if_2421_9810" x="12" y="17" width="28" height="15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.41855"/>
<feGaussianBlur stdDeviation="3.41855"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2421_9810"/>
<feGaussianBlur stdDeviation="2" result="effect2_foregroundBlur_2421_9810"/>
</filter>
<filter id="filter4_i_2421_9810" x="16" y="17" width="20" height="10.4185" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.41855"/>
<feGaussianBlur stdDeviation="3.41855"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2421_9810"/>
</filter>
<linearGradient id="paint0_linear_2421_9810" x1="1.6484e-07" y1="0.389011" x2="52" y2="51.611" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDDCEE"/>
<stop offset="1" stop-color="#EEDDEF"/>
</linearGradient>
</defs>
</svg>
