@php
    $blockId = 'text-image-collection-' . uniqid();
@endphp

<div id="{{ $blockId }}" class="text-image-collection relative flex justify-center items-center mb-8">
    <div class="container px-5 py-8 lg:py-[62px]">
        <div class="mx-auto">
            {{-- Tagline Icons --}}
            @if(!empty($attributes['taglineIcons']))
                <div class="tagline-icons flex justify-center mb-3">
                    @foreach($attributes['taglineIcons'] as $index => $icon)
                        @if(!empty($icon['image']))
                            <div class="tagline-icon {{ $index > 0 ? '-ml-[5px]' : '' }}">
                                <img src="{{ $icon['image'] }}" alt="" class="w-7 h-7 object-contain">
                            </div>
                        @endif
                    @endforeach
                </div>
            @endif

            {{-- Title and Description --}}
            <div class="text-center mb-8 lg:mb-16">
                @if(!empty($attributes['title']))
                    <x-title variation="large" class="mb-0">{!! $attributes['title'] !!}</x-title>
                @endif

                @if(!empty($attributes['description']))
                    <p class="text-bodyMedium lg:text-bodyMediumDesktop max-w-lg mx-auto">{!! $attributes['description'] !!}</p>
                @endif
            </div>

            {{-- Collection Items --}}
            @if(!empty($attributes['items']))
                <div class="collection-items lg:max-w-[856px] lg:m-auto">
                    @foreach($attributes['items'] as $index => $item)
                        <div class="collection-item grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8 lg:mb-16 last:mb-0">
                            {{-- Image - alternating left/right based on index --}}
                            <div class="{{ $index % 2 == 0 ? 'lg:order-1' : 'lg:order-2' }}">
                                <div class="min-h-[424px] h-full rounded-3xl bg-neutral-10 overflow-hidden relative">
                                    @if(!empty($item['image']))
                                        <img src="{{ $item['image'] }}" alt="" class="absolute inset-0 w-full h-full object-cover">
                                    @else
                                        <div class="w-full h-full flex items-center justify-center bg-[#F4F6FA]">
                                            <svg class="w-16 h-16 text-neutral-30" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-5-7l-3 3.72L9 13l-3 4h12l-4-5z"/>
                                            </svg>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            {{-- Content - alternating right/left based on index --}}
                            <div class="{{ $index % 2 == 0 ? 'lg:order-2' : 'lg:order-1' }}">
                                <div class="h-full flex flex-col justify-end">
                                    {{-- Item Title --}}
                                    @if(!empty($item['title']))
                                        <x-title variation="medium" class="mb-1">{!! $item['title'] !!}</x-title>
                                    @endif

                                    {{-- Item Subtitle --}}
                                    @if(!empty($item['subtitle']))
                                        <p class="text-bodySmall mb-6 lg:text-bodySmallDesktop">{!! $item['subtitle'] !!}</p>
                                    @endif

                                    {{-- Technology Icons --}}
                                    @if(!empty($item['techIcons']))
                                        @php
                                            $techIcons = [];
                                            // Get technology icons from the central repository
                                            if (!empty($item['techIcons'])) {
                                                $allTechIcons = get_option('technology_icons', []);
                                                foreach ($item['techIcons'] as $iconId) {
                                                    foreach ($allTechIcons as $icon) {
                                                        if ($icon['id'] == $iconId) {
                                                            $techIcons[] = $icon['url'];
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                        @endphp

                                        @if(!empty($techIcons))
                                            <div class="technology-icons flex mb-5">
                                                @foreach($techIcons as $iconIndex => $iconUrl)
                                                    <div class="technology-icon {{ $iconIndex > 0 ? '-ml-[8px]' : '' }} flex items-center justify-center w-7 h-7 bg-[#E8EBF3] rounded-full overflow-hidden border border-white">
                                                        <img class="object-contain w-4 h-4" src="{{ $iconUrl }}" alt="">
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    @endif

                                    {{-- Item Description --}}
                                    @if(!empty($item['description']))
                                        <p class="text-bodySmall lg:text-bodySmallDesktop">{!! $item['description'] !!}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
