import Swiper from 'swiper';
import { Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';

document.addEventListener('DOMContentLoaded', () => {
    const caseStudiesSection = document.querySelector('.case-studies-section');

    if (!caseStudiesSection) return;

    const swiperContainer = caseStudiesSection.querySelector('.swiper');
    if (!swiperContainer) return;

    let swiperInstance = null;

    const initializeSwiper = () => {
        if (window.innerWidth >= 1024) {
            if (!swiperInstance) {
                swiperInstance = new Swiper(swiperContainer, {
                    modules: [Pagination, Autoplay],
                    cssMode: true,
                    mousewheel: true,
                    keyboard: true,
                    pagination: {
                        el: caseStudiesSection.querySelector('.swiper-pagination'),
                        clickable: true,
                    },
                    slidesPerView: 'auto',
                    spaceBetween: 24,
                    autoplay: {
                        delay: 3000,
                        pauseOnMouseEnter: true,
                    },
                });
            }
        } else if (swiperInstance) {
            swiperInstance.destroy(true, true);
            swiperInstance = null;
        }
    };

    initializeSwiper();
    window.addEventListener('resize', initializeSwiper);
});
