import { registerBlockType } from '@wordpress/blocks';
import { RichText, useBlockProps } from '@wordpress/block-editor';
import { Fragment } from '@wordpress/element';
import { commonAttributes, commonSave } from './common';

registerBlockType('sage/counter', {
    apiVersion: 2,
    title: 'Counter',
    icon: 'dashboard',
    category: 'layout',
    attributes: {
        ...commonAttributes,
    },
    edit: ({ attributes, setAttributes }) => {
        const { startNumber, endNumber, numberLabel, unit, title } = attributes;

        return (
            <Fragment>
                <div className="counter-section" {...useBlockProps()}>
                    <div className="font-sans">
                        <RichText
                            tagName="h2"
                            placeholder="Enter title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                        />
                        <div>
                            <RichText
                                tagName="h4"
                                placeholder="Start Number"
                                value={startNumber}
                                onChange={(value) => setAttributes({ startNumber: value })}
                            />
                            <RichText
                                tagName="h4"
                                placeholder="End Number"
                                value={endNumber}
                                onChange={(value) => setAttributes({ endNumber: value })}
                            />
                            <RichText
                                tagName="h5"
                                placeholder="Unit"
                                value={unit}
                                onChange={(value) => setAttributes({ unit: value })}
                                className="text-xs uppercase text-neutral-80 mb-2"
                            />
                            <RichText
                                tagName="h5"
                                placeholder="Number Label"
                                value={numberLabel}
                                onChange={(value) => setAttributes({ numberLabel: value })}
                                className="text-xs uppercase text-neutral-80 mb-2"
                            />
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: commonSave,
});
