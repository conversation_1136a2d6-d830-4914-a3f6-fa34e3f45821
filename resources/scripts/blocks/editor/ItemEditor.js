import React from 'react';
import { Button } from '@wordpress/components';

/**
 * Container for editing item details
 *
 * @param {Object} props Component props
 * @param {React.ReactNode} props.children Child components
 * @param {number} props.itemIndex Index of the current item
 * @param {Function} props.onRemove Callback for removing the item
 * @param {string} props.itemName Name of the item type (e.g., "Item", "Slide", "Logo")
 * @param {boolean} props.showRemoveButton Whether to show the remove button
 * @returns {JSX.Element} ItemEditor component
 */
const ItemEditor = ({
    children,
    itemIndex,
    onRemove,
    itemName = 'Item',
    showRemoveButton = true
}) => {
    return (
        <div className="p-4 bg-white rounded-md border border-neutral-30">
            <div className="flex justify-between items-center mb-4 pb-3 border-b border-neutral-20">
                <h4 className="text-base font-medium m-0">{itemName} {itemIndex + 1} Details</h4>
                {showRemoveButton && (
                    <button
                        className="bg-transparent border-0 p-0 text-red-600 hover:text-red-700 cursor-pointer transition-all"
                        onClick={onRemove}
                        aria-label={`Remove ${itemName.toLowerCase()}`}
                    >
                        <span className="dashicons dashicons-no-alt" style={{ fontSize: '16px' }}></span>
                    </button>
                )}
            </div>

            <div>
                {children}
            </div>
        </div>
    );
};

export default ItemEditor;
