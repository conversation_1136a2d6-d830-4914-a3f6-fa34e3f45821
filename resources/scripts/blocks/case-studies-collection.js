import { registerBlockType } from '@wordpress/blocks';
import { RichText, useBlockProps } from '@wordpress/block-editor';
import { ComboboxControl, Button } from '@wordpress/components';
import { Fragment, useState, useEffect } from '@wordpress/element';
import apiFetch from '@wordpress/api-fetch';
import { commonAttributes } from './common';
import {
    TextControl
} from './editor';

registerBlockType('sage/case-studies-collection', {
    apiVersion: 2,
    title: 'Case Studies Collection',
    icon: 'admin-post',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        featuredCaseStudyId: {
            type: 'number',
        },
        featuredCaseStudyTitle: {
            type: 'string',
        },
        featuredCaseStudyExcerpt: {
            type: 'string',
        },
        featuredCaseStudyImage: {
            type: 'string',
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const {
            title,
            description,
            featuredCaseStudyId,
            featuredCaseStudyTitle,
            featuredCaseStudyExcerpt,
            featuredCaseStudyImage,
        } = attributes;
        const blockProps = useBlockProps();
        const [inputValue, setInputValue] = useState(featuredCaseStudyTitle || '');
        const [searchResults, setSearchResults] = useState([]);
        const [isLoading, setIsLoading] = useState(false);

        useEffect(() => {
            if (inputValue) {
                setIsLoading(true);
                apiFetch({
                    path: `/wp/v2/case_study?search=${encodeURIComponent(inputValue)}&per_page=20&_embed`,
                })
                .then((caseStudies) => {
                    const options = caseStudies.map((cs) => ({
                        label: cs.title.rendered,
                        value: cs.id,
                        excerpt: cs.excerpt.rendered,
                        image:
                            cs._embedded &&
                            cs._embedded['wp:featuredmedia'] &&
                            cs._embedded['wp:featuredmedia'][0] &&
                            cs._embedded['wp:featuredmedia'][0].source_url
                                ? cs._embedded['wp:featuredmedia'][0].source_url
                                : '',
                    }));
                    setSearchResults(options);
                    setIsLoading(false);
                })
                .catch(() => {
                    setSearchResults([]);
                    setIsLoading(false);
                });
            } else {
                setSearchResults([]);
            }
        }, [inputValue]);

        useEffect(() => {
            setInputValue(featuredCaseStudyTitle || '');
        }, [featuredCaseStudyTitle]);

        return (
            <Fragment>
                <div {...blockProps}>
                    <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                        <div className="mb-4 border-b border-neutral-30">
                            <h2 className="text-2xl font-bold text-neutral-90">Case Studies Collection</h2>
                        </div>

                        <TextControl
                            label="Title"
                            value={title}
                            onChange={(value) => setAttributes({ title: value })}
                            placeholder="Enter collection title"
                            tagName="h2"
                        />

                        <TextControl
                            label="Description"
                            value={description}
                            onChange={(value) => setAttributes({ description: value })}
                            placeholder="Enter collection description"
                            tagName="p"
                            multiline={true}
                        />

                        <div className="mb-6 mt-6">
                            <div className="flex justify-between items-center mb-2">
                                <label className="block text-sm font-medium text-neutral-70">Featured Case Study</label>
                            </div>
                            <div className="p-4 bg-neutral-20 rounded-md">
                                {!featuredCaseStudyId && (
                                    <div className="p-3 bg-white rounded-md border border-neutral-30">
                                        <ComboboxControl
                                            label="Search and select a featured case study"
                                            value={inputValue}
                                            onChange={(selectedValue) => {
                                                const selectedOption = searchResults.find(
                                                    (option) => option.value === selectedValue
                                                );
                                                if (selectedOption) {
                                                    setAttributes({
                                                        featuredCaseStudyId: selectedOption.value,
                                                        featuredCaseStudyTitle: selectedOption.label,
                                                        featuredCaseStudyExcerpt: selectedOption.excerpt,
                                                        featuredCaseStudyImage: selectedOption.image,
                                                    });
                                                    setInputValue(selectedOption.label);
                                                } else {
                                                    setAttributes({
                                                        featuredCaseStudyId: null,
                                                        featuredCaseStudyTitle: '',
                                                        featuredCaseStudyExcerpt: '',
                                                        featuredCaseStudyImage: '',
                                                    });
                                                    setInputValue('');
                                                }
                                            }}
                                            onFilterValueChange={(newValue) => setInputValue(newValue)}
                                            options={searchResults}
                                            isLoading={isLoading}
                                        />
                                        <p className="mt-1 text-xs text-neutral-60">Search for a case study to feature in the collection</p>
                                    </div>
                                )}
                                {featuredCaseStudyId && (
                                    <div className="p-4 bg-white rounded-md border border-neutral-30 relative">
                                        <div className="flex justify-between items-start mb-3">
                                            <h3 className="text-base font-medium m-0">Selected Case Study</h3>
                                            <Button
                                                className="p-1 text-xs bg-red-50 border border-red-300 text-red-600 rounded"
                                                onClick={() => {
                                                    setAttributes({
                                                        featuredCaseStudyId: null,
                                                        featuredCaseStudyTitle: '',
                                                        featuredCaseStudyExcerpt: '',
                                                        featuredCaseStudyImage: '',
                                                    });
                                                    setInputValue('');
                                                }}
                                                icon="no-alt"
                                                iconSize={16}
                                                aria-label="Remove featured case study"
                                            />
                                        </div>
                                        <div className="flex flex-col md:flex-row gap-4">
                                            {featuredCaseStudyImage && (
                                                <div className="md:w-1/3">
                                                    <img
                                                        className="w-full h-auto object-cover rounded-md"
                                                        src={featuredCaseStudyImage}
                                                        alt={featuredCaseStudyTitle}
                                                    />
                                                </div>
                                            )}
                                            <div className="md:w-2/3">
                                                <h4 className="text-lg font-medium mb-2" dangerouslySetInnerHTML={{ __html: featuredCaseStudyTitle }} />
                                                <div
                                                    className="text-sm text-neutral-70"
                                                    dangerouslySetInnerHTML={{ __html: featuredCaseStudyExcerpt }}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                            <p className="mt-1 text-xs text-neutral-60">Select a featured case study to display prominently in the collection</p>
                        </div>
                    </div>
                </div>
            </Fragment>
        );
    },
    save: ({ attributes }) => {
        const blockProps = useBlockProps.save();
        return (
            <div {...blockProps}>
                <div style={{ display: 'none' }}>
                    <RichText.Content tagName="h2" value={attributes.title} />
                    <RichText.Content tagName="p" className="block-description" value={attributes.description} />
                    <RichText.Content tagName="h3" value={attributes.featuredCaseStudyTitle} />
                    <div dangerouslySetInnerHTML={{ __html: attributes.featuredCaseStudyExcerpt }} />
                    {attributes.featuredCaseStudyImage && (
                        <img src={attributes.featuredCaseStudyImage} alt={attributes.featuredCaseStudyTitle} />
                    )}
                </div>
                <div data-dynamic="sage/case-studies-collection"></div>
            </div>
        );
    },
});
