@php
    $location = 'footer_navigation';
    $locations = get_nav_menu_locations();
    $menu_id = $locations[$location] ?? null;
    $flat_menu_items = wp_get_nav_menu_items($menu_id);
    $menu_items = [];
    $menu_sections = [];

    foreach ($flat_menu_items as $item) {
        $item->children = [];
        $menu_items[$item->ID] = $item;
    }

    foreach ($menu_items as $item) {
        if ($item->menu_item_parent == 0) {
            $menu_sections[$item->ID] = $item;
        } else {
            $menu_sections[$item->menu_item_parent]->children[] = $item;
        }
    }
@endphp

<footer class="content-info flex flex-col items-center justify-center relative lg:mb-6 lg:mt-16">
    <div class="container px-5 pt-8 pb-14 lg:pt-32">
        <div class="flex flex-wrap justify-between items-center pb-8 mb-8 border-b border-neutral-40 lg:flex-nowrap">
            <x-title variation="hero" level="h2">Stay ahead by partnering with us.</x-title>
            <x-button
                type="link"
                variant="primary"
                :href="'#'">
                Let's talk
            </x-button>
        </div>
        <div class="flex flex-wrap justify-between items-start gap-16 lg:flex-nowrap">
            <div class="w-full lg:w-5/12">
                {!! display_custom_logo('w-40 mb-5') !!}
                <p class="mb-5">Tech excellence, trusted by Fortune 500 companies and global industry leaders.</p>
                <p class="text-bodyExtraSmall text-[#656B74] mb-1 lg:text-bodyExtraSmallDesktop">Signup to our Newsletter</p>
                <div class="relative">
                    <form action="#" method="POST" class="flex">
                        <input type="email" placeholder="<EMAIL>" class="px-4 pt-2.5 py-2 mr-3 border border-neutral-50 rounded-3xl w-full appearance-none placeholder:text-[#656B74]">
                        <x-button 
                            type="link"
                            variant="primary">
                            Submit
                        </x-button>
                    </form>
                </div>
            </div>

            @foreach ($menu_sections as $section)
                <div class="w-2/5 lg:w-3/12">
                    <h2 class="uppercase text-xs text-neutral-90 font-semibold tracking-wider mb-4">{{ $section->title }}</h2>
                    <ul class="space-y-1">
                        @foreach ($section->children as $child)
                        <li class="text-base font-semibold text-neutral-90">
                            <a href="{{ $child->url }}" class="flex">
                                <div class="flex items-center flex-grow gap-1">
                                    <h3 class="text-bodyExtraSmall font-normal text-[#656B74] whitespace-nowrap lg:text-bodyExtraSmallDesktop">{{ $child->title }}</h3>
                                    @if ($child->description)
                                    <p class="text-xs font-normal whitespace-nowrap pt-1 pb-0.5 px-2 ml-1 rounded-full flex items-center justify-center bg-[linear-gradient(73.87deg,#D7E6FD_2.73%,#F4F6FA_100%)] hover:bg-[linear-gradient(80.45deg,_#000000_-20%,_#003954_80%)] text-[#003954]">{!! $child->description !!}</p>
                                    @endif
                                </div>
                            </a>
                        </li>
                        @endforeach
                    </ul>
                </div>
            @endforeach
        </div>
    </div>
    <div class="w-full flex justify-center">
        <div class="container p-5 pb-8 flex gap-6 flex-col justify-between items-center lg:pb-32 lg:flex-row lg:gap-0">
            <span class="text-bodyExtraSmall text-[#656B74] lg:text-bodyExtraSmallDesktop">@ 2024 Atlantbh Sarajevo. Made with ❤️ in Sarajevo</span>
            <div class="flex space-x-4">
                <a href="#" class="text-[#a3abb9]" aria-label="Facebook">
                    <i class="fab fa-facebook-f text-xl"></i>
                </a>
                <a href="#" class="text-[#a3abb9]" aria-label="Instagram">
                    <i class="fab fa-instagram text-xl"></i>
                </a>
                <a href="#" class="text-[#a3abb9]" aria-label="X (Twitter)">
                    <i class="fab fa-x-twitter text-xl"></i>
                </a>
                <a href="#" class="text-[#a3abb9]" aria-label="YouTube">
                    <i class="fab fa-youtube text-xl"></i>
                </a>
                <a href="#" class="text-[#a3abb9]" aria-label="WordPress">
                    <i class="fab fa-wordpress text-xl"></i>
                </a>
            </div>
        </div>
    </div>
    <x-floating-bg color="gray" />
</footer>
