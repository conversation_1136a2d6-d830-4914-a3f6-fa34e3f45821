<svg width="1392" height="464" viewBox="0 0 1392 464" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1939_5178)">
<circle opacity="0.1" cx="322" cy="410" r="298" fill="#008FD3"/>
<circle opacity="0.1" cx="645" cy="322" r="298" fill="#008FD3"/>
<circle opacity="0.1" cx="958" cy="472" r="298" fill="#008FD3"/>
<circle opacity="0.1" cx="1094" cy="245" r="298" fill="#008FD3"/>
<circle opacity="0.1" cx="671" cy="599" r="298" fill="#008FD3"/>
<g clip-path="url(#clip1_1939_5178)">
<g filter="url(#filter0_b_1939_5178)">
<rect width="1392" height="464" fill="white" fill-opacity="0.5"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_b_1939_5178" x="-354" y="-354" width="2100" height="1172" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="177"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1939_5178"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1939_5178" result="shape"/>
</filter>
<clipPath id="clip0_1939_5178">
<rect width="1392" height="464" fill="white"/>
</clipPath>
<clipPath id="clip1_1939_5178">
<rect width="1392" height="464" rx="24" fill="white"/>
</clipPath>
</defs>
</svg>
