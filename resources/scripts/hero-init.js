import Swiper from 'swiper';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

document.addEventListener('DOMContentLoaded', () => {
    const heroSection = document.querySelector('.hero-section');
    if (!heroSection) return;

    const heroSwiperContainer = heroSection.querySelector('.hero-swiper');
    if (heroSwiperContainer) {
        new Swiper(heroSwiperContainer, {
            modules: [Autoplay],
            spaceBetween: 24,
            loop: true,
            autoplay: {
                delay: 5000,
            },
        });
    }

    const logosCarousel = heroSection.querySelector('.carousel-logos');
    if (logosCarousel) {
        const slides = Array.from(logosCarousel.children);
        const speed = 1;
        const direction = -1;

        slides.forEach((slide) => {
            const clone = slide.cloneNode(true);
            logosCarousel.appendChild(clone);
        });

        let translateX = 0;

        function animate() {
            translateX += speed * direction;

            if (direction === -1 && translateX <= -logosCarousel.scrollWidth / 2) {
                translateX = 0;
            } else if (direction === 1 && translateX >= 0) {
                translateX = -logosCarousel.scrollWidth / 2;
            }

            logosCarousel.style.transform = `translateX(${translateX}px)`;
            requestAnimationFrame(animate);
        }

        logosCarousel.style.display = 'flex';
        logosCarousel.style.transition = 'transform 0s linear';
        requestAnimationFrame(animate);
    }
});
