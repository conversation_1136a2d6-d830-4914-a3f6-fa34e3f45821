document.addEventListener('DOMContentLoaded', function () {
    const modal = document.getElementById('atlanter-modal')
    if (!modal) {
        return
    }
    let atlanterCache = {}
    let imageCache = {}
    const modalContent = document.getElementById('atlanter-modal-content')
    const featuredImage = document.getElementById('atlanter-featured-image')
    const postTitle = document.getElementById('atlanter-post-title')
    const postPosition = document.getElementById('atlanter-position')
    const closeBtn = document.getElementById('atlanter-modal-close')

    function fetchAtlanterData(slug) {
        if (atlanterCache[slug]) {
            return Promise.resolve(atlanterCache[slug])
        }
        return fetch(`/wp-json/wp/v2/atlanters?slug=${slug}&_embed`)
            .then(response => response.json())
            .then(data => {
                if (data && data.length > 0) {
                    atlanterCache[slug] = data[0]
                    return data[0]
                }
                return null
            })
    }

    function preloadImage(src) {
        return new Promise(resolve => {
            const img = new Image()
            img.onload = function () {
                resolve(src)
            }
            img.onerror = function () {
                resolve(null)
            }
            img.src = src
        })
    }

    function prefetchAtlanterData(slug) {
        if (atlanterCache[slug]) {
            return
        }
        fetchAtlanterData(slug).then(post => {
            if (post && post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                let imgUrl = post._embedded['wp:featuredmedia'][0].source_url
                if (imgUrl) {
                    preloadImage(imgUrl).then(loadedSrc => {
                        if (loadedSrc) {
                            imageCache[slug] = loadedSrc
                        }
                    })
                }
            }
        }).catch(() => {})
    }

    function openAtlanterModal(slug) {
        const url = new URL(window.location)
        url.searchParams.set('atlanter', slug)
        history.pushState({}, '', url)
        modalContent.innerHTML = '<p>Loading...</p>'
        featuredImage.style.display = 'none'
        if (postTitle) {
            postTitle.innerHTML = ''
        }
        if (postPosition) {
            postPosition.innerHTML = ''
        }
        fetchAtlanterData(slug).then(post => {
            if (post) {
                if (postTitle && post.title && post.title.rendered) {
                    postTitle.innerHTML = post.title.rendered
                }
                if (postPosition && post.meta && post.meta.position) {
                    postPosition.innerHTML = post.meta.position
                }
                let imgUrl = ''
                if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                    imgUrl = post._embedded['wp:featuredmedia'][0].source_url
                }
                let imagePromise = Promise.resolve(null)
                if (imgUrl) {
                    if (imageCache[slug]) {
                        imagePromise = Promise.resolve(imageCache[slug])
                    } else {
                        imagePromise = preloadImage(imgUrl)
                    }
                }
                imagePromise.then(loadedSrc => {
                    if (loadedSrc) {
                        featuredImage.src = loadedSrc
                        featuredImage.style.display = 'block'
                    }
                    modalContent.innerHTML = post.content.rendered
                    modal.classList.remove('hidden')
                    requestAnimationFrame(() => {
                        modal.classList.remove('opacity-0', 'scale-95')
                        modal.classList.add('opacity-100', 'scale-100')
                    })
                })
            } else {
                modalContent.innerHTML = '<p>No content found.</p>'
                modal.classList.remove('hidden')
                requestAnimationFrame(() => {
                    modal.classList.remove('opacity-0', 'scale-95')
                    modal.classList.add('opacity-100', 'scale-100')
                })
            }
        }).catch(err => {
            console.error(err)
            modalContent.innerHTML = '<p>Error loading content.</p>'
            modal.classList.remove('hidden')
            requestAnimationFrame(() => {
                modal.classList.remove('opacity-0', 'scale-95')
                modal.classList.add('opacity-100', 'scale-100')
            })
        })
    }

    function closeAtlanterModal() {
        modal.classList.remove('opacity-100', 'scale-100')
        modal.classList.add('opacity-0', 'scale-95')
        const url = new URL(window.location)
        url.searchParams.delete('atlanter')
        history.replaceState({}, '', url)
        setTimeout(() => {
            modal.classList.add('hidden')
        }, 300)
    }

    closeBtn.addEventListener('click', closeAtlanterModal)
    const params = new URLSearchParams(window.location.search)
    const atlanterSlug = params.get('atlanter')
    if (atlanterSlug) {
        openAtlanterModal(atlanterSlug)
    }
    document.querySelectorAll('.atlanter-card').forEach(card => {
        card.addEventListener('mouseenter', function () {
            const slug = card.getAttribute('data-slug')
            if (slug) {
                prefetchAtlanterData(slug)
            }
        })
        card.addEventListener('click', function () {
            const slug = card.getAttribute('data-slug')
            if (slug) {
                openAtlanterModal(slug)
            }
        })
    })
})
