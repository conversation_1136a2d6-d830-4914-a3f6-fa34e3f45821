import React from 'react';

/**
 * Section header for block editors
 * 
 * @param {Object} props Component props
 * @param {string} props.title Section title
 * @param {string} props.description Optional section description
 * @returns {JSX.Element} SectionHeader component
 */
const SectionHeader = ({ title, description }) => {
    return (
        <div className="mb-6 border-b border-neutral-30">
            <h3 className="text-base font-medium mb-3">{title}</h3>
            {description && (
                <p className="text-xs text-neutral-60 mb-2">{description}</p>
            )}
        </div>
    );
};

export default SectionHeader;
