@php
    $blockId = 'case-study-outcomes-' . uniqid();
@endphp

<div id="{{ $blockId }}" class="case-studies-outcome relative flex justify-center items-center mb-8">
    <x-case-study-styles :accentColor="$attributes['accentColor']" blockClass="case-studies-outcome" :blockId="$blockId" />
    <div class="container px-5 py-10">
        <div class="layout-wrapper relative max-w-3xl flex items-start gap-6 lg:gap-12">
            <div class="timeline after:w-1 after:h-full after:absolute after:left-[19px]">
                <div class="icon-container flex items-center justify-center w-[42px] h-[42px] rounded-full">
                    <img class="w-5 h-5 object-contain" src="{{ $attributes['icon'] }}" />
                </div>
            </div>
            <div>
                @if(!empty($attributes['title']))
                    <x-title class="mb-4" variation="medium">{!! $attributes['title'] !!}</x-title>
                @endif
                @if(!empty($attributes['description']))
                    <p class="mb-6">{!! $attributes['description'] !!}</p>
                @endif

                <div class="outcome-wrapper">
                    @php
                        $pvals = ! empty($attributes['percentageValues']) && is_array($attributes['percentageValues'])
                            ? $attributes['percentageValues']
                            : [];
                        $enabledPvals = array_values(array_filter($pvals, function($item) {
                            return !empty($item['enabled']);
                        }));
                        $enabledCount = count($enabledPvals);
                        $radius = 45;
                        $strokeWidth = 10;
                        $circumference = 2 * pi() * $radius;

                        // We're using 100% as the maximum for the circle display
                        // This ensures the circle makes logical sense as a percentage indicator
                    @endphp

                    @php
                        // Get outcomes from attributes
                        $outcomes = ! empty($attributes['outcomes']) && is_array($attributes['outcomes'])
                            ? $attributes['outcomes']
                            : [];

                        // Get legacy text outcomes from attributes
                        $textOutcomes = ! empty($attributes['textOutcomes']) && is_array($attributes['textOutcomes'])
                            ? $attributes['textOutcomes']
                            : [];

                        // For backward compatibility, if we have a legacy outcome and no outcomes,
                        // add the legacy outcome to the outcomes array
                        if (empty($outcomes)) {
                            // Migrate legacy percentage values
                            if (!empty($attributes['percentageValues']) && is_array($attributes['percentageValues'])) {
                                foreach ($attributes['percentageValues'] as $item) {
                                    if (!empty($item['enabled'])) {
                                        $outcomes[] = [
                                            'text' => $item['outcome'] ?? '',
                                            'showPercentage' => true,
                                            'percentage' => $item['value'] ?? 0
                                        ];
                                    }
                                }
                            }

                            // Migrate legacy text outcomes
                            foreach ($textOutcomes as $item) {
                                $outcomes[] = [
                                    'text' => $item['text'] ?? '',
                                    'showPercentage' => false,
                                    'percentage' => 0
                                ];
                            }

                            // Migrate legacy single outcome
                            if (empty($outcomes) && !empty($attributes['outcome'])) {
                                $outcomes[] = [
                                    'text' => $attributes['outcome'],
                                    'showPercentage' => false,
                                    'percentage' => 0
                                ];
                            }
                        }
                    @endphp

                    @if (!empty($outcomes))
                        @php
                            // We'll process outcomes in their original order
                            // but keep track of percentage outcomes for layout purposes
                            $currentPercentageOutcomes = [];
                        @endphp

                        {{-- Process outcomes in their original order --}}
                        @foreach ($outcomes as $index => $outcome)
                            @if (!empty($outcome['showPercentage']))
                                {{-- Add to current percentage batch --}}
                                @php $currentPercentageOutcomes[] = $outcome; @endphp

                                {{-- If this is the last outcome or the next one is a text-only outcome,
                                     render the current batch of percentage outcomes --}}
                                @if ($index === count($outcomes) - 1 ||
                                    (isset($outcomes[$index + 1]) && empty($outcomes[$index + 1]['showPercentage'])))

                                    @php
                                        $percentageCount = count($currentPercentageOutcomes);

                                        // Determine grid layout for percentage items
                                        $gridClass = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'; // Default for 3+ items
                                        if ($percentageCount === 2) {
                                            $gridClass = 'grid-cols-2'; // Always 2 columns for 2 items
                                        }
                                    @endphp

                                    <div class="outcome-container blue-bg rounded-3xl overflow-hidden p-8 mb-6">
                                        @if ($percentageCount === 1)
                                            {{-- Single percentage outcome --}}
                                            @php
                                                $percentageItem = reset($currentPercentageOutcomes);
                                                // Always use 100% as the maximum for the circle display
                                                $progress = min(($percentageItem['percentage'] / 100), 1) * $circumference;
                                                $offset = $circumference - $progress;
                                            @endphp
                                            <div class="flex items-center gap-8">
                                                <div class="percentage-item">
                                                    <svg class="progress-svg" width="112" height="112" viewBox="0 0 100 100">
                                                        <circle cx="50" cy="50" r="{{ $radius }}" fill="none" stroke="#eef3fb" stroke-width="{{ $strokeWidth }}" />
                                                        <circle cx="50" cy="50" r="{{ $radius }}" fill="none" stroke="#99D2ED" stroke-width="{{ $strokeWidth }}" stroke-linecap="round"
                                                            stroke-dasharray="{{ $circumference }}" stroke-dashoffset="{{ $offset }}" transform="rotate(-90 50 50)" />
                                                        <circle cx="50" cy="50" r="{{ $radius - ($strokeWidth / 2) }}" fill="#fff" stroke="none" />
                                                        <text x="52" y="60" text-anchor="middle" class="percentage-value font-normal pt-2 text-small lg:text-smallDesktop" fill="#000" font-size="{{ $percentageItem['percentage'] > 999 ? '8' : '10' }}">
                                                            {{ $percentageItem['percentage'] }}%
                                                        </text>
                                                    </svg>
                                                </div>
                                                @if (!empty($percentageItem['text']))
                                                    <x-title variant="small" highlight="light">{!! $percentageItem['text'] !!}</x-title>
                                                @endif
                                            </div>
                                        @else
                                            {{-- Multiple percentage outcomes --}}
                                            <div class="grid {{ $gridClass }} gap-8">
                                                @foreach ($currentPercentageOutcomes as $pIndex => $pItem)
                                                    @php
                                                        // Using grid layout now, so no need for width classes
                                                        $itemClass = 'flex flex-col items-center';

                                                        // Special handling for specific layouts
                                                        if ($percentageCount === 3) {
                                                            // For 3 items, center the items in their grid cells
                                                            $itemClass = 'flex flex-col items-center justify-self-center';
                                                        } elseif ($percentageCount === 4 && $pIndex === 3) {
                                                            // For 4 items, make the last item centered in a new row
                                                            $itemClass = 'flex flex-col items-center col-span-full justify-self-center';
                                                        }

                                                        // Calculate percentage circle
                                                        $progress = min(($pItem['percentage'] / 100), 1) * $circumference;
                                                        $offset = $circumference - $progress;
                                                    @endphp
                                                    <div class="percentage-item flex flex-col items-center gap-4 {{ $itemClass }}">
                                                        <svg class="progress-svg" width="112" height="112" viewBox="0 0 100 100">
                                                            <circle cx="50" cy="50" r="{{ $radius }}" fill="none" stroke="#eef3fb" stroke-width="{{ $strokeWidth }}" />
                                                            <circle cx="50" cy="50" r="{{ $radius }}" fill="none" stroke="#99D2ED" stroke-width="{{ $strokeWidth }}" stroke-linecap="round"
                                                                stroke-dasharray="{{ $circumference }}" stroke-dashoffset="{{ $offset }}" transform="rotate(-90 50 50)" />
                                                            <circle cx="50" cy="50" r="{{ $radius - ($strokeWidth / 2) }}" fill="#fff" stroke="none" />
                                                            <text x="52" y="60" text-anchor="middle" class="percentage-value font-normal pt-2 text-small lg:text-smallDesktop" fill="#000" font-size="{{ $pItem['percentage'] > 999 ? '8' : '10' }}">
                                                                {{ $pItem['percentage'] }}%
                                                            </text>
                                                        </svg>
                                                        @if (!empty($pItem['text']))
                                                            <x-title variant="small" highlight="light" class="outcome-text">
                                                                {!! $pItem['text'] !!}
                                                            </x-title>
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>

                                    {{-- Reset the percentage outcomes batch --}}
                                    @php $currentPercentageOutcomes = []; @endphp
                                @endif
                            @elseif (!empty($outcome['text']))
                                {{-- Text-only outcome - render in its own container --}}
                                <div class="outcome-container blue-bg rounded-3xl overflow-hidden p-8 mb-6">
                                    <x-title variant="small" highlight="light" class="outcome-text">
                                        {!! $outcome['text'] !!}
                                    </x-title>
                                </div>
                            @endif
                        @endforeach

                    {{-- Legacy rendering for backward compatibility --}}
                    @elseif (!empty($textOutcomes) || !empty($attributes['outcome']) || !empty($enabledPvals))
                        @php
                            // Create a combined array of all legacy outcomes in the correct order
                            $legacyOutcomes = [];

                            // Add percentage outcomes first
                            foreach ($enabledPvals as $pval) {
                                $legacyOutcomes[] = [
                                    'showPercentage' => true,
                                    'percentage' => $pval['value'] ?? 0,
                                    'text' => $pval['outcome'] ?? ''
                                ];
                            }

                            // Add text outcomes
                            foreach ($textOutcomes as $textOutcome) {
                                if (!empty($textOutcome['text'])) {
                                    $legacyOutcomes[] = [
                                        'showPercentage' => false,
                                        'percentage' => 0,
                                        'text' => $textOutcome['text']
                                    ];
                                }
                            }

                            // Add single legacy outcome if it exists and no other outcomes
                            if (empty($legacyOutcomes) && !empty($attributes['outcome'])) {
                                $legacyOutcomes[] = [
                                    'showPercentage' => false,
                                    'percentage' => 0,
                                    'text' => $attributes['outcome']
                                ];
                            }

                            // Track percentage outcomes for layout
                            $currentPercentageOutcomes = [];
                        @endphp

                        {{-- Process legacy outcomes in their original order --}}
                        @foreach ($legacyOutcomes as $index => $outcome)
                            @if (!empty($outcome['showPercentage']))
                                {{-- Add to current percentage batch --}}
                                @php $currentPercentageOutcomes[] = $outcome; @endphp

                                {{-- If this is the last outcome or the next one is a text-only outcome,
                                     render the current batch of percentage outcomes --}}
                                @if ($index === count($legacyOutcomes) - 1 ||
                                    (isset($legacyOutcomes[$index + 1]) && empty($legacyOutcomes[$index + 1]['showPercentage'])))

                                    @php
                                        $percentageCount = count($currentPercentageOutcomes);

                                        // Determine grid layout for percentage items
                                        $gridClass = 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'; // Default for 3+ items
                                        if ($percentageCount === 2) {
                                            $gridClass = 'grid-cols-2'; // Always 2 columns for 2 items
                                        }
                                    @endphp

                                    <div class="outcome-container blue-bg rounded-3xl overflow-hidden p-8 mb-6">
                                        @if ($percentageCount === 1)
                                            {{-- Single percentage outcome --}}
                                            @php
                                                $percentageItem = reset($currentPercentageOutcomes);
                                                // Always use 100% as the maximum for the circle display
                                                $progress = min(($percentageItem['percentage'] / 100), 1) * $circumference;
                                                $offset = $circumference - $progress;
                                            @endphp
                                            <div class="flex items-center gap-8">
                                                <div class="percentage-item">
                                                    <svg class="progress-svg" width="112" height="112" viewBox="0 0 100 100">
                                                        <circle cx="50" cy="50" r="{{ $radius }}" fill="none" stroke="#eef3fb" stroke-width="{{ $strokeWidth }}" />
                                                        <circle cx="50" cy="50" r="{{ $radius }}" fill="none" stroke="#99D2ED" stroke-width="{{ $strokeWidth }}" stroke-linecap="round"
                                                            stroke-dasharray="{{ $circumference }}" stroke-dashoffset="{{ $offset }}" transform="rotate(-90 50 50)" />
                                                        <circle cx="50" cy="50" r="{{ $radius - ($strokeWidth / 2) }}" fill="#fff" stroke="none" />
                                                        <text x="52" y="60" text-anchor="middle" class="percentage-value font-normal pt-2 text-small lg:text-smallDesktop" fill="#000" font-size="{{ $percentageItem['percentage'] > 999 ? '8' : '10' }}">
                                                            {{ $percentageItem['percentage'] }}%
                                                        </text>
                                                    </svg>
                                                </div>
                                                @if (!empty($percentageItem['text']))
                                                    <x-title variant="small" highlight="light">{!! $percentageItem['text'] !!}</x-title>
                                                @endif
                                            </div>
                                        @else
                                            {{-- Multiple percentage outcomes --}}
                                            <div class="grid {{ $gridClass }} gap-8">
                                                @foreach ($currentPercentageOutcomes as $pIndex => $pItem)
                                                    @php
                                                        // Using grid layout now, so no need for width classes
                                                        $itemClass = 'flex flex-col items-center';

                                                        // Special handling for specific layouts
                                                        if ($percentageCount === 3) {
                                                            // For 3 items, center the items in their grid cells
                                                            $itemClass = 'flex flex-col items-center justify-self-center';
                                                        } elseif ($percentageCount === 4 && $pIndex === 3) {
                                                            // For 4 items, make the last item centered in a new row
                                                            $itemClass = 'flex flex-col items-center col-span-full justify-self-center';
                                                        }

                                                        // Calculate percentage circle
                                                        $progress = min(($pItem['percentage'] / 100), 1) * $circumference;
                                                        $offset = $circumference - $progress;
                                                    @endphp
                                                    <div class="percentage-item flex flex-col items-center gap-4 {{ $itemClass }}">
                                                        <svg class="progress-svg" width="112" height="112" viewBox="0 0 100 100">
                                                            <circle cx="50" cy="50" r="{{ $radius }}" fill="none" stroke="#eef3fb" stroke-width="{{ $strokeWidth }}" />
                                                            <circle cx="50" cy="50" r="{{ $radius }}" fill="none" stroke="#99D2ED" stroke-width="{{ $strokeWidth }}" stroke-linecap="round"
                                                                stroke-dasharray="{{ $circumference }}" stroke-dashoffset="{{ $offset }}" transform="rotate(-90 50 50)" />
                                                            <circle cx="50" cy="50" r="{{ $radius - ($strokeWidth / 2) }}" fill="#fff" stroke="none" />
                                                            <text x="52" y="60" text-anchor="middle" class="percentage-value font-normal pt-2 text-small lg:text-smallDesktop" fill="#000" font-size="{{ $pItem['percentage'] > 999 ? '8' : '10' }}">
                                                                {{ $pItem['percentage'] }}%
                                                            </text>
                                                        </svg>
                                                        @if (!empty($pItem['text']))
                                                            <x-title variant="small" highlight="light" class="outcome-text">
                                                                {!! $pItem['text'] !!}
                                                            </x-title>
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>

                                    {{-- Reset the percentage outcomes batch --}}
                                    @php $currentPercentageOutcomes = []; @endphp
                                @endif
                            @elseif (!empty($outcome['text']))
                                {{-- Text-only outcome - render in its own container --}}
                                <div class="outcome-container blue-bg rounded-3xl overflow-hidden p-8 mb-6">
                                    <x-title variant="small" highlight="light" class="outcome-text">
                                        {!! $outcome['text'] !!}
                                    </x-title>
                                </div>
                            @endif
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
