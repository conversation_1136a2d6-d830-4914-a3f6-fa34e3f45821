<div class="slider-text-section relative flex justify-center items-center">
    <div class="container px-5 py-10">
        <div class="flex flex-col">
            <div class="flex flex-wrap mb-6 gray after:w-full after:h-px after:lg:my-14 lg:mb-0">
                <div class="flex flex-col lg:flex-row">
                    <div class="flex flex-col lg:w-1/3 lg:pr-10">
                        @if(!empty($attributes['title']))
                            <x-title class="mb-6" variation="large">{!! $attributes['title'] !!}</x-title>
                        @endif
                        @if(!empty($attributes['sliders']))
                            @php
                                $loopSlides = collect($attributes['sliders'])->concat($attributes['sliders']);
                            @endphp
                            <div class="slider swiper-container w-full mb-6 lg:mb-0" style="mask:linear-gradient(to right,rgba(0,0,0,0) 1%,rgba(0,0,0,1) 20%,rgba(0,0,0,1) 80%,rgba(0,0,0,0) 99%)">
                                <div class="swiper-wrapper !ease-linear lg:justify-between">
                                    @foreach($loopSlides as $slide)
                                        @if(!empty($slide['image']))
                                            <div class="swiper-slide !w-auto flex h-12 items-center justify-center lg:max-w-fit">
                                                <img class="w-full h-full max-w-24 object-contain" src="{!! $slide['image'] !!}" />
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                    @if(!empty($attributes['description']))
                        <p class="text-bodyLarge lg:text-bodyLargeDesktop lg:w-2/3">{!! $attributes['description'] !!}</p>
                    @endif
                </div>
            </div>
            @if (!empty($attributes['boxes']))
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($attributes['boxes'] as $box)
                        @php
                            $isLastOdd = ($loop->last && (count($attributes['boxes']) % 2 !== 0));
                        @endphp
                        <div class="border border-[#E8EBF3] p-5 rounded-3xl {{ $isLastOdd ? 'md:col-span-2' : 'md:col-span-1' }}">
                            <img class="mb-4 w-10 h-10" src="{!! $box['image'] !!}" />
                            <p class="box-content">{!! $box['content'] !!}</p>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
