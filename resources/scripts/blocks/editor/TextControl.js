import React from 'react';
import { RichText } from '@wordpress/block-editor';

/**
 * Text control component for editable text fields
 * 
 * @param {Object} props Component props
 * @param {string} props.label Label for the field
 * @param {string} props.value Current value
 * @param {Function} props.onChange Callback for value change
 * @param {string} props.placeholder Placeholder text
 * @param {string} props.tagName HTML tag name
 * @param {string} props.description Description text
 * @param {boolean} props.multiline Whether the field should be multiline
 * @returns {JSX.Element} TextControl component
 */
const TextControl = ({ 
    label, 
    value, 
    onChange, 
    placeholder = 'Enter text', 
    tagName = 'p',
    description,
    multiline = false
}) => {
    return (
        <div className="mb-4">
            <label className="block text-sm font-medium text-neutral-70 mb-2">{label}</label>
            <RichText
                tagName={tagName}
                placeholder={placeholder}
                value={value}
                onChange={onChange}
                className={`p-3 bg-white rounded-md focus:border-primary-70 focus:ring-1 focus:ring-primary-70 outline-none text-base w-auto ${multiline ? 'min-h-[100px]' : ''}`}
                style={{ border: '1px solid #E2E8F0' }}
            />
            {description && (
                <p className="mt-2 text-xs text-neutral-60">{description}</p>
            )}
        </div>
    );
};

export default TextControl;
