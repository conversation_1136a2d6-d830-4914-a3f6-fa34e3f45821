.menu-item.menu-item-depth-1.menu-item-custom {
    pointer-events: none;
}

.menu-item.menu-item-depth-1.menu-item-custom .item-controls {
    display: none;
}

.menu-item.menu-item-depth-1.menu-item-custom .is-submenu {
    display: none;
}

.menu-item.menu-item-depth-1.menu-item-custom .item-title {
    margin-right: 0;
    display: flex;
    justify-content: space-between;
}

.menu-item.menu-item-depth-1.menu-item-custom .item-title::after {
    content: 'Section';
    font-weight: 300;
    font-style: italic;
    margin-left: 4px;
    font-size: 12px;
    opacity: 0.5;
}

.menu-item.menu-item-depth-1.menu-item-custom .menu-item-handle {
    background: white;
    border: 0;
    border-bottom: 2px solid #3b3b3b;
    color: black;
    padding: 2px 0;
}

.menu-item .field-move-combo.description-group {
    display: none;
}