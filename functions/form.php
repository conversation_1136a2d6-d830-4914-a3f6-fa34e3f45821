<?php
add_action('phpmailer_init', function($phpmailer) {
    $phpmailer->isSMTP();
    $phpmailer->Host = 'email-smtp.us-east-1.amazonaws.com';
    $phpmailer->SMTPAuth = true;
    $phpmailer->Port = 587;
    $phpmailer->Username = 'AKIA5U6POP6VLTDBU3F3';
    $phpmailer->Password = 'BLIpthFcEdBVCcPJYAFkz4zFRMFHfwC71V3g2nO4634w';
    $phpmailer->SMTPSecure = 'tls';
    $phpmailer->From = '<EMAIL>';
    $phpmailer->FromName = 'AtlantBH Staging';
});

add_action('rest_api_init', function () {
    register_rest_route('sage', '/send-contact-form', [
        'methods' => 'POST',
        'callback' => 'send_contact_form',
        'permission_callback' => '__return_true'
    ]);
});

function send_contact_form(WP_REST_Request $request) {
    $data = $request->get_json_params();

    $name       = sanitize_text_field($data['name']);
    $email      = sanitize_email($data['email']);
    $message    = sanitize_textarea_field($data['message']);
    $categories = implode(', ', array_map('sanitize_text_field', $data['categories']));

    $to_email = sanitize_email($data['submissionEmail'] ?? get_option('contact_form_email', '<EMAIL>'));

    $subject = 'New Contact Form Submission';
    $body    = "Name: $name\nEmail: $email\nCategories: $categories\n\nMessage:\n$message";
    $headers = ['Content-Type: text/plain; charset=UTF-8'];

    if (wp_mail($to_email, $subject, $body, $headers)) {
        return new WP_REST_Response(['message' => 'Message sent successfully.'], 200);
    } else {
        global $phpmailer;
        error_log('Mailer Error: ' . $phpmailer->ErrorInfo);
        return new WP_REST_Response(['message' => 'Message failed to send.'], 500);
    }
}

add_action('rest_api_init', function () {
    register_rest_route('sage', '/send-cta-file', [
        'methods'             => 'POST',
        'callback'            => 'send_cta_file',
        'permission_callback' => '__return_true',
    ]);
});

function send_cta_file(\WP_REST_Request $request) {
    $data    = $request->get_json_params();
    $email   = sanitize_email($data['email']);
    $fileUrl = esc_url_raw($data['fileUrl']);

    if (empty($email) || empty($fileUrl)) {
        return new WP_REST_Response(['message' => 'Missing email or file URL.'], 400);
    }

    $upload_dir    = wp_upload_dir();
    $file_name     = basename($fileUrl);
    $temp_path     = $upload_dir['path'] . '/' . wp_unique_filename($upload_dir['path'], $file_name);
    $file_contents = @file_get_contents($fileUrl);

    if (!$file_contents) {
        return new WP_REST_Response(['message' => 'Unable to download the file.'], 500);
    }

    file_put_contents($temp_path, $file_contents);

    $subject = 'Your Requested File';
    $body    = "<p>Please find your file attached.</p>";
    $headers = ['Content-Type: text/html; charset=UTF-8'];
    $attachments = [$temp_path];

    $sent = wp_mail($email, $subject, $body, $headers, $attachments);
    
    @unlink($temp_path);

    if ($sent) {
        return new WP_REST_Response(['message' => 'File was sent successfully.'], 200);
    } else {
        return new WP_REST_Response(['message' => 'Error sending file.'], 500);
    }
}
