import Swiper from 'swiper';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';

document.addEventListener('DOMContentLoaded', () => {
    const testimonialsSection = document.querySelector('.testimonials-section');
    if (!testimonialsSection) return;

    const swiperContainer = testimonialsSection.querySelector('.swiper');
    const prevButton = testimonialsSection.querySelector('.swiper-button-prev');
    const nextButton = testimonialsSection.querySelector('.swiper-button-next');
    const swiperWrapper = testimonialsSection.querySelector('.swiper-wrapper');
    const slides = testimonialsSection.querySelectorAll('.swiper-slide');
    
    if (!swiperContainer || !swiperWrapper || !slides.length) return;
    
    const originalSlides = Array.from(slides);
    const slideCount = originalSlides.length;
    
    swiperWrapper.innerHTML = '';
    
    for (let i = 0; i < slideCount * 3; i++) {
        const originalIndex = i % slideCount;
        const clonedSlide = originalSlides[originalIndex].cloneNode(true);
        clonedSlide.setAttribute('data-swiper-slide-index', originalIndex);
        // Mark duplicates for clarity
        if (i >= slideCount) {
            clonedSlide.classList.add('swiper-slide-duplicate');
        }
        swiperWrapper.appendChild(clonedSlide);
    }
    
    const swiper = new Swiper(swiperContainer, {
        modules: [Navigation],
        navigation: {
            nextEl: nextButton,
            prevEl: prevButton,
        },
        slidesPerView: 'auto',
        spaceBetween: 24,
        slidesPerGroup: 1,
        grabCursor: true,
        speed: 300,
        loop: true,
        loopAdditionalSlides: 3,
        watchSlidesProgress: true,
        watchOverflow: true
    });
    
    if (prevButton && nextButton) {
        nextButton.addEventListener('click', () => {
            if (swiper.isEnd) {
                swiper.slideTo(0, 0);
            }
        });
        
        prevButton.addEventListener('click', () => {
            if (swiper.isBeginning) {
                swiper.slideTo(swiper.slides.length - 1, 0);
            }
        });
    }
});