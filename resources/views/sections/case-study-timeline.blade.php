@php
    $blockId = 'case-study-timeline-' . uniqid();
@endphp

<div id="{{ $blockId }}" class="case-studies-timeline relative flex justify-center items-center mb-8">
    <x-case-study-styles :accentColor="$attributes['accentColor']" blockClass="case-studies-timeline" :blockId="$blockId" />
    <div class="container px-5 py-10">
        <div class="relative max-w-3xl flex items-start gap-6 lg:gap-12">
            <div class="timeline after:w-1 after:h-full after:absolute after:left-[19px]">
                <div class="icon-container flex items-center justify-center w-[42px] h-[42px] rounded-full">
                    <img class="w-5 h-5 object-contain" src="{{ $attributes['icon'] }}" />
                </div>
            </div>
            <div>
                @if(!empty($attributes['title']))
                    <x-title class="mb-4" variation="medium">{!! $attributes['title'] !!}</x-title>
                @endif
                @if(!empty($attributes['description']))
                    <p class="mb-6">{!! $attributes['description'] !!}</p>
                @endif

                @if (!empty($attributes['timeline']))
                    <div class="hidden md:block px-8 py-4 mb-8 rounded-3xl overflow-hidden bg-gradient-to-r from-[#EEE8F3] to-[#E8EAF4]">
                        <div class="relative w-full h-20 before:absolute before:top-1/2 before:-translate-y-1/2 before:-mt-1 before:w-full before:h-0.5 before:bg-gradient-to-r before:from-[#AA95F4] before:to-[#95B8F4]">
                            @foreach ($attributes['timeline'] as $item)
                                @php
                                    if ($loop->first) {
                                        $translateClass = '-translate-y-1/2 translate-x-0 items-start';
                                    } elseif ($loop->last) {
                                        $translateClass = '-translate-y-1/2 -translate-x-full items-end';
                                    } else {
                                        $translateClass = '-translate-y-1/2 -translate-x-1/2 items-center';
                                    }
                                @endphp
                                <div class="absolute flex flex-col top-1/2 {{ $translateClass }}" style="left: {!! $item['position'] !!}%;">
                                    <span class="text-bodyExtraSmall mb-1 font-medium whitespace-nowrap uppercase text-[#303945]">
                                        {!! $item['event'] !!}
                                    </span>
                                    <div class="w-5 h-5 bg-white rounded-full mb-1"></div>
                                    <span class="text-bodyExtraSmall mt-1 font-light whitespace-nowrap text-[#3F4A5A] lg:text-bodyExtraSmallDesktop">
                                        {!! $item['date'] !!}
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="block md:hidden px-4 pt-7 pb-12 mb-8 rounded-3xl overflow-hidden bg-gradient-to-r from-[#EEE8F3] to-[#E8EAF4]">
                        <div class="relative h-96">
                            <div class="absolute inset-x-0 top-0 bottom-0 flex justify-center">
                                <div class="w-0.5 relative top-4 bg-gradient-to-b from-[#AA95F4] to-[#95B8F4]"></div>
                            </div>
                            @foreach ($attributes['timeline'] as $item)
                                <div class="absolute w-full flex items-center" style="top: {!! $item['position'] !!}%;">
                                    <div class="flex-1 text-right pr-5">
                                        <span class="text-bodyExtraSmall font-medium whitespace-nowrap uppercase text-[#303945]">
                                            {!! $item['event'] !!}
                                        </span>
                                    </div>
                                    <div class="flex-none mx-2">
                                        <div class="w-5 h-5 bg-white rounded-full"></div>
                                    </div>
                                    <div class="flex-1 text-left pl-5">
                                        <span class="text-bodyExtraSmall font-light whitespace-nowrap text-[#3F4A5A]">
                                            {!! $item['date'] !!}
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                @if (!empty($attributes['positions']))
                    @php
                        $bgColors = [
                            'bg-[#EEE0E7]',
                            'bg-[#E0EDF1]',
                            'bg-[#EFE3E5]',
                            'bg-[#E1E7F3]',
                            'bg-[#E2F1E9]',
                            'bg-[#EBEEE2]',
                        ];
                        $textColors = [
                            'text-[#5A1321]',
                            'text-[#00567F]',
                            'text-[#5A1321]',
                            'text-[#00567F]',
                            'text-[#375843]',
                            'text-[#375843]',
                        ];

                        // Get positions from the central repository
                        $allPositions = get_option('positions', []);

                        // Debug output if needed
                        if (isset($_GET['debug_positions'])) {
                            echo '<pre>All Positions: ' . json_encode($allPositions, JSON_PRETTY_PRINT) . '</pre>';
                            echo '<pre>Block Positions: ' . json_encode($attributes['positions'], JSON_PRETTY_PRINT) . '</pre>';
                        }
                    @endphp
                    <div class="positions grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                        @foreach ($attributes['positions'] as $position)
                            @php
                                // For all existing positions, assume they are manual positions (positionId = 0)
                                // unless explicitly marked as coming from the repository
                                $positionId = 0; // Default to manual position

                                // Only try to extract positionId if it's explicitly set
                                // First check for positionId in the position array
                                if (isset($position['positionId']) && $position['positionId'] > 0) {
                                    $positionId = intval($position['positionId']);
                                }
                                // Check for content in a span with class position-id
                                elseif (isset($position['content']) && preg_match('/<span class="position-id">(\d+)<\/span>/', $position['content'], $matches) && intval($matches[1]) > 0) {
                                    $positionId = intval($matches[1]);
                                }
                                // Then check for data-position-id attribute
                                elseif (isset($position['data-position-id']) && intval($position['data-position-id']) > 0) {
                                    $positionId = intval($position['data-position-id']);
                                }
                                // Check for data attributes in the position array
                                elseif (isset($position['attrs']) && isset($position['attrs']['data-position-id']) && intval($position['attrs']['data-position-id']) > 0) {
                                    $positionId = intval($position['attrs']['data-position-id']);
                                }
                                // Check for data-position-id in the HTML content
                                elseif (isset($position['rendered']) && preg_match('/data-position-id="(\d+)"/', $position['rendered'], $matches) && intval($matches[1]) > 0) {
                                    $positionId = intval($matches[1]);
                                }

                                // If positionId is 0 or not found, treat as a manual position

                                // Debug output if needed
                                if (isset($_GET['debug_positions'])) {
                                    echo '<pre>Position data: ' . json_encode($position, JSON_PRETTY_PRINT) . '</pre>';
                                    echo '<pre>Position ID: ' . $positionId . '</pre>';
                                }
                                $positionData = $position;

                                // If it's from the repository, get the latest data
                                if ($positionId > 0) {
                                    foreach ($allPositions as $repoPosition) {
                                        if ($repoPosition['id'] == $positionId) {
                                            $positionData = [
                                                'text' => $repoPosition['name'],
                                                'icon' => $repoPosition['url']
                                            ];
                                            break;
                                        }
                                    }
                                }
                            @endphp
                            <div class="flex gap-2 items-center {{ $bgColors[$loop->index % count($bgColors)] }} {{ $textColors[$loop->index % count($textColors)] }} py-4 px-5 rounded-2xl">
                                @if (!empty($positionData['icon']))
                                    <img src="{!! $positionData['icon'] !!}" alt="Position Icon" />
                                @endif
                                <span class="text-bodySmall mt-1 lg:text-bodySmallDesktop">{!! $positionData['text'] !!}</span>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
