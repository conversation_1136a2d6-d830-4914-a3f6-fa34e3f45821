import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ImageUploader
} from './editor';

registerBlockType('sage/text-banner', {
    apiVersion: 2,
    title: 'Text Banner',
    icon: 'format-image',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        textContent: {
            type: 'string',
            default: '',
        },
        imageUrl: {
            type: 'string',
            default: '',
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const { textContent, imageUrl } = attributes;
        const blockProps = useBlockProps();

        return (
            <div {...blockProps}>
                <div className="font-sans m-6 p-6 bg-neutral-10 rounded-xl shadow-sm border border-neutral-30">
                    <div className="mb-4 border-b border-neutral-30">
                        <h2 className="text-2xl font-bold text-neutral-90">Text Banner</h2>
                    </div>

                    <TextControl
                        label="Banner Text"
                        value={textContent}
                        onChange={(value) => setAttributes({ textContent: value })}
                        placeholder="Enter banner text"
                        tagName="p"
                        multiline={true}
                    />

                    <ImageUploader
                        image={imageUrl}
                        onSelect={(url) => setAttributes({ imageUrl: url })}
                        onRemove={() => setAttributes({ imageUrl: '' })}
                        label="Banner Image"
                        description="Upload an image for the text banner."
                        height={48}
                        objectFit="cover"
                        altText="Text Banner"
                    />
                </div>
            </div>
        );
    },
    save: commonSave,
});
