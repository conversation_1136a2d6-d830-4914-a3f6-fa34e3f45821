import Swiper from 'swiper';
import { Autoplay, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import JustValidate from 'just-validate';

document.addEventListener('DOMContentLoaded', function () {
    const contactFormSection = document.querySelector('.contact-form-section');
    if (contactFormSection) {
        const swiperContainer = contactFormSection.querySelector('.swiper-container');
        if (swiperContainer) {
            const swiper = new Swiper(swiperContainer, {
                modules: [Autoplay, Pagination],
                loop: true,
                autoplay: { delay: 5000, disableOnInteraction: false },
                pagination: { el: swiperContainer.querySelector('.swiper-pagination'), clickable: true },
                spaceBetween: 0
            });
        }
        const form = document.querySelector('#contact-form');
        if (form) {
            let submitAttempted = false;
            form.addEventListener('submit', function () {
                submitAttempted = true;
            });
            const categories = [];
            contactFormSection.querySelectorAll('.pill').forEach((pill) => {
                const category = pill.dataset.category;
                const closeIcon = pill.querySelector('.close-icon');
                const text = pill.querySelector('.pill-text');
                closeIcon.classList.add('w-0', 'opacity-0', '-mr-1');
                pill.addEventListener('click', (event) => {
                    event.preventDefault();
                    if (pill.classList.contains('selected')) {
                        pill.classList.remove('selected', 'bg-primary-20');
                        pill.style.removeProperty('border-color');
                        text.style.removeProperty('color');
                        closeIcon.classList.add('w-0', 'opacity-0', '-mr-1');
                        closeIcon.classList.remove('w-4', 'opacity-100', 'mr-0');
                        const index = categories.indexOf(category);
                        if (index > -1) categories.splice(index, 1);
                    } else {
                        pill.classList.add('selected', 'bg-primary-20');
                        pill.style.borderColor = '#91D1F1';
                        text.style.color = '#00567F';
                        closeIcon.classList.remove('w-0', 'opacity-0', '-mr-1');
                        closeIcon.classList.add('w-4', 'opacity-100', 'mr-0');
                        categories.push(category);
                    }
                });
            });
            let messageWrapper = null;
            const textarea = form.querySelector('textarea[name="message"]');
            if (textarea) {
                const maxLength = textarea.getAttribute('maxlength');
                if (maxLength) {
                    messageWrapper = document.createElement('div');
                    messageWrapper.className = 'flex flex-row-reverse items-center justify-between';
                    const counter = document.createElement('span');
                    counter.className = 'text-[#6E7B91]';
                    counter.textContent = `0/${maxLength}`;
                    messageWrapper.appendChild(counter);
                    textarea.insertAdjacentElement('afterend', messageWrapper);
                    textarea.addEventListener('input', () => {
                        counter.textContent = `${textarea.value.length}/${maxLength}`;
                    });
                }
            }
            const formContainer = form.parentElement;
            const overlay = formContainer.querySelector('#form-overlay');
            const overlayImg = overlay.querySelector('#overlay-img');
            const overlayTitle = overlay.querySelector('#overlay-title');
            const overlaySubtitle = overlay.querySelector('#overlay-subtitle');
            const overlayCloseBtn = overlay.querySelector('#overlay-close');
            const countdownEl = overlay.querySelector('#countdown');
            const staticTitle = formContainer.querySelector('#static-title');
            const closeOverlay = () => {
                overlay.style.opacity = '0';
                setTimeout(() => {
                    overlay.classList.add('hidden');
                    form.style.opacity = '1';
                    if (staticTitle) {
                        staticTitle.style.opacity = '1';
                    }
                }, 300);
            };
            const showOverlay = (type) => {
                form.style.opacity = '0';
                if (staticTitle) {
                    staticTitle.style.opacity = '0';
                }
                let baseText = '';
                if (type === 'success') {
                    overlayImg.src = formContainer.dataset.successImg;
                    overlayTitle.textContent = 'Thank you for reaching out to us!';
                    baseText = 'We’ll get back to you soon. This window will close automatically in ';
                } else {
                    overlayImg.src = formContainer.dataset.errorImg;
                    overlayTitle.textContent = 'Oops! We couldn’t receive your message.';
                    baseText = 'Please try again. This window will close automatically in ';
                }
                let countdownValue = 5;
                countdownEl.textContent = countdownValue + (countdownValue === 1 ? " second" : " seconds");
                overlaySubtitle.innerHTML = baseText + `<span id="countdown">${countdownEl.textContent}</span>.`;
                overlay.classList.remove('hidden');
                overlay.style.opacity = '0';
                setTimeout(() => {
                    overlay.style.opacity = '1';
                    const interval = setInterval(() => {
                        countdownValue--;
                        if (countdownValue > 0) {
                            countdownEl.textContent = countdownValue + (countdownValue === 1 ? " second" : " seconds");
                            overlaySubtitle.innerHTML = baseText + `<span id="countdown">${countdownEl.textContent}</span>.`;
                        } else {
                            clearInterval(interval);
                            closeOverlay();
                        }
                    }, 1000);
                    overlayCloseBtn.addEventListener('click', () => {
                        clearInterval(interval);
                        closeOverlay();
                    }, { once: true });
                }, 300);
            };
            const validation = new JustValidate('#contact-form', {
                errorFieldCssClass: '!border-[#B33620] !text-[#B33620] !bg-[#FCF9F9E5] !placeholder-[#B33620]',
                errorLabelCssClass: '!text-[#B33620]'
            });
            validation
                .addField('input[name="name"]', [
                    { rule: 'required', errorMessage: 'Name is required.' },
                    { rule: 'minLength', value: 3, errorMessage: 'Name must be at least 3 characters' }
                ])
                .addField('input[name="email"]', [
                    { rule: 'required', errorMessage: 'Email is required.' },
                    { rule: 'email', errorMessage: 'Please enter a valid email.' }
                ])
                .addField('textarea[name="message"]', [
                    { rule: 'required', errorMessage: 'Message is required.' },
                    { rule: 'minLength', value: 10, errorMessage: 'Please enter a message at least 10 characters long.' }
                ], {
                    errorsContainer: messageWrapper
                })
                .onSuccess((event) => {
                    event.preventDefault();
                    const submitBtn = form.querySelector('[type="submit"]');
                    submitBtn.classList.add('spinning');
                    const formData = new FormData(form);
                    const data = {
                        name: formData.get('name'),
                        email: formData.get('email'),
                        message: formData.get('message'),
                        categories
                    };
                    fetch('/wp-json/sage/send-contact-form', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    })
                        .then(response => response.json())
                        .then(result => {
                            submitBtn.classList.remove('spinning');
                            if (result.message && result.message.toLowerCase().includes('successfully')) {
                                showOverlay('success');
                                form.reset();
                                categories.length = 0;
                                if (textarea && messageWrapper) {
                                    const maxLen = textarea.getAttribute('maxlength');
                                    if (maxLen) {
                                        const counter = messageWrapper.querySelector('span');
                                        if (counter) {
                                            counter.textContent = `0/${maxLen}`;
                                        }
                                    }
                                }
                                contactFormSection.querySelectorAll('.pill').forEach(pill => {
                                    const text = pill.querySelector('.pill-text');
                                    pill.style.removeProperty('border-color');
                                    pill.classList.remove('selected', 'bg-primary-20');
                                    text.style.removeProperty('color');
                                    const closeIcon = pill.querySelector('.close-icon');
                                    closeIcon.classList.add('w-0', 'opacity-0', '-mr-1');
                                    closeIcon.classList.remove('w-4', 'opacity-100', 'mr-0');
                                });
                            } else {
                                showOverlay('error');
                            }
                        })
                        .catch(error => {
                            submitBtn.classList.remove('spinning');
                            console.error('Error submitting the form:', error);
                            showOverlay('error');
                        });
                });
            validation.onValidate(({ fields }) => {
                if (!submitAttempted) return;
                requestAnimationFrame(() => {
                    Object.keys(fields).forEach((selector) => {
                        const fieldObj = fields[selector];
                        const input = fieldObj.elem;
                        const container = input.closest('div');
                        const label = container ? container.querySelector('label') : null;
                        if (label) {
                            if (fieldObj.isValid) {
                                label.classList.remove('!text-[#B33620]');
                            } else {
                                label.classList.add('!text-[#B33620]');
                            }
                        }
                    });
                });
            });
        }
    }
});
