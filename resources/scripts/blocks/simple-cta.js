import { registerBlockType } from '@wordpress/blocks';
import { useBlockProps } from '@wordpress/block-editor';
import { commonAttributes, commonSave } from './common';
import {
    TextControl,
    ButtonEditor,
    EditorContainer
} from './editor';

registerBlockType('sage/simple-cta', {
    apiVersion: 2,
    title: 'Simple CTA',
    icon: 'button',
    category: 'layout',
    attributes: {
        ...commonAttributes,
        title: {
            type: 'string',
            default: '',
        },
        buttonLink: {
            type: 'string',
            default: '',
        },
    },
    edit: ({ attributes, setAttributes }) => {
        const blockProps = useBlockProps();
        const { title, buttonLink } = attributes;

        return (
            <div {...blockProps}>
                <EditorContainer blockTitle="Simple CTA">
                    <TextControl
                        label="Title"
                        value={title}
                        onChange={(value) => setAttributes({ title: value })}
                        placeholder="Enter CTA title"
                        tagName="h2"
                    />

                    <div className="mb-4">
                        <label className="block text-sm font-medium text-neutral-70 mb-2">Link URL</label>
                        <div className="flex items-center gap-2 relative">
                            <div className="flex-grow relative">
                                <input 
                                    type="text" 
                                    value={buttonLink || ''}
                                    onChange={(e) => setAttributes({ buttonLink: e.target.value })}
                                    placeholder="https://example.com"
                                    className="p-3 bg-white rounded-md focus:border-primary-70 focus:ring-1 focus:ring-primary-70 outline-none text-base block w-full" 
                                    style={{ border: '1px solid #E2E8F0' }}
                                />
                                {buttonLink && (
                                    <button 
                                        onClick={() => setAttributes({ buttonLink: '' })}
                                        className="absolute right-3 pt-1 cursor-pointer border-0 bg-transparent top-1/2 -translate-y-1/2 text-neutral-50 hover:text-neutral-70 transition-colors"
                                        title="Clear URL"
                                    >
                                        <span className="dashicons dashicons-no-alt" style={{ fontSize: '16px' }}></span>
                                    </button>
                                )}
                            </div>
                        </div>
                        <p className="mt-2 text-xs text-neutral-60">Enter the URL where this CTA should link to.</p>
                    </div>
                </EditorContainer>
            </div>
        );
    },
    save: commonSave,
});
