@props(['type' => 'default', 'variant' => 'primary', 'href' => '#', 'size' => 'medium', 'icon' => null])

@php
    $variantClasses = match ($variant) {
        'primary' => 'flex items-center justify-center font-light bg-transparent text-white 
        border border-transparent relative z-[1] before:absolute before:-z-[1] before:rounded-full
        before:-m-px before:w-[calc(100%_+_2px)] before:h-[calc(100%_+_2px)] before:top-0 before:left-0
        before:bg-[linear-gradient(80.45deg,_#000000_20%,_#003954_80%)] before:bg-[length:140%]
        before:transition-all before:duration-500 hover:before:bg-right',
        'secondary' => 'transition-all duration-500 rounded-full flex items-center justify-center font-light bg-transparent text-neutral-100 border border-neutral-100',
        'tertiary' => 'transition-all duration-500 rounded-full flex items-center justify-center font-light bg-transparent text-white border border-white',
        default => 'bg-black text-white font-semibold py-2 px-4 rounded',
    };

    $sizeClasses = match ($size) {
        'large' => 'text-buttonLarge pt-[21px] pb-[16px] px-[27px] lg:text-buttonLargeDesktop',
        'medium' => 'text-buttonMedium pt-[18px] pb-[15px] px-[23px] lg:text-buttonMediumDesktop',
        'small' => 'text-buttonSmall pt-[16px] pb-[12px] px-[19px] lg:text-buttonSmallDesktop',
        'extra-small' => 'text-buttonExtraSmall pt-[12px] pb-[9px] px-[15px] lg:text-buttonExtraSmallDesktop',
        default => '',
    };

    $iconClassMap = [
        'link' => [
            'base' => 'fa-light fa-arrow-up-right',
            'size' => [
                'large' => 'ml-[12px] -mt-[7px] relative top-px',
                'medium' => 'ml-[12px] -mt-[7px] relative top-px',
                'small' => 'ml-[8px] -mt-[7px] relative top-px',
                'extra-small' => 'ml-[6px] -mt-[7px] relative top-0.5',
            ],
        ],
        'down' => [
            'base' => 'fa-light fa-arrow-down',
            'size' => [
                'large' => 'ml-[12px] -mt-[7px] relative top-px',
                'medium' => 'ml-[12px] -mt-[7px] relative top-px',
                'small' => 'ml-[8px] -mt-[7px] relative top-px',
                'extra-small' => 'ml-[6px] -mt-[7px] relative top-0.5',
            ],
        ],
        'loading' => [
            'base' => 'fa-solid fa-spinner',
            'size' => [
                'large' => 'ml-[12px] -mt-[7px] relative top-px',
                'medium' => 'ml-[12px] -mt-[7px] relative top-px',
                'small' => 'ml-[8px] -mt-[7px] relative top-px',
                'extra-small' => 'ml-[6px] -mt-[7px] relative top-0.5',
            ],
        ],
    ];

    $iconHtml = '';
    if ($icon && isset($iconClassMap[$icon])) {
        $iconBaseClass = $iconClassMap[$icon]['base'];
        $iconSizeClass = $iconClassMap[$icon]['size'][$size] ?? '';
        $iconHtml = "<i class=\"{$iconBaseClass} {$iconSizeClass}\"></i>";
    }

    $additionalClasses = $icon === 'loading' ? 'loading-button' : '';
    $classes = "{$variantClasses} {$sizeClasses} {$additionalClasses}";
@endphp

@if($type === 'submit')
    <button type="submit" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }} {!! $iconHtml !!}
    </button>
@elseif($type === 'button')
    <button type="button" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }} {!! $iconHtml !!}
    </button>
@else
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }} {!! $iconHtml !!}
    </a>
@endif
