@php
$colorMap = [
    'blue' => '!text-[#33A5DC]',
    'green' => '!text-[#538564]',
    'peach' => '!text-[#E66953]',
    'pink' => '!text-[#855384]',
];

$totalItems = count(is_array($attributes['items'] ?? null) ? $attributes['items'] : []) + 1;
$leftCount = ceil($totalItems / 2) - 1;
$items = is_array($attributes['items'] ?? null) ? $attributes['items'] : [];
$leftColumn = array_slice($items, 0, $leftCount);
$rightColumn = array_slice($items, $leftCount);

array_unshift($leftColumn, [
    'type' => 'title',
    'subtitle' => $attributes['subtitle'] ?? '',
    'title' => $attributes['title'] ?? '',
]);

$taglineColorClass = array_key_exists($attributes['taglineColor'] ?? '', $colorMap) ? $colorMap[$attributes['taglineColor']] : '';
@endphp

<div class="cta-section flex justify-center items-center">
    <div class="max-w-full px-5 py-16 lg:max-w-[904px]">
        <div class="flex flex-col justify-between gap-6 mb-6 lg:flex-row">
            <div class="flex flex-col justify-end gap-6">
                @foreach($leftColumn as $item)
                    @if(isset($item['type']) && $item['type'] === 'title')
                        <div class="flex flex-col mb-4">
                            <x-title variation="tagline" class="{{ $taglineColorClass }} uppercase font-medium mb-3">
                                {!! $item['subtitle'] !!}
                            </x-title>
                            <x-title variation="large">{!! $item['title'] !!}</x-title>
                        </div>
                    @else
                        <div class="flex flex-col border border-[#E8EBF3] rounded-3xl">
                            <img src="{{ $item['image'] }}" class="w-full h-auto object-cover rounded-3xl mb-4">
                            <div class="p-8 pt-3">
                                <x-title variation="small" class="mb-2 pr-2">{!! $item['title'] ?? '' !!}</x-title>
                                <p class="text-pretty">{!! $item['description'] ?? '' !!}</p>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>

            <div class="flex flex-col justify-end gap-6">
                @foreach($rightColumn as $item)
                    <div class="flex flex-col border border-[#E8EBF3] rounded-3xl">
                        <img src="{{ $item['image'] }}" class="w-full h-auto object-cover rounded-3xl mb-4">
                        <div class="p-8 pt-3">
                            <x-title variation="small" class="mb-2 pr-2">{!! $item['title'] ?? '' !!}</x-title>
                            <p class="text-pretty">{!! $item['description'] ?? '' !!}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="blue-dark-bg p-8 rounded-3xl flex flex-col justify-between lg:flex-row lg:items-center">
            <span class="text-bodyLarge mb-4 block max-w-56 lg:mb-0 lg:text-bodyLargeDesktop">{!! $attributes['ctaTitle'] ?? '' !!}</span>
            <form id="download-form" data-file-url="{{ $attributes['ctaFile'] ?? '' }}" class="flex gap-2 items-stretch">
                <input class="w-full pl-5 pt-0.5 rounded-xl border border-[#CFD5E3] lg:min-w-64" type="email" name="email" placeholder="{!! $attributes['inputPlaceholder'] !!}" />
                <x-button type="submit" size="small" variant="primary">Download</x-button>
            </form>
        </div>
    </div>
</div>
